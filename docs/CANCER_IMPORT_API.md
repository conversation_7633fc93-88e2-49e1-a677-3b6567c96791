# Cancer Data Import API Documentation

## Overview
ระบบ Import ข้อมูลโรคมะเร็งที่ปรับปรุงใหม่ รองรับการตรวจสอบข้อมูลก่อนนำเข้า และส่งออกไฟล์ Excel สำหรับข้อมูลที่ไม่ผ่านการตรวจสอบ

## Features
- ✅ การตรวจสอบข้อมูลแบบ Real-time validation
- ✅ การประมวลผลแบบ Queue (Background processing)
- ✅ ส่งออกไฟล์ Excel สำหรับข้อมูลที่ไม่ผ่านการตรวจสอบ
- ✅ การติดตามสถานะการ Import
- ✅ การดาวน์โหลดไฟล์ Error Report

## API Endpoints

### 1. Import Cancer Data
**POST** `/api/import/cancer`

**Headers:**
```
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**Parameters:**
- `file` (required): ไฟล์ Excel (.xlsx, .xls)

**Response:**
```json
{
    "status": true,
    "message": "เริ่มการนำเข้าข้อมูลแล้ว กรุณารอสักครู่",
    "import_id": 123
}
```

### 2. Get Import Status
**GET** `/api/import/status/{id}`

**Response:**
```json
{
    "status": true,
    "data": {
        "id": 123,
        "detail": "นำเข้าข้อมูลโรคมะเร็ง",
        "path": "imports/20250718/1642123456-cancer_data.xlsx",
        "total": 100,
        "ok": 85,
        "fail": 15,
        "error_file": "imports/errors/20250718/cancer_import_errors_20250718164523.xlsx",
        "created_at": "2025-07-18 16:42:34",
        "finished_at": "2025-07-18 16:45:23"
    }
}
```

### 3. Get All Import Data
**GET** `/api/import/data`

**Response:**
```json
{
    "status": true,
    "data": [
        {
            "id": 123,
            "detail": "นำเข้าข้อมูลโรคมะเร็ง",
            "total": 100,
            "ok": 85,
            "fail": 15,
            "created_at": "2025-07-18 16:42:34",
            "finished_at": "2025-07-18 16:45:23"
        }
    ]
}
```

### 4. Download Error File
**GET** `/api/import/download-error/{id}`

**Response:** 
ไฟล์ Excel ที่มีข้อมูลที่ไม่ผ่านการตรวจสอบ

### 5. Delete Import Data
**POST** `/api/import/delete/{id}`

**Response:**
```json
{
    "status": true,
    "message": "ลบข้อมูลสำเร็จ"
}
```

## Excel File Format

### Required Columns (ตามลำดับ):
1. **hospital_code** - รหัสโรงพยาบาล
2. **hn_no** - หมายเลข HN
3. **title_code** - รหัสคำนำหน้า
4. **name** - ชื่อ
5. **last_name** - นามสกุล
6. **cid** - เลขบัตรประชาชน (13 หลัก)
7. **birth_date** - วันเกิด (รูปแบบ d/m/Y)
8. **sex_code** - รหัสเพศ
9. **nationality_code** - รหัสสัญชาติ
10. **death_date** - วันที่เสียชีวิต (รูปแบบ d/m/Y)
11. **deathcause_code** - รหัสสาเหตุการเสียชีวิต
12. **address_no** - บ้านเลขที่
13. **address_moo** - หมู่
14. **area_code** - รหัสพื้นที่
15. **address_zipcode** - รหัสไปรษณีย์
16. **permanent_address_no** - บ้านเลขที่ (ที่อยู่ถาวร)
17. **permanent_address_moo** - หมู่ (ที่อยู่ถาวร)
18. **permanent_area_code** - รหัสพื้นที่ (ที่อยู่ถาวร)
19. **permanent_address_zipcode** - รหัสไปรษณีย์ (ที่อยู่ถาวร)
20. **telephone_1** - เบอร์โทรศัพท์ 1
21. **telephone_2** - เบอร์โทรศัพท์ 2
22. **first_entrance_date** - วันที่เข้ารับการรักษาครั้งแรก (รูปแบบ d/m/Y)
23. **entrance_date** - วันที่เข้ารับการรักษา (รูปแบบ d/m/Y)
24. **finance_support_code** - รหัสสิทธิการรักษา
25. **diagnosis_date** - วันที่วินิจฉัย (รูปแบบ d/m/Y)
26. **diagnosis_code** - รหัสการวินิจฉัย
27. **diagnosis_out** - การวินิจฉัยนอกสถานที่ (0 หรือ 1)
28. **excision_in_cut_date** - วันที่ตัดชิ้นเนื้อ (รูปแบบ d/m/Y)
29. **excision_in_read_date** - วันที่อ่านผลชิ้นเนื้อ (รูปแบบ d/m/Y)
30. **topo_code** - รหัส Topography
31. **recurrent** - การกลับเป็นซ้ำ (0 หรือ 1)
32. **recurrent_date** - วันที่กลับเป็นซ้ำ (รูปแบบ d/m/Y)
33. **morphology_code** - รหัส Morphology
34. **behaviour_code** - รหัส Behaviour
35. **grade_code** - รหัส Grade
36. **t_code** - รหัส T
37. **n_code** - รหัส N
38. **m_code** - รหัส M
39. **tnm_date** - วันที่ TNM (รูปแบบ d/m/Y)
40. **stage_code** - รหัส Stage
41. **extension_code** - รหัส Extension
42. **icd10_code** - รหัส ICD10
43-56. **met_1 ถึง met_7** และ **met_1_date ถึง met_7_date** - ข้อมูลการแพร่กระจาย
57. **met_7_other** - การแพร่กระจายอื่นๆ
58. **clinical_summary** - สรุปทางคลินิก

### Treatment Data (คอลัมน์ 59-118):
สำหรับข้อมูลการรักษา 10 รายการ แต่ละรายการมี 6 คอลัมน์:
- **treatment_code** - รหัสการรักษา
- **treatment_date** - วันที่เริ่มรักษา (รูปแบบ d/m/Y)
- **treatment_date_end** - วันที่สิ้นสุดการรักษา (รูปแบบ d/m/Y)
- **note** - หมายเหตุ
- **none_protocol** - ไม่ตาม Protocol (0 หรือ 1)
- **none_protocol_note** - หมายเหตุไม่ตาม Protocol

## Validation Rules

### ข้อมูลที่จำเป็น (Required):
- เลขบัตรประชาชน (13 หลัก)
- HN
- ชื่อ-นามสกุล
- วันเกิด
- เพศ
- วันที่เข้ารับการรักษา
- วันที่วินิจฉัย
- รหัส ICD10

### การตรวจสอบรหัสต่างๆ:
- รหัสโรงพยาบาล ต้องมีในตาราง `bd_hospital`
- รหัสเพศ ต้องมีในตาราง `bd_sex`
- รหัส Morphology ต้องมีในตาราง `bd_mor`
- รหัส ICD10 ต้องมีในตาราง `bd_icd10`
- รหัสการรักษา ต้องมีในตาราง `bd_treatment`

## Error Handling

### ไฟล์ Error Report จะประกอบด้วย:
- แถวที่เกิดข้อผิดพลาด
- รายละเอียดข้อผิดพลาด
- ข้อมูลสำคัญจากแถวนั้น (HN, ชื่อ, เลขบัตรประชาชน, etc.)

### สีในไฟล์ Excel:
- **Header**: พื้นหลังสีแดง ตัวอักษรสีขาว
- **แถว Error**: พื้นหลังสีแดงอ่อน

## Queue Configuration

ระบบใช้ Laravel Queue สำหรับประมวลผลข้อมูลในพื้นหลัง

### การตั้งค่า Queue:
```bash
# เริ่ม Queue Worker
php artisan queue:work

# หรือใช้ Supervisor สำหรับ Production
```

### การตรวจสอบ Queue:
```bash
# ดู Jobs ที่รอประมวลผล
php artisan queue:monitor

# ดู Failed Jobs
php artisan queue:failed
```

## Usage Example

### JavaScript/Ajax:
```javascript
// Upload file
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('/api/import/cancer', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.status) {
        // เริ่มตรวจสอบสถานะ
        checkImportStatus(data.import_id);
    }
});

// Check status
function checkImportStatus(importId) {
    fetch(`/api/import/status/${importId}`, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.data.finished_at) {
            // การ import เสร็จสิ้น
            console.log(`สำเร็จ: ${data.data.ok}, ล้มเหลว: ${data.data.fail}`);
            
            if (data.data.error_file) {
                // มีไฟล์ error ให้ดาวน์โหลด
                window.open(`/api/import/download-error/${importId}`);
            }
        } else {
            // ยังไม่เสร็จ ตรวจสอบอีกครั้งใน 5 วินาที
            setTimeout(() => checkImportStatus(importId), 5000);
        }
    });
}
```

## Notes
- ไฟล์ Excel ต้องเริ่มข้อมูลจากแถวที่ 3 (แถว 1-2 เป็น header)
- รองรับไฟล์ .xlsx และ .xls เท่านั้น
- ข้อมูลวันที่ต้องเป็นรูปแบบ d/m/Y (เช่น 15/07/2025)
- ระบบจะสร้างผู้ป่วยใหม่หากไม่พบในระบบ
- ข้อมูลการรักษาจะถูกบันทึกเฉพาะที่มี treatment_code
