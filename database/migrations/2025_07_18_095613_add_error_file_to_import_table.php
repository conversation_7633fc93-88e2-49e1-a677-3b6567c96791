<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('import_table', function (Blueprint $table) {
            $table->string('error_file')->nullable()->after('path');
            $table->text('error_message')->nullable()->after('error_file');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('import_table', function (Blueprint $table) {
            $table->dropColumn(['error_file', 'error_message']);
        });
    }
};
