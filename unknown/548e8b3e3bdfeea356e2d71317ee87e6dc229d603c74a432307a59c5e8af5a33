<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithTitle;

class PatientsAddressExport implements FromArray, WithHeadings, WithStrictNullComparison, WithColumnWidths, WithTitle
{
    protected $input;

    // Constructor to pass parameters
    public function __construct($input)
    {
        $this->input = $input;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function array(): array
    {
        $start_date = $this->input->query('date_start');
        $end_date = $this->input->query('date_end');
        $gender_code = $this->input->query('gender_code');
        $area_level_id = $this->input->query('area_level_id');
        $area_id = $this->input->query('area_id');
        $behaviour_code_start = $this->input->query('behaviour_code_start');
        $behaviour_code_end = $this->input->query('behaviour_code_end');
        $stage_code_start = $this->input->query('stage_code_start');
        $stage_code_end = $this->input->query('stage_code_end');
        $morpho_start = $this->input->query('morpho_start');
        $morpho_end = $this->input->query('morpho_end');
        $treatment_code = $this->input->query('treatment_code');
        $age_start = $this->input->query('age_start');
        $age_end = $this->input->query('age_end');
        $recurent = $this->input->query('recurent');
        $deathcase = $this->input->query('deathcase');
        $icd10_group_id = array_filter($this->input->query('icd10_group_id', []));

        if (!$start_date || !$end_date) {
            return response()->json(['error' => 'กรุณาใส่วันที่เริ่มและวันที่สิ้นสุด'], 400);
        }

        $results = [];

        switch ($area_level_id) {
            case 4:
                $results = DB::table('data_patient as p')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('concat(v.province_name_th) as province'),
                        DB::raw('concat(d.district_name_th) as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(s.id) AS total_count')
                    )
                    ->leftJoin('data_cancer_summary as s', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.permanent_address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.permanent_address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    ->where('s.behaviour_code', 3)
                    ->where('s.hospital_code', '=', $area_id)
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('s.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            default:
                return response()->json(['error' => 'Invalid area level'], 400);
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        $exportData = $results->map(function ($item) use ($total_count) {
            return [
                'เขตสุขภาพ' => $item->health_region,
                'จังหวัด' => $item->province,
                'อำเภอ' => $item->district,
                'เพศชาย(ราย)' => $item->male_count ?: '0',
                'เพศชาย(%)' => $total_count > 0 ? number_format(($item->male_count * 100.0 / $total_count), 10) : '0',
                'เพศหญิง(ราย)' => $item->female_count ?: '0',
                'เพศหญิง(%)' => $total_count > 0 ? number_format(($item->female_count * 100.0 / $total_count), 10) : '0',
                'ผู้ป่วยทั้งหมด(ราย)' => $item->total_count,
                'ผู้ป่วยทั้งหมด(%)' => $total_count > 0 ? number_format(($item->total_count * 100.0 / $total_count), 10) : '0',
            ];
        })->toArray();

        //แถวรวมทั้งหมด
        $exportData[] = [
            'เขตสุขภาพ' => '',
            'จังหวัด' => '',
            'อำเภอ' => 'รวม',
            'เพศชาย(ราย)' => $total_male,
            'เพศชาย(%)' => $total_count > 0 ? number_format(($total_male * 100.0 / $total_count), 10) : 0,
            'เพศหญิง(ราย)' => $total_female,
            'เพศหญิง(%)' => $total_count > 0 ? number_format(($total_female * 100.0 / $total_count), 10) : 0,
            'ผู้ป่วยทั้งหมด(ราย)' => $total_count,
            'ผู้ป่วยทั้งหมด(%)' => $total_count > 0 ? number_format(100.0, 2) : '0',
        ];

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'เขตสุขภาพ',
            'จังหวัด',
            'อำเภอ',
            'เพศชาย(ราย)',
            'เพศชาย(%)',
            'เพศหญิง(ราย)',
            'เพศหญิง(%)',
            'ผู้ป่วยทั้งหมด(ราย)',
            'ผู้ป่วยทั้งหมด(%)',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 20,
            'F' => 20,
            'G' => 20,
            'H' => 20,
            'I' => 20,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'RP07';
    }
}
