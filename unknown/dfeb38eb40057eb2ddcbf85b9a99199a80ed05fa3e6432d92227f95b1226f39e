<?php

namespace App\Exports;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithTitle;

class number_type_treatment_Export implements FromArray, WithHeadings, WithColumnWidths, WithStrictNullComparison, WithTitle
{
    protected $input;

    public function __construct(Request $input)
    {
        $this->input = $input;
    }

    public function array(): array
    {
        $start_date = $this->input->query('date_start');
        $end_date = $this->input->query('date_end');
        $gender_code = $this->input->query('gender_code');
        $area_id = $this->input->query('area_id');
        $behaviour_code_start = $this->input->query('behaviour_code_start');
        $behaviour_code_end = $this->input->query('behaviour_code_end');
        $stage_code_start = $this->input->query('stage_code_start');
        $stage_code_end = $this->input->query('stage_code_end');
        $morpho_start = $this->input->query('morpho_start');
        $morpho_end = $this->input->query('morpho_end');
        $treatment_code = $this->input->query('treatment_code');
        $age_start = $this->input->query('age_start');
        $age_end = $this->input->query('age_end');
        $recurent = $this->input->query('recurent');
        $deathcase = $this->input->query('deathcase');
        $icd10_group_id = array_filter($this->input->query('icd10_group_id', []));

        // Fetch treatment categories
        $treatmentCategories = DB::table('bd_treatment_category')
            ->select(DB::raw("code AS treatment_code"), DB::raw("name AS treatment_type"))
            ->get();

        // Fetch results
        $results = DB::table('data_patient')
            ->select(
                DB::raw("group_t.name AS treatment_type"),
                DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                DB::raw('COUNT(data_cancer_summary.id) AS total_count')
            )
            ->join('data_cancer_summary', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
            ->join('data_cancer_summary_treatment', function ($join) {
                $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                    ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
            })
            ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
            ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
            ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
            ->where('data_cancer_summary.behaviour_code', 3)
            ->when($area_id, fn($query) => $query->where('data_patient.hospital_code', $area_id))
            ->when($gender_code, fn($query) => $query->where('data_patient.sex_code', $gender_code))
            ->when(isset($behaviour_code_start) && isset($behaviour_code_end), fn($query) => $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]))
            ->when(isset($stage_code_start) && isset($stage_code_end), fn($query) => $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]))
            ->when($morpho_start && $morpho_end, fn($query) => $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]))
            ->when($treatment_code, fn($query) => $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code))
            ->when($age_start && $age_end, fn($query) => $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]))
            ->when($recurent === 'Y', fn($query) => $query->where('data_cancer_summary.recurrent', 1))
            ->when($deathcase === 'Y', fn($query) => $query->whereNotNull('data_patient.death_date'))
            ->when($icd10_group_id, fn($query) => $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id))
            ->groupBy('group_t.code', 'group_t.name')
            ->orderBy('group_t.code', 'ASC')
            ->get();

        // Totals
        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        // Map results and calculate percentages
        $exportData = $treatmentCategories->map(function ($category) use ($results, $total_count) {
            $matchingResult = $results->firstWhere('treatment_type', $category->treatment_type);

            return [
                'treatment_type' => $category->treatment_type,
                'male_count' => $matchingResult->male_count ?? 0,
                'male_percentage' => $total_count > 0 ? number_format((($matchingResult->male_count ?? 0) * 100) / $total_count, 10) : 0,
                'female_count' => $matchingResult->female_count ?? 0,
                'female_percentage' => $total_count > 0 ? number_format((($matchingResult->female_count ?? 0) * 100) / $total_count, 10) : 0,
                'total_count' => $matchingResult->total_count ?? 0,
                'total_percentage' => $total_count > 0 ? number_format((($matchingResult->total_count ?? 0) * 100) / $total_count, 10) : 0,
            ];
        })->toArray();

        // Add totals row
        $exportData[] = [
            'treatment_type' => 'รวม',
            'male_count' => $total_male,
            'male_percentage' => $total_count > 0 ? number_format(($total_male * 100) / $total_count, 10) : 0,
            'female_count' => $total_female,
            'female_percentage' => $total_count > 0 ? number_format(($total_female * 100) / $total_count, 10) : 0,
            'total_count' => $total_count,
            'total_percentage' => $total_count > 0 ? number_format(100, 2) : 0,
        ];

        return $exportData;
    }


    public function headings(): array
    {
        return [
            'ประเภทการรักษา',
            'ชาย (จำนวน)',
            'ชาย (%)',
            'หญิง (จำนวน)',
            'หญิง (%)',
            'รวม (จำนวน)',
            'รวม (%)',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 30,
            'B' => 15,
            'C' => 15,
            'D' => 15,
            'E' => 15,
            'F' => 15,
            'G' => 15,
        ];
    }

    public function title(): string
    {
        return 'Number Type Treatment';
    }
}
