<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithTitle;

class Export_Types_of_cancer_Excel implements FromArray, WithHeadings, WithColumnWidths, WithStrictNullComparison, WithTitle
{
    protected $input;

    public function __construct($input)
    {
        $this->input = $input;
    }

    public function array(): array
    {
        $start_date = $this->input->query('date_start');
        $end_date = $this->input->query('date_end');
        $gender_code = $this->input->query('gender_code');
        $area_id = $this->input->query('area_id');
        $area_level_id = $this->input->query('area_level_id');
        $behaviour_code_start = $this->input->query('behaviour_code_start');
        $behaviour_code_end = $this->input->query('behaviour_code_end');
        $stage_code_start = $this->input->query('stage_code_start');
        $stage_code_end = $this->input->query('stage_code_end');
        $morpho_start = $this->input->query('morpho_start');
        $morpho_end = $this->input->query('morpho_end');
        $treatment_code = $this->input->query('treatment_code');
        $age_start = $this->input->query('age_start');
        $age_end = $this->input->query('age_end');
        $recurent = $this->input->query('recurent');
        $deathcase = $this->input->query('deathcase');
        $icd10_group_id = array_filter($this->input->query('icd10_group_id', []));

        switch ($area_level_id) {
            case 1:
                # code...
                break;

            case 2:
                # code...
                break;

            case 3:
                # code...
                break;

            case 4:

                $siteGroups = DB::table('bd_sitegroup')
                    ->select('group_text as GroupName', 'group_desc as GroupDescription')
                    ->get();

                $data = DB::table('data_patient')
                    ->select(
                        'bd_sitegroup.group_text as GroupName',
                        'bd_sitegroup.group_desc as GroupDescription',
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age IS NULL OR data_cancer_summary.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END), 0) as '0'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END), 0) as '5'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END), 0) as '10'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END), 0) as '15'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END), 0) as '20'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END), 0) as '25'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END), 0) as '30'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END), 0) as '35'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END), 0) as '40'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END), 0) as '45'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END), 0) as '50'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END), 0) as '55'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END), 0) as '60'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END), 0) as '65'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END), 0) as '70'"),
                        DB::raw("COALESCE(SUM(CASE WHEN data_cancer_summary.age >= 75 THEN 1 ELSE 0 END), 0) as '75+'"),
                        DB::raw('COALESCE(COUNT(DISTINCT data_cancer_summary.patient_id), 0) as TotalPatients')
                    )
                    ->leftJoin('data_cancer_summary', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_sitegroup', 'data_cancer_summary.icd10_group_id', '=', 'bd_sitegroup.group_id')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    ->where('data_patient.sex_code', 2)
                    // ->when($gender_code, function ($query) use ($gender_code) {
                    //     $query->where('data_patient.sex_code', $gender_code);
                    // })
                    ->when($area_id, function ($query) use ($area_id) {
                        $query->where('data_cancer_summary.hospital_code', $area_id);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        $query->whereExists(function ($subQuery) use ($treatment_code) {
                            $subQuery->select(DB::raw(1))
                                ->from('data_cancer_summary_treatment as t')
                                ->whereRaw('data_cancer_summary.patient_id = t.patient_id')
                                ->whereRaw('data_cancer_summary.icd10_code = t.icd10_code')
                                ->where('t.treatment_type_id', $treatment_code);
                        });
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_sitegroup.group_text', 'bd_sitegroup.group_desc')
                    ->get();
                break;
        }
        $dataArray = collect($siteGroups)->map(function ($group) use ($data) {
            $existingData = $data->firstWhere('GroupName', $group->GroupName);

            return [
                'GroupName' => $group->GroupName,
                'GroupDescription' => $group->GroupDescription,
                '0' => $existingData->{"0"} ?? 0,
                '5' => $existingData->{"5"} ?? 0,
                '10' => $existingData->{"10"} ?? 0,
                '15' => $existingData->{"15"} ?? 0,
                '20' => $existingData->{"20"} ?? 0,
                '25' => $existingData->{"25"} ?? 0,
                '30' => $existingData->{"30"} ?? 0,
                '35' => $existingData->{"35"} ?? 0,
                '40' => $existingData->{"40"} ?? 0,
                '45' => $existingData->{"45"} ?? 0,
                '50' => $existingData->{"50"} ?? 0,
                '55' => $existingData->{"55"} ?? 0,
                '60' => $existingData->{"60"} ?? 0,
                '65' => $existingData->{"65"} ?? 0,
                '70' => $existingData->{"70"} ?? 0,
                '75+' => $existingData->{"75+"} ?? 0,
                'TotalPatients' => $existingData->TotalPatients ?? 0,
            ];
        })->toArray();

        $totalRow = array_reduce($dataArray, function ($carry, $item) {
            foreach ($item as $key => $value) {
                if ($key !== 'GroupName' && $key !== 'GroupDescription') {
                    $carry[$key] = ($carry[$key] ?? 0) + $value;
                } else {
                    $carry[$key] = $key === 'GroupDescription' ? 'รวม' : '';
                }
            }
            return $carry;
        }, []);

        $dataArray[] = $totalRow;

        return $dataArray;
    }

    public function headings(): array
    {
        return [
            'รหัสกลุ่มโรค',
            'กลุ่มโรค',
            '0+',
            '5+',
            '10+',
            '15+',
            '20+',
            '25+',
            '30+',
            '35+',
            '40+',
            '45+',
            '50+',
            '55+',
            '60+',
            '65+',
            '70+',
            '75+',
            'รวม',
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15,
            'B' => 30
        ];
    }

    public function title(): string
    {
        return 'Types of Cancer';
    }
}
