<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class CancerImport implements WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    // public function collection(Collection $rows)
    // {
    //     foreach ($rows as $row) {
    //         $data = $this->getValPatient($row);

    //         $val_patient['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
    //         $val_patient['updated_by'] = $user->id;
    //         $val_patient['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
    //         $val_patient['created_by'] = $user->id;
    //         $patient_id = DB::table('data_patient')->insertGetId($val_patient);

    //         $c = CancerController::getValCancer($patient_id, $input, $mor_key);
    //     }
    // }

    // private function getValPatient($input)
    // {
    //     $val = [
    //         'hospital_code'                     => $input['hosp'],
    //         'hn_no'                             => $input['hn'],
    //         'title_code'                        => $input['title'],
    //         'name'                              => $input['name'],
    //         'last_name'                         => $input['lastn'],
    //         'cid'                               => str_replace("-", "", $input['id']),
    //         'birth_date'                        => getDateTimeTHtoEN($input['birthd']),
    //         'sex_code'                          => $input['sex'],
    //         'nationality_code'                  => $input['nat'],
    //         'death_date'                        => isset($input['diedate']) && $input['diedate'] != '' ? getDateTimeTHtoEN($input['diedate']) : null,
    //         'deathcause_code'                   => isset($input['cause']) ? $input['cause'] : null,
    //         // 'address_no'                        => isset($input['address_no']) ? $input['address_no'] : null,
    //         // 'address_moo'                       => isset($input['address_moo']) ? $input['address_moo'] : null,
    //         // 'address_province_id'               => isset($input['address_province_id']) ? $input['address_province_id'] : null,
    //         // 'address_district_id'               => isset($input['address_district_id']) ? $input['address_district_id'] : null,
    //         // 'address_sub_district_id'           => isset($input['address_sub_district_id']) ? $input['address_sub_district_id'] : null,
    //         // 'address_zipcode'                   => isset($input['address_zipcode']) ? $input['address_zipcode'] : null,
    //         'area_code'                         => isset($input['addcode']) ? $input['addcode'] : null,
    //         // 'permanent_address_no'              => isset($input['permanent_address_no']) ? $input['permanent_address_no'] : null,
    //         // 'permanent_address_moo'             => isset($input['permanent_address_moo']) ? $input['permanent_address_moo'] : null,
    //         // 'permanent_address_province_id'     => isset($input['permanent_address_province_id']) ? $input['permanent_address_province_id'] : null,
    //         // 'permanent_address_district_id'     => isset($input['permanent_address_district_id']) ? $input['permanent_address_district_id'] : null,
    //         // 'permanent_address_sub_district_id' => isset($input['permanent_address_sub_district_id']) ? $input['permanent_address_sub_district_id'] : null,
    //         // 'permanent_address_zipcode'         => isset($input['permanent_address_zipcode']) ? $input['permanent_address_zipcode'] : null,
    //         'permanent_area_code'               => isset($input['addcode2']) ? $input['addcode2'] : null,
    //         'email'                             => isset($input['email']) ? $input['email'] : null,
    //         'telephone_1'                       => isset($input['tel']) ? $input['tel'] : null,
    //         'telephone_2'                       => isset($input['tel2']) ? $input['tel2'] : null,
    //     ];

    //     return $val;
    // }
}
