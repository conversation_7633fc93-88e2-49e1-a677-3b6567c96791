<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithStartRow;

class PatientDeadImport implements ToCollection, WithStartRow
{
    public $found = 0;
    public $notFound = 0;
    public $failed = 0;

    public function collection(Collection $rows)
    {
        foreach ($rows as $index => $row) {
            $mapped = [
                'name'              => $row[0],
                'last_name'         => $row[1],
                'cid'               => $row[2],
                // 'birth_date'        => ($row[5] - 543) . '-' . $row[4] . '-' . $row[3],
                'area_code'         => substr($row[6], 0, 6),
                'sex_code'          => $row[8],
                'death_date'        => (intval($row[15]) - 543) . '-' . $row[14] . '-' . $row[13],
            ];

            // $validator = Validator::make($mapped, $this->rules(), $this->rulesMessage());
            $validator = Validator::make($mapped, $this->rules());

            if ($validator->fails()) {
                $this->failed++;
                continue;
                // $this->errors[] = [
                //     'row'    => $index + 2,
                //     'errors' => implode(', ', $validator->errors()->all()),
                //     'data'   => $mapped,
                // ];
            } else {
                // $this->validatedRows[] = $mapped;
            }

            //check duplicate
            $exists = DB::table('data_patient')->where(DB::raw("'REPLACE(cid, '-', '')'"), str_replace('-', '', $mapped['cid']))->exists();
            if ($exists) {
                $this->found++;
                DB::table('data_patient')->where(DB::raw("'REPLACE(cid, '-', '')'"), str_replace('-', '', $mapped['cid']))->update([
                    'death_date'        => $mapped['death_date'],
                    'deathcause_code'   => 1,
                ]);
            } else {
                $this->notFound++;
                DB::table('dco')->insert([
                    'cid'               => $mapped['cid'],
                    'death_date'        => $mapped['death_date'],
                ]);
            }
        }

        Log::info('Found:' . $this->found . '|NotFound:' . $this->notFound . '|Failed:' . $this->failed);

        
    }

    public function startRow(): int
    {
        return 2;
    }

    public function rules(): array
    {
        return [
            'name'              => 'required',
            'last_name'         => 'required',
            'cid'               => 'required|size:13',
            // 'birth_date'        => 'required|date',
            'area_code'         => 'required|exists:bd_area,code',
            'sex_code'          => 'required|in:1,2',
            'death_date'        => 'required|date',
        ];
    }

    // public function rulesMessage(): array
    // {
    //     return [
    //         'name.required'             => '|name|',
    //         'cid.required'              => '|cid|',
    //         'cid.regex'                 => '|cid|',
    //         'title_name.required'       => '|title_name|',
    //         'title_name.exists'         => '|title_name|',
    //         'last_name.required'        => '|last_name|',
    //         'sex_name.required'         => '|sex_name|',
    //         'sex_name.in'               => '|sex_name|',
    //         'death_date.required'       => '|death_date|',
    //         'death_date.date'           => '|death_date|',
    //         'deathcause_name.required'  => '|deathcause_name|',
    //         'deathcause_name.in'        => '|deathcause_name|',
    //     ];
    // }

    // public function chunkSize(): int
    // {
    //     return 100;
    // }
}
