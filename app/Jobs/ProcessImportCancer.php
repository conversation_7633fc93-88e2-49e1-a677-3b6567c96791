<?php

namespace App\Jobs;

use App\Http\Controllers\CancerController;
use App\Http\Controllers\ImportController;
use App\Imports\CancerDataImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ProcessImportCancer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $import_id;
    protected $file_path;
    protected $user_id;

    /**
     * Create a new job instance.
     */
    public function __construct($import_id, $file_path, $user_id)
    {
        $this->import_id = $import_id;
        $this->file_path = $file_path;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Processing import cancer job');

        try {
            // อ่านข้อมูลจากไฟล์
            $rows = Excel::toArray(new CancerDataImport, $this->file_path, 'public');

            $errors = [];
            $success = [];
            $totalRows = count($rows[0]);

            foreach ($rows[0] as $key => $row) {
                $rowNumber = $key + 3; // เริ่มจากแถวที่ 3 (header อยู่แถว 1-2)

                try {
                    $data = $this->mapRowData($row);

                    Log::info('Row ' . $rowNumber . ': ' . json_encode($data));

                    // ตรวจสอบข้อมูลพื้นฐาน
                    $validationErrors = $this->validateRowData($data, $rowNumber);

                    if (!empty($validationErrors)) {
                        $errors[] = [
                            'row' => $rowNumber,
                            'data' => $row,
                            'errors' => $validationErrors
                        ];
                        continue;
                    }

                    // ประมวลผลข้อมูลที่ผ่านการตรวจสอบ
                    $this->processValidData($data);

                    $success[] = [
                        'row' => $rowNumber,
                        'data' => $data
                    ];
                } catch (\Exception $e) {
                    Log::error('Error processing row ' . $rowNumber . ': ' . $e->getMessage());
                    $errors[] = [
                        'row' => $rowNumber,
                        'data' => $row,
                        'errors' => ['เกิดข้อผิดพลาดในการประมวลผล: ' . $e->getMessage()]
                    ];
                }
            }

            // สร้างไฟล์ Excel สำหรับข้อมูลที่ไม่ผ่าน
            $errorFilePath = null;
            if (!empty($errors)) {
                $errorFilePath = $this->createErrorExcelFile($errors);
            }

            // อัพเดทสถานะการ import
            DB::table('import_table')->where('id', $this->import_id)->update([
                'total'         => $totalRows,
                'ok'            => count($success),
                'fail'          => count($errors),
                'error_file'    => $errorFilePath,
                'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
                'finished_at'   => Carbon::now()->format('Y-m-d H:i:s'),
            ]);

            Log::info('Finished processing import cancer job');
        } catch (\Exception $e) {
            Log::error('Error in ProcessImportCancer job: ' . $e->getMessage());

            // อัพเดทสถานะเป็น error
            DB::table('import_table')->where('id', $this->import_id)->update([
                'fail'          => 1,
                'error_message' => $e->getMessage(),
                'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
                'finished_at'   => Carbon::now()->format('Y-m-d H:i:s'),
            ]);
        }
    }

    private function mapRowData($row)
    {
        $data = [
            'hospital_code' => $row[0],
            'hn_no' => $row[1],
            'title_code' => $row[2],
            'name' => $row[3],
            'last_name' => $row[4],
            'cid' => $row[5],
            'birth_date' => $row[6],
            'sex_code' => $row[7],
            'nationality_code' => $row[8],
            'death_date' => $row[9],
            'deathcause_code' => $row[10],
            'address_no' => $row[11],
            'address_moo' => $row[12],
            'area_code' => $row[13],
            'address_zipcode' => $row[14],
            'permanent_address_no' => $row[15],
            'permanent_address_moo' => $row[16],
            'permanent_area_code' => $row[17],
            'permanent_address_zipcode' => $row[18],
            'telephone_1' => $row[19],
            'telephone_2' => $row[20],
            'first_entrance_date' => $row[21],
            'entrance_date' => $row[22],
            'finance_support_code' => $row[23],
            'diagnosis_date' => $row[24],
            'diagnosis_code' => $row[25],
            'diagnosis_out' => $row[26],
            'excision_in_cut_date' => $row[27],
            'excision_in_read_date' => $row[28],
            'topo_code' => $row[29],
            'recurrent' => $row[30],
            'recurrent_date' => $row[31],
            'morphology_code' => $row[32],
            'behaviour_code' => $row[33],
            'grade_code' => $row[34],
            't_code' => $row[35],
            'n_code' => $row[36],
            'm_code' => $row[37],
            'tnm_date' => $row[38],
            'stage_code' => $row[39],
            'extension_code' => $row[40],
            'icd10_code' => $row[41],
            'met_1' => $row[42],
            'met_1_date' => $row[43],
            'met_2' => $row[44],
            'met_2_date' => $row[45],
            'met_3' => $row[46],
            'met_3_date' => $row[47],
            'met_4' => $row[48],
            'met_4_date' => $row[49],
            'met_5' => $row[50],
            'met_5_date' => $row[51],
            'met_6' => $row[52],
            'met_6_date' => $row[53],
            'met_7' => $row[54],
            'met_7_date' => $row[55],
            'met_7_other' => $row[56],
            'clinical_summary' => $row[57],
        ];

        // จัดการข้อมูลการรักษา
        $treatments = [];
        for ($i = 58; $i < 117; $i += 6) {
            if (!empty($row[$i])) {
                $treatments[] = [
                    'treatment_code' => $row[$i],
                    'treatment_date' => $row[$i + 1],
                    'treatment_date_end' => $row[$i + 2],
                    'note' => $row[$i + 3],
                    'none_protocol' => $row[$i + 4],
                    'none_protocol_note' => $row[$i + 5],
                ];
            }
        }

        $data['treatments'] = $treatments;
        return $data;
    }

    private function validateRowData($data, $rowNumber)
    {
        $errors = [];

        // ตรวจสอบข้อมูลพื้นฐาน
        if (empty($data['cid']) || !preg_match('/^\d{13}$/', $data['cid'])) {
            $errors[] = "เลขบัตรประชาชนไม่ถูกต้อง";
        }

        if (empty($data['hn_no'])) {
            $errors[] = "HN ไม่สามารถเป็นค่าว่างได้";
        }

        if (empty($data['name'])) {
            $errors[] = "ชื่อไม่สามารถเป็นค่าว่างได้";
        }

        if (empty($data['last_name'])) {
            $errors[] = "นามสกุลไม่สามารถเป็นค่าว่างได้";
        }

        // ตรวจสอบรหัสต่างๆ ในฐานข้อมูล
        if (!empty($data['hospital_code']) && !DB::table('bd_hospital')->where('code', $data['hospital_code'])->exists()) {
            $errors[] = "รหัสโรงพยาบาลไม่ถูกต้อง";
        }

        if (!empty($data['sex_code']) && !DB::table('bd_sex')->where('code', $data['sex_code'])->exists()) {
            $errors[] = "รหัสเพศไม่ถูกต้อง";
        }

        if (!empty($data['morphology_code']) && !DB::table('bd_mor')->where('key', $data['morphology_code'])->exists()) {
            $errors[] = "รหัส Morphology ไม่ถูกต้อง";
        }

        if (!empty($data['icd10_code']) && !DB::table('bd_icd10')->where('ICD10', $data['icd10_code'])->exists()) {
            $errors[] = "รหัส ICD10 ไม่ถูกต้อง";
        }

        // ตรวจสอบการรักษา
        foreach ($data['treatments'] as $index => $treatment) {
            if (!empty($treatment['treatment_code']) && !DB::table('bd_treatment')->where('code', $treatment['treatment_code'])->exists()) {
                $errors[] = "รหัสการรักษาลำดับที่ " . ($index + 1) . " ไม่ถูกต้อง";
            }
        }

        return $errors;
    }

    private function processValidData($data)
    {
        DB::beginTransaction();

        try {
            $user = DB::table('users')->find($this->user_id);

            // ค้นหาหรือสร้างผู้ป่วย
            $patient = DB::table('data_patient')
                ->where('hospital_code', $data['hospital_code'])
                ->where('hn_no', $data['hn_no'])
                ->where('cid', $data['cid'])
                ->first();

            if (!$patient) {
                $patientData = $this->getPatientValue($data, $user);
                $patient_id = DB::table('data_patient')->insertGetId($patientData);
            } else {
                $patient_id = $patient->id;
            }

            // สร้างข้อมูลโรคมะเร็ง
            $cancerData = $this->getCancerValue($data, $patient_id, $user);
            $cancer_id = DB::table('data_cancer_summary')->insertGetId($cancerData);

            // สร้างข้อมูลการรักษา
            foreach ($data['treatments'] as $treatment) {
                if (!empty($treatment['treatment_code'])) {
                    $treatmentData = $this->getTreatmentValue($treatment, $patient_id, $cancer_id, $cancerData['icd10_code']);
                    DB::table('data_cancer_summary_treatment')->insertGetId($treatmentData);
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    private function getPatientValue($data, $user)
    {
        return [
            'hospital_code'                     => $data['hospital_code'],
            'hn_no'                            => $data['hn_no'],
            'title_code'                       => $data['title_code'],
            'name'                             => $data['name'],
            'last_name'                        => $data['last_name'],
            'cid'                              => $this->convertCIDToFormatted($data['cid']),
            'birth_date'                       => $this->convertDateFormat($data['birth_date']),
            'sex_code'                         => $data['sex_code'],
            'nationality_code'                 => $data['nationality_code'],
            'death_date'                       => $this->convertDateFormat($data['death_date']),
            'deathcause_code'                  => $data['deathcause_code'],
            'address_no'                       => $data['address_no'],
            'address_moo'                      => $data['address_moo'],
            'area_code'                        => $data['area_code'],
            'address_province_id'              => isset($data['area_code']) ? substr($data['area_code'], 0, 2) : null,
            'address_district_id'              => isset($data['area_code']) ? substr($data['area_code'], 0, 4) : null,
            'address_sub_district_id'          => isset($data['area_code']) ? substr($data['area_code'], 0, 6) : null,
            'address_zipcode'                  => $data['address_zipcode'],
            'permanent_address_no'             => $data['permanent_address_no'],
            'permanent_address_moo'            => $data['permanent_address_moo'],
            'permanent_area_code'              => $data['permanent_area_code'],
            'permanent_address_province_id'    => isset($data['permanent_area_code']) ? substr($data['permanent_area_code'], 0, 2) : null,
            'permanent_address_district_id'    => isset($data['permanent_area_code']) ? substr($data['permanent_area_code'], 0, 4) : null,
            'permanent_address_sub_district_id' => isset($data['permanent_area_code']) ? substr($data['permanent_area_code'], 0, 6) : null,
            'permanent_address_zipcode'        => $data['permanent_address_zipcode'],
            'telephone_1'                      => $data['telephone_1'],
            'telephone_2'                      => $data['telephone_2'],
            'updated_at'                       => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_by'                       => $user->id,
            'created_at'                       => Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'                       => $user->id,
        ];
    }

    private function getCancerValue($data, $patient_id, $user)
    {
        $topo_code = !empty($data['topo_code']) ? $data['topo_code'] : null;
        $topo_id = $topo_code ? substr($topo_code, 0, 2) : null;

        return [
            'patient_id'                => $patient_id,
            'hospital_code'             => $data['hospital_code'],
            'first_entrance_date'       => $this->convertDateFormat($data['first_entrance_date']),
            'entrance_date'             => $this->convertDateFormat($data['entrance_date']),
            'finance_support_text'      => DB::table('bd_finance_support')->where('code', $data['finance_support_code'])->selectRaw("CONCAT(code, ' ', name) as code_name")->value('code_name'),
            'finance_support_code'      => $data['finance_support_code'],
            'diagnosis_date'            => $this->convertDateFormat($data['diagnosis_date']),
            'diagnosis_code'            => $data['diagnosis_code'],
            'diagnosis_text'            => DB::table('bd_diagnosis')->where('code', $data['diagnosis_code'])->selectRaw("CONCAT(code, ' ', name) as code_name")->value('code_name'),
            'diagnosis_out'             => $data['diagnosis_out'],
            'excision_in_cut_date'      => $this->convertDateFormat($data['excision_in_cut_date']),
            'excision_in_read_date'     => $this->convertDateFormat($data['excision_in_read_date']),
            'topo_code'                 => $data['topo_code'],
            'topo_id'                   => $topo_id,
            'topo_text'                 => DB::table('bd_topo')->where('code', $data['topo_code'])->selectRaw("CONCAT(code, ' ', name) as code_name")->value('code_name'),
            'recurrent'                 => $data['recurrent'],
            'recurrent_date'            => $this->convertDateFormat($data['recurrent_date']),
            'morphology_code'           => $data['morphology_code'],
            'morphology_text'           => DB::table('bd_mor')->where('key', $data['morphology_code'])->selectRaw("CONCAT(code, ' ', name) as code_name")->value('code_name'),
            'behaviour_code'            => $data['behaviour_code'],
            'behaviour_text'            => DB::table('bd_behaviour')->where('code', $data['behaviour_code'])->selectRaw("CONCAT(code, ' ', name) as code_name")->value('code_name'),
            'grade_code'                => $data['grade_code'],
            'grade_text'                => DB::table('bd_grade')->where('code', $data['grade_code'])->selectRaw("CONCAT(code, ' ', name) as code_name")->value('code_name'),
            't_code'                    => $data['t_code'],
            'n_code'                    => $data['n_code'],
            'm_code'                    => $data['m_code'],
            'tnm_date'                  => $this->convertDateFormat($data['tnm_date']),
            'stage_code'                => $data['stage_code'],
            'extension_code'            => $data['extension_code'],
            'icd10_group_id'            => CancerController::getIcd10group($data['icd10_code']),
            'icd10_code'                => $data['icd10_code'],
            'icd10_text'                => DB::table('bd_icd10')->where('ICD10', $data['icd10_code'])->value('ICD10_TEXT'),
            'met_1'                     => $data['met_1'],
            'met_1_date'                => $this->convertDateFormat($data['met_1_date']),
            'met_2'                     => $data['met_2'],
            'met_2_date'                => $this->convertDateFormat($data['met_2_date']),
            'met_3'                     => $data['met_3'],
            'met_3_date'                => $this->convertDateFormat($data['met_3_date']),
            'met_4'                     => $data['met_4'],
            'met_4_date'                => $this->convertDateFormat($data['met_4_date']),
            'met_5'                     => $data['met_5'],
            'met_5_date'                => $this->convertDateFormat($data['met_5_date']),
            'met_6'                     => $data['met_6'],
            'met_6_date'                => $this->convertDateFormat($data['met_6_date']),
            'met_7'                     => $data['met_7'],
            'met_7_date'                => $this->convertDateFormat($data['met_7_date']),
            'met_7_other'               => $data['met_7_other'],
            'txt_clinical_sammary'      => $data['clinical_summary'],
            'age'                       => $this->calculateAge($data['birth_date'], $data['diagnosis_date']),
            'created_at'                => Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'                => $user->id,
            'updated_at'                => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_by'                => $user->id,
            'source_id'                 => 6, // Import source
        ];
    }

    private function getTreatmentValue($treatment, $patient_id, $cancer_id, $icd10_code)
    {
        return [
            'patient_id'            => $patient_id,
            'cancer_id'             => $cancer_id,
            'icd10_code'            => $icd10_code,
            'treatment_type_id'     => $treatment['treatment_code'],
            'treatment_code'        => DB::table('bd_treatment')->where('code', $treatment['treatment_code'])->selectRaw("CONCAT(code, ' ', name) as code_name")->value('code_name'),
            'treatment_date'        => $this->convertDateFormat($treatment['treatment_date']),
            'treatment_date_end'    => $this->convertDateFormat($treatment['treatment_date_end']),
            'note'                  => $treatment['note'],
            'none_protocol'         => $treatment['none_protocol'],
            'none_protocol_note'    => $treatment['none_protocol_note'],
            'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'            => Carbon::now()->format('Y-m-d H:i:s'),
        ];
    }

    private function convertDateFormat($dateString)
    {
        if (empty($dateString)) {
            return null;
        }

        try {
            // ถ้าเป็นรูปแบบ d/m/Y
            if (preg_match('/^\d{1,2}\/\d{1,2}\/\d{4}$/', $dateString)) {
                [$day, $month, $year] = explode('/', $dateString);

                // แปลงปี พ.ศ. เป็น ค.ศ.
                if ((int)$year > 2400) {
                    $year -= 543;
                }

                return Carbon::createFromDate($year, $month, $day)->format('Y-m-d');
            }

            // ถ้าเป็นรูปแบบ Y-m-d อยู่แล้ว
            if (preg_match('/^\d{4}-\d{1,2}-\d{1,2}$/', $dateString)) {
                [$day, $month, $year] = explode('-', $dateString);

                // แปลงปี พ.ศ. เป็น ค.ศ.
                if ((int)$year > 2400) {
                    $year -= 543;
                }

                return Carbon::createFromDate($year, $month, $day)->format('Y-m-d');
            }

            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function calculateAge($birthDate, $diagnosisDate)
    {
        try {
            $birth = Carbon::createFromFormat('d/m/Y', $birthDate);
            $diagnosis = Carbon::createFromFormat('d/m/Y', $diagnosisDate);
            return $birth->diffInYears($diagnosis);
        } catch (\Exception $e) {
            return null;
        }
    }

    private function createErrorExcelFile($errors)
    {
        try {
            Log::info('Creating error Excel file for ' . count($errors) . ' errors');

            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // ตั้งชื่อ sheet
            $sheet->setTitle('ข้อมูลที่ไม่ผ่านการตรวจสอบ');

            // สร้าง header สำหรับทุกคอลัมน์
            $headers = [
                'แถวที่',
                'ข้อผิดพลาด',
                'รหัสโรงพยาบาล',           // 0
                'HN',                      // 1
                'รหัสคำนำหน้า',             // 2
                'ชื่อ',                    // 3
                'นามสกุล',                 // 4
                'เลขบัตรประชาชน',          // 5
                'วันเกิด',                 // 6
                'รหัสเพศ',                 // 7
                'รหัสสัญชาติ',             // 8
                'วันที่เสียชีวิต',          // 9
                'รหัสสาเหตุการเสียชีวิต',    // 10
                'บ้านเลขที่',               // 11
                'หมู่',                    // 12
                'รหัสพื้นที่',              // 13
                'รหัสไปรษณีย์',            // 14
                'บ้านเลขที่ (ถาวร)',        // 15
                'หมู่ (ถาวร)',             // 16
                'รหัสพื้นที่ (ถาวร)',       // 17
                'รหัสไปรษณีย์ (ถาวร)',     // 18
                'โทรศัพท์ 1',              // 19
                'โทรศัพท์ 2',              // 20
                'วันที่เข้ารับการรักษาครั้งแรก', // 21
                'วันที่เข้ารับการรักษา',    // 22
                'รหัสการสนับสนุนทางการเงิน', // 23
                'วันที่วินิจฉัย',           // 24
                'รหัสการวินิจฉัย',          // 25
                'การวินิจฉัยออก',          // 26
                'วันที่ตัดออกใน',           // 27
                'วันที่อ่านตัดออกใน',       // 28
                'รหัส Topo',              // 29
                'การกลับเป็นซ้ำ',          // 30
                'วันที่กลับเป็นซ้ำ',        // 31
                'รหัส Morphology',        // 32
                'รหัส Behaviour',         // 33
                'รหัส Grade',             // 34
                'รหัส T',                 // 35
                'รหัส N',                 // 36
                'รหัส M',                 // 37
                'วันที่ TNM',             // 38
                'รหัส Stage',             // 39
                'รหัส Extension',         // 40
                'รหัส ICD10',             // 41
                'Met 1',                  // 42
                'วันที่ Met 1',           // 43
                'Met 2',                  // 44
                'วันที่ Met 2',           // 45
                'Met 3',                  // 46
                'วันที่ Met 3',           // 47
                'Met 4',                  // 48
                'วันที่ Met 4',           // 49
                'Met 5',                  // 50
                'วันที่ Met 5',           // 51
                'Met 6',                  // 52
                'วันที่ Met 6',           // 53
                'Met 7',                  // 54
                'วันที่ Met 7',           // 55
                'Met 7 อื่นๆ',            // 56
                'สรุปทางคลินิก',           // 57
            ];

            // เพิ่ม header สำหรับการรักษา (58-116)
            for ($i = 1; $i <= 10; $i++) {
                $headers[] = "รหัสการรักษา $i";        // 58, 64, 70, 76, 82, 88, 94, 100, 106, 112
                $headers[] = "วันที่เริ่มการรักษา $i";  // 59, 65, 71, 77, 83, 89, 95, 101, 107, 113
                $headers[] = "วันที่สิ้นสุดการรักษา $i"; // 60, 66, 72, 78, 84, 90, 96, 102, 108, 114
                $headers[] = "หมายเหตุการรักษา $i";     // 61, 67, 73, 79, 85, 91, 97, 103, 109, 115
                $headers[] = "ไม่ตาม Protocol $i";     // 62, 68, 74, 80, 86, 92, 98, 104, 110, 116
                $headers[] = "หมายเหตุไม่ตาม Protocol $i"; // 63, 69, 75, 81, 87, 93, 99, 105, 111, 117
            }

            // ตั้งค่า header ในไฟล์ Excel
            $columnIndex = 0;
            foreach ($headers as $header) {
                $columnLetter = $this->getColumnLetter($columnIndex);
                $sheet->setCellValue($columnLetter . '1', $header);
                $sheet->getStyle($columnLetter . '1')->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setARGB('FFFF0000');
                $sheet->getStyle($columnLetter . '1')->getFont()->getColor()->setARGB('FFFFFFFF');
                $sheet->getStyle($columnLetter . '1')->getFont()->setBold(true);
                $columnIndex++;
            }

            // เพิ่มข้อมูล error
            $row = 2;
            foreach ($errors as $error) {
                $columnIndex = 0;

                // แถวที่และข้อผิดพลาด
                $sheet->setCellValue($this->getColumnLetter($columnIndex++) . $row, $error['row']);
                $sheet->setCellValue($this->getColumnLetter($columnIndex++) . $row, implode(', ', $error['errors']));

                // เพิ่มข้อมูลทุกคอลัมน์จากแถวที่ error
                if (isset($error['data'])) {
                    $data = $error['data'];

                    // เพิ่มข้อมูลจากคอลัมน์ 0-117 (หรือจนกว่าจะหมดข้อมูล)
                    for ($i = 0; $i < count($data); $i++) {
                        $sheet->setCellValue($this->getColumnLetter($columnIndex++) . $row, $data[$i] ?? '');
                    }
                }

                // ทำให้แถว error เป็นสีแดงอ่อน
                $lastColumn = $this->getColumnLetter(count($headers) - 1);
                $sheet->getStyle('A' . $row . ':' . $lastColumn . $row)->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setARGB('FFFFCCCC');

                $row++;
            }

            // ปรับขนาดคอลัมน์
            for ($i = 0; $i < count($headers); $i++) {
                $sheet->getColumnDimension($this->getColumnLetter($i))->setAutoSize(true);
            }

            // บันทึกไฟล์
            $filename = 'cancer_import_errors_' . Carbon::now()->format('YmdHis') . '.xlsx';
            $path = 'upload/imports/errors/' . Carbon::now()->format('Ymd');

            // สร้างโฟลเดอร์ถ้ายังไม่มี (สร้างแบบ recursive)
            $fullDirectoryPath = public_path($path);
            Log::info('Creating directory: ' . $fullDirectoryPath);

            if (!file_exists($fullDirectoryPath)) {
                $created = mkdir($fullDirectoryPath, 0755, true);
                Log::info('Directory creation result: ' . ($created ? 'success' : 'failed'));
            }

            $fullPath = $path . '/' . $filename;
            $fullFilePath = public_path($fullPath);

            Log::info('Saving Excel file to: ' . $fullFilePath);

            $writer = new Xlsx($spreadsheet);
            $writer->save($fullFilePath);

            Log::info('Excel file saved successfully');

            return $fullPath;
        } catch (\Exception $e) {
            Log::error('Error creating error Excel file: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * แปลงหมายเลขคอลัมน์เป็นตัวอักษร Excel (A, B, C, ..., AA, AB, ...)
     */
    private function getColumnLetter($columnIndex)
    {
        $letter = '';
        while ($columnIndex >= 0) {
            $letter = chr($columnIndex % 26 + 65) . $letter;
            $columnIndex = intval($columnIndex / 26) - 1;
        }
        return $letter;
    }

    private function convertCIDToFormatted($cid)
    {
        // ตรวจสอบว่า $cid เป็นค่าว่างเปล่าหรือไม่ หากใช่ ให้คืนค่า null
        if (empty($cid)) {
            return null;
        }

        // ตรวจสอบว่า $cid เป็นตัวเลข 13 หลักหรือไม่
        if (preg_match('/^(\d{1})(\d{4})(\d{5})(\d{2})(\d{1})$/', $cid, $matches)) {
            // หากตรงตามรูปแบบ ให้จัดรูปแบบโดยเพิ่มขีดคั่น
            // $matches[1] คือหลักที่ 1
            // $matches[2] คือหลักที่ 2-5
            // $matches[3] คือหลักที่ 6-10
            // $matches[4] คือหลักที่ 11-12
            // $matches[5] คือหลักที่ 13
            return "{$matches[1]}-{$matches[2]}-{$matches[3]}-{$matches[4]}-{$matches[5]}";
        }

        // หากไม่ตรงกับรูปแบบตัวเลข 13 หลัก ให้คืนค่า null หรือ $cid เดิม ขึ้นอยู่กับความต้องการ
        return null;
    }
}
