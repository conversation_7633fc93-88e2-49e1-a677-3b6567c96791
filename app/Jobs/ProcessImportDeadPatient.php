<?php

namespace App\Jobs;

use App\Imports\PatientDeadImport;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class ProcessImportDeadPatient implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $file_path;
    public $import_id;

    /**
     * Create a new job instance.
     */
    public function __construct($import_id, $file_path)
    {
        $this->import_id = $import_id;
        $this->file_path = $file_path;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Processing import daed job');
        
        $import = new PatientDeadImport();
        
        Excel::import($import, $this->file_path, 'public');

        DB::table('import_table')->where('id', $this->import_id)->update([
            'total'         => $import->found + $import->notFound + $import->failed,
            'ok'            => $import->found + $import->notFound,
            'fail'          => $import->failed,
            'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'finished_at'   => Carbon::now()->format('Y-m-d H:i:s'),
        ]);

        Log::info('Finished processing import daed job');
    }
}
