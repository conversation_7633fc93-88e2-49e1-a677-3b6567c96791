<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithTitle;

class Rp01Export implements FromCollection, WithHeadings, WithStrictNullComparison, WithTitle
{
    protected $input;

    // Constructor to pass parameters
    public function __construct($input)
    {
        $this->input = $input;
    }

    // Fetch the data for the export
    public function collection()
    {
        $input = $this->input;
        $start_date = $this->input->query('date_start');
        $end_date = $this->input->query('date_end');
        $gender_code = $this->input->query('gender_code');
        $area_level_id = $this->input->query('area_level_id');
        $area_id = $this->input->query('area_id');
        $behaviour_code_start = $this->input->query('behaviour_code_start');
        $behaviour_code_end = $this->input->query('behaviour_code_end');
        $stage_code_start = $this->input->query('stage_code_start');
        $stage_code_end = $this->input->query('stage_code_end');
        $morpho_start = $this->input->query('morpho_start');
        $morpho_end = $this->input->query('morpho_end');
        $treatment_code = $this->input->query('treatment_code');
        $age_start = $this->input->query('age_start');
        $age_end = $this->input->query('age_end');
        $recurent = $this->input->query('recurent');
        $deathcase = $this->input->query('deathcase');
        $icd10_group_id = array_filter($this->input->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                # code...
                break;

            case 2:
                # code...
                break;

            case 3:
                # code...
                break;

            case 4:
                $results = DB::table('data_patient', 'p')
                    ->select(
                        'hos.code',
                        'hos.name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_cancer_summary as s', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_hospital as hos', 'p.hospital_code', '=', 'hos.code')
                    ->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code')
                    ->where('s.behaviour_code', 3)
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.code', $area_id);
                    })
                    ->when((isset($behaviour_code_start) && isset($behaviour_code_end)), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('hos.code', 'hos.name')
                    ->get();
                break;

            default:
                # code...
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? round($value->male_count * 100.0 / $total_male, 2)
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? round($value->female_count * 100.0 / $total_female, 2)
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? round($value->total_count * 100.0 / $total_count, 2)
                : 0;
        }

        $total_male_per = $results->sum('male_percentage');
        $total_female_per = $results->sum('female_percentage');
        $total_per = $results->sum('total_percentage');

        $results->push((object)[
            'code' => '',
            'name' => 'รวม',
            'male_count' => $total_male,
            'male_percentage' => $total_male_per,
            'female_count' => $total_female,
            'female_percentage' => $total_female_per,
            'total_count' => $total_count,
            'total_percentage' => $total_per,
        ]);

        return $results;
    }

    public function headings(): array
    {
        return [
            'รหัส',
            'สถานพยาบาล',
            'ชาย(ราย)',
            'ชาย (%)',
            'หญิง(ราย)',
            'หญิง (%)',
            'รวม(ราย)',
            'รวม (%)'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'RP01';
    }
}
