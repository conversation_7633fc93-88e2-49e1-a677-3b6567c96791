<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithTitle;

class DiagnosisExport implements FromCollection, WithHeadings, WithStrictNullComparison, WithTitle
{
    protected $date_start;
    protected $date_end;
    protected $gender_code;
    protected $area_level_id;
    protected $area_id;
    protected $behaviour_code_start;
    protected $behaviour_code_end;
    protected $stage_code_start;
    protected $stage_code_end;
    protected $morpho_start;
    protected $morpho_end;
    protected $treatment_code;
    protected $age_start;
    protected $age_end;
    protected $recurent;
    protected $deathcase;
    protected $icd10_group_id;

    public function __construct($request)
    {
        $this->date_start = $request->query('date_start');
        $this->date_end = $request->query('date_end');
        $this->gender_code = $request->query('gender_code');
        $this->area_level_id = $request->query('area_level_id');
        $this->area_id = $request->query('area_id');
        $this->behaviour_code_start = $request->query('behaviour_code_start');
        $this->behaviour_code_end = $request->query('behaviour_code_end');
        $this->stage_code_start = $request->query('stage_code_start');
        $this->stage_code_end = $request->query('stage_code_end');
        $this->morpho_start = $request->query('morpho_start');
        $this->morpho_end = $request->query('morpho_end');
        $this->treatment_code = $request->query('treatment_code');
        $this->age_start = $request->query('age_start');
        $this->age_end = $request->query('age_end');
        $this->recurent = $request->query('recurent');
        $this->deathcase = $request->query('deathcase');
        $this->icd10_group_id = array_filter($request->query('icd10_group_id', []));
    }

    public function collection()
    {
        $query = DB::table('data_patient')
            ->leftJoin('data_cancer_summary', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
            ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
            ->select(
                'bd_diagnosis.code',
                'bd_diagnosis.name',
                DB::raw('COUNT( CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                DB::raw('COUNT( CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                DB::raw('COUNT( data_cancer_summary.id) AS total_count')
            )
            ->where('data_cancer_summary.behaviour_code', 3);

        if ($this->date_start && $this->date_end) {
            $query->whereBetween('data_cancer_summary.diagnosis_date', [$this->date_start, $this->date_end]);
        }
        if ($this->gender_code) {
            $query->where('data_patient.sex_code', $this->gender_code);
        }
        if ($this->area_id) {
            $query->where('data_patient.hospital_code', $this->area_id);
        }
        if ($this->behaviour_code_start && $this->behaviour_code_end) {
            $query->whereBetween('data_cancer_summary.behaviour_code', [$this->behaviour_code_start, $this->behaviour_code_end]);
        }
        if ($this->stage_code_start && $this->stage_code_end) {
            $query->whereBetween('data_cancer_summary.stage_code', [$this->stage_code_start, $this->stage_code_end]);
        }
        if ($this->morpho_start && $this->morpho_end) {
            $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$this->morpho_start, $this->morpho_end]);
        }
        if ($this->treatment_code) {
            $query->where('data_cancer_summary_treatment.treatment_type_id', $this->treatment_code);
        }
        if ($this->age_start && $this->age_end) {
            $query->whereBetween('data_cancer_summary.age', [$this->age_start, $this->age_end]);
        }
        if (isset($this->recurent) && $this->recurent == 'Y') {
            $query->where('data_cancer_summary.recurrent', 1);
        }
        if (isset($this->deathcase) && $this->deathcase == 'Y') {
            $query->whereNotNull('data_patient.death_date');
        }
        if ($this->icd10_group_id) {
            $query->whereIn('data_cancer_summary.icd10_group_id', $this->icd10_group_id);
        }
        $query->groupBy('bd_diagnosis.code', 'bd_diagnosis.name');
        $query->orderBy('bd_diagnosis.code');

        $results = $query->get();

        $diagnoses = DB::table('bd_diagnosis')
            ->select(
                'bd_diagnosis.code',
                'bd_diagnosis.name',
                DB::raw('0 AS male_count'),
                DB::raw('0 AS male_percentage'),
                DB::raw('0 AS female_count'),
                DB::raw('0 AS female_percentage'),
                DB::raw('0 AS total_count'),
                DB::raw('0 AS total_percentage')
            )
            ->orderBy('bd_diagnosis.code', 'asc')
            ->get();

        $merged = $results->merge($diagnoses)->unique('code')->sortBy('code');

        $total_male = $merged->sum('male_count');
        $total_female = $merged->sum('female_count');
        $total_count = $merged->sum('total_count');

        foreach ($merged as $key => $value) {
            $merged[$key]->male_percentage = $total_male > 0
                ? ($value->male_count * 100.0) / $total_male
                : 0;

            $merged[$key]->female_percentage = $total_female > 0
                ? ($value->female_count * 100.0) / $total_female
                : 0;

            $merged[$key]->total_percentage = $total_count > 0
                ? ($value->total_count * 100.0) / $total_count
                : 0;
        }

        $merged->push((object)[
            'code' => 'รวม',
            'name' => '',
            'male_count' => $total_male,
            'female_count' => $total_female,
            'total_count' => $total_count,
            'male_percentage' => $total_male > 0 ? 100 : 0,
            'female_percentage' => $total_female > 0 ? 100 : 0,
            'total_percentage' => $total_count > 0 ? 100 : 0,
        ]);

        return $merged;
    }
    public function headings(): array
    {
        return [
            'รหัสการวินิจฉัย',
            'วิธีวินิจฉัย',
            'เพศชาย(ราย)',
            'เพศหญิง(ราย)',
            'รวม(ราย)',
            'เพศชาย(%)',
            'เพศหญิง(%)',
            'รวม(%)'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'RP02';
    }
}
