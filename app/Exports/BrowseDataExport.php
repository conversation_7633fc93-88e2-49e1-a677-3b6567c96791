<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\DB;

class BrowseDataExport implements FromCollection, WithHeadings
{
    protected $date_start;
    protected $date_end;
    protected $hospital_code;
    public function __construct($date_start, $date_end, $hospital_code = [])
    {
        $this->date_start = $date_start;
        $this->date_end = $date_end;
        $this->hospital_code = $hospital_code;
    }

    public function collection()
    {
        $query = DB::table('vw_data_export');

        if ($this->date_start && $this->date_end) {
            $query->whereBetween('vw_data_export.diagnosis_date', [$this->date_start, $this->date_end]);
        }
        if ($this->hospital_code) {
            $query->whereIn('vw_data_export.hospital_code', $this->hospital_code);
        }
        return $query->get();
    }

    public function headings(): array
    {
        return [
            'รหัสโรงพยาบาบ',
            'HN',
            'คํานําหน้า',
            'ชื่อ',
            'นามสกุล',
            'เลขบัตรประชาชน',
            'เพศ',
            'วันเกิด',
            'สัญชาติ',
            'บ้านเลขที่',
            'หมู่',
            'ตําบล',
            'อําเภอ',
            'จังหวัด',
            'รหัสที่อยู่',
            'บ้านเลขที่ตามทะเบียนบ้าน',
            'หมู่ตามทะเบียนบ้าน',
            'ตําบลตามทะเบียนบ้าน',
            'อําเภอตามทะเบียนบ้าน',
            'จังหวัดตามทะเบียนบ้าน',
            'รหัสที่อยู่ตามทะเบียนบ้าน',
            'วันที่วิเนิจฉัย',
            'Topo',
            'Morphology',
            'Behaviour',
            'ICD10',
            'ICD10Text',
            'Grade',
            'Stage',
            'Extension',
            'วันที่เสียชีวิต',
            'สาเหตุการเสียชีวิต'
        ];
    }
}
