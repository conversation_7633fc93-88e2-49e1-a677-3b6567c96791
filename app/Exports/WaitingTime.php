<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class WaitingTime implements FromArray, WithHeadings
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'CID',
            'ชื่อ',
            'ICD-10',
            'วันที่ Visit',
            'วันที่วินิจฉัย',
            'วันที่ตัดชิ้นเนื้อ',
            'วันที่เริ่มผ่าตัด',
            'วันที่สิ้นสุดผ่าตัด',
            'วันที่เริ่มฉายแสง',
            'วันที่สิ้นสุดฉายแสง',
            'วันที่เริ่มเคมี',
            'วันที่สิ้นสุดเคมี',
        ];
    }
}
