<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class BrowseDataVisitExport implements FromCollection, WithHeadings, WithStyles
{
    protected $date_start;
    protected $date_end;
    protected $hospital_code;

    public function __construct($date_start, $date_end, $hospital_code)
    {
        $this->date_start = $date_start;
        $this->date_end = $date_end;
        $this->hospital_code = $hospital_code;
    }

    public function styles(Worksheet $sheet)
    {
        // เพิ่มสีพื้นหลังให้กับแต่ละช่วง
        $sheet->getStyle('A1:AE1')
            ->getFill()->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('FFFF00'); // สีเหลือง

        $sheet->getStyle('AF1:BO1')
            ->getFill()->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('ADD8E6'); // สีฟ้าอ่อน

        $sheet->getStyle('BP1:BT1')
            ->getFill()->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('90EE90'); // สีเขียวอ่อน
    }

    public function collection()
    {
        $query = DB::table('vw_data_visit_export');

        if ($this->date_start && $this->date_end) {
            $query->whereBetween('diagnosis_date', [$this->date_start, $this->date_end]);
            // ->orWhereNull('vw_data_export.diagnosis_date');  // ใช้ orWhere เพื่อลากข้อมูลที่ diagnosis_date เป็น null
        }

        if ($this->hospital_code) {
            $query->where('hospital_code', $this->hospital_code);
        }

        return $query->get();
    }

    public function headings(): array
    {
        $headers = [
            // ส่วนที่ 1 - ข้อมูลทั่วไป
            'รหัสโรงพยาบาล',
            'หมายเลขผู้ป่วย',
            'คำนำหน้า',
            'ชื่อ',
            'นามสกุล',
            'หมายเลขบัตรประชาชน',
            'เพศ',
            'วันเกิด',
            'สัญชาติ',
            'บ้านเลขที่',
            'หมู่ที่',
            'รหัสไปรษณีย์',
            'รหัสพื้นที่',
            'ตำบล',
            'อำเภอ',
            'จังหวัด',
            'รหัสจังหวัดที่อยู่',
            'รหัสอำเภอที่อยู่',
            'รหัสตำบลที่อยู่',
            'บ้านเลขที่ที่อยู่ถาวร',
            'หมู่ที่ที่อยู่ถาวร',
            'รหัสไปรษณีย์ที่อยู่ถาวร',
            'รหัสพื้นที่ที่อยู่ถาวร',
            'ตำบลที่อยู่ถาวร',
            'อำเภอที่อยู่ถาวร',
            'จังหวัดที่อยู่ถาวร',
            'รหัสจังหวัดที่อยู่ถาวร',
            'รหัสอำเภอที่อยู่ถาวร',
            'รหัสตำบลที่อยู่ถาวร',
            'วันเสียชีวิต',
            'รหัสสาเหตุการตาย',

            // ส่วนที่ 2 - ข้อมูลเกี่ยวกับมะเร็ง
            'ประเภทเนื้อเยื่อ',
            'การกลับเป็นซ้ำ',
            'วันที่กลับเป็นซ้ำ',
            'การวินิจฉัย',
            'ลักษณะทางพยาธิวิทยา',
            'พฤติกรรมทางพยาธิวิทยา',
            'เกรด',
            'ระยะ',
            'การขยายตัว',
            'การแพร่กระจาย',
            'อวัยวะที่แพร่กระจาย',
            'Bone',
            'วันที่ Bone',
            'Brain',
            'วันที่ Brain',
            'Liver',
            'วันที่ Liver',
            'Lung',
            'วันที่ Lung',
            'Lymph Node',
            'วันที่ Lymph Node',
            'Peritoneum',
            'วันที่ Peritoneum',
            'Other',
            'วันที่ Other',
            'Other',
            'วันที่วินิจฉัย',
            'รหัส ICD10',
            'รายละเอียด ICD10',
            'วันที่เข้ารับการรักษาครั้งแรก',
            'วันที่เข้ารับการรักษา',
            'สรุปทางคลินิก',
            'T',
            'N',
            'M',
            'วันที่ TNM',

            // ส่วนที่ 3 - ข้อมูลการรักษา
            'รหัสการรักษา',
            'วันที่เริ่มการรักษา',
            'วันที่สิ้นสุดการรักษา',
            'วันที่ปรึกษา',
            'หมายเหตุ'
        ];

        return $headers;
    }
}
