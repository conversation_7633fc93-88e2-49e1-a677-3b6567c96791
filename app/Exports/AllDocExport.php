<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class AllDocExport implements WithMultipleSheets
{
    protected $input;

    // Constructor to pass parameters
    public function __construct($input)
    {
        $this->input = $input;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        $sheets[] = new Rp01Export($this->input);//01
        $sheets[] = new DiagnosisExport($this->input);//02
        $sheets[] = new ExtensionExport($this->input);//03
        $sheets[] = new ReportStageExport($this->input);//05
        $sheets[] = new number_type_treatment_Export($this->input);//06
        $sheets[] = new PatientsAddressExport($this->input);//07

        // $this->input['gender_code'] = 1;
        $sheets[] = new Export_Types_of_cancer_ExcelMale($this->input);//08 ชาย
        // $this->input['gender_code'] = 2;
        $sheets[] = new Export_Types_of_cancer_ExcelFemale($this->input);//09 หญิง

        return $sheets;
    }
}
