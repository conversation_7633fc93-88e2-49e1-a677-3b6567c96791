<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Facades\DB;

class Refuse implements FromCollection, WithHeadings, WithTitle, WithStyles,WithStrictNullComparison
{
    protected $hospitalCode;
    protected $dateStart;
    protected $dateEnd;

    public function __construct($hospitalCode = null, $dateStart = null, $dateEnd = null)
    {
        $this->hospitalCode = $hospitalCode;
        $this->dateStart = $dateStart;
        $this->dateEnd = $dateEnd;
    }

    public function collection()
    {
        $merged = DB::table('data_refer AS R')
            ->leftJoin('bd_hospital AS HOS', 'R.from_hospital_code', '=', 'HOS.code')
            ->select(
                'HOS.code',
                'HOS.name',
                DB::raw('COALESCE(SUM(R.reson_reject_1), 0) AS reson_reject_1'),
                DB::raw('COALESCE(SUM(R.reson_reject_2), 0) AS reson_reject_2'),
                DB::raw('COALESCE(SUM(R.reson_reject_3), 0) AS reson_reject_3')
            )
            ->when($this->hospitalCode, function ($query, $hospitalCode) {
                return $query->where('R.from_hospital_code', '=', $hospitalCode);
            })
            ->when($this->dateStart && $this->dateEnd, function ($query) {
                return $query->whereBetween(DB::raw('DATE(R.created_at)'), [$this->dateStart, $this->dateEnd]);
            })
            ->groupBy('HOS.name', 'HOS.code')
            ->orderBy('HOS.code', 'ASC')
            ->get();

        $total_reject_1 = $merged->sum('reson_reject_1');
        $total_reject_2 = $merged->sum('reson_reject_2');
        $total_reject_3 = $merged->sum('reson_reject_3');

        $merged->push((object)[
            'code' => '',
            'name' => 'รวม',
            'reson_reject_1' => $total_reject_1,
            'reson_reject_2' => $total_reject_2,
            'reson_reject_3' => $total_reject_3,
        ]);

        return $merged;
    }

    public function headings(): array
    {
        return [
            ['เหตุผลการปฏิเสธ'],
            ['รหัสโรงพยาบาล',
            'ชื่อโรงพยาบาล',
            'ด้านบุคลกร',
            'ด้านเคื่องมือ',
            'ด้านบริการ']
        ];
    }

    public function title(): string
    {
        return 'รายงานการปฏิเสธ';
    }
    public function styles(Worksheet $sheet)
    {
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A1')->getFont()->setBold(true);
        $sheet->getColumnDimension('A')->setWidth(25);
        $sheet->getColumnDimension('B')->setWidth(25);
        $sheet->getColumnDimension('C')->setWidth(25);
        $sheet->getColumnDimension('D')->setWidth(25);
        $sheet->getColumnDimension('E')->setWidth(25);
    }
}
