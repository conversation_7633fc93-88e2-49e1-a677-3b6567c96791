<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExportCancerDetail implements FromCollection, WithHeadings
{
    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'รหัสโรงพยาบาล',
            'HN',
            'คำนำหน้า',
            'ชื่อ',
            'นามสกุล',
            'เลขบัตรประชาชน',
            'วันเกิด',
            'เพศ',
            'สัญชาติ',
            'วันที่เสียชีวิต',
            'สาเหตุการเสียชีวิต',
            'บ้านเลขที่',
            'หมู่ที่',
            'ตำบล',
            'อำเภอ',
            'จังหวัด',
            'รหัสไปรษณีย์',
            'บ้านเลขที่',
            'หมู่ที่',
            'ตำบล',
            'อำเภอ',
            'จังหวัด',
            'รหัสไปรษณีย์',
            'เบอร์โทร 1',
            'เบอร์โทร 2',
            'วันที่เข้ารับบริการครั้งแรก',
            'สิทธิการรักษา',
            'วันที่วินิจฉัย',
            'อายุ',
            'วิธีวินิจฉัย',
            'วินิจฉัยนอก รพ.',
            'วันที่ตัดชิ้นเนื้อ',
            'วันที่อ่านชิ้นเนื้อ',
            'Topography',
            'Recurrent',
            'วันที่ Recurrent',
            'Morphology',
            'Behaviour',
            'Grade',
            'T',
            'N',
            'M',
            'วันที่ TNM',
            'Stage',
            'Extend',
            'ICD-10 Group',
            'รหัส ICD-10',
            'ชื่อ ICD-10',
            'Bone',
            'Bone วันที่',
            'Brain',
            'Brain วันที่',
            'Liver',
            'Liver วันที่',
            'Lung',
            'Lung วันที่',
            'Lymph Node',
            'Lymph Node วันที่',
            'Peritoneum',
            'Peritoneum วันที่',
            'Other',
            'Other วันที่',
            'ระบุ',
            'Clinical Summary',
        ];
    }
}
