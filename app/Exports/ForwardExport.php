<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Facades\DB;

class ForwardExport implements FromCollection, WithHeadings, WithTitle, WithStyles,WithStrictNullComparison
{
    protected $hospitalCode;
    protected $date_start;
    protected $date_end;
    public function __construct($request)
    {
        $this->hospitalCode = $request->query('hospital_code', null);
        $this->date_start = $request->query('date_start');
        $this->date_end = $request->query('date_end');
    }
    public function collection()
    {
        $merged = DB::table('data_refer as R')
            ->leftJoin('bd_hospital as HOS', 'R.to_hospital_code', '=', 'HOS.code')
            ->select(
                'HOS.code',
                'HOS.name',
                DB::raw('SUM(R.reson_diagnosis_excision) AS reson_diagnosis_excision'),
                DB::raw('SUM(R.reson_diagnosis_ct) AS reson_diagnosis_ct'),
                DB::raw('SUM(R.reson_diagnosis_mri) AS reson_diagnosis_mri'),
                DB::raw('SUM(R.reson_diagnosis_bone) AS reson_diagnosis_bone'),
                DB::raw('SUM(R.reson_diagnosis_mammogram) AS reson_diagnosis_mammogram'),
                DB::raw('SUM(R.reson_diagnosis_ultra) AS reson_diagnosis_ultra'),
                DB::raw('SUM(R.reson_diagnosis_other) AS reson_diagnosis_other'),
                DB::raw('SUM(R.reson_treat_radiation) AS reson_treat_radiation'),
                DB::raw('SUM(R.reson_treat_surgery) AS reson_treat_surgery'),
                DB::raw('SUM(R.reson_treat_chemo) AS reson_treat_chemo'),
                DB::raw('SUM(R.reson_treat_pallative) AS reson_treat_pallative'),
                DB::raw('SUM(R.reson_treat_other) AS reson_treat_other'),
                DB::raw('SUM(R.reson_right) AS reson_right'),
                DB::raw('SUM(R.reson_wanted) AS reson_wanted'),
                DB::raw('SUM(R.reson_other) AS reson_other')
            )
            ->whereBetween(DB::raw('DATE(R.created_at)'), [$this->date_start, $this->date_end])
            
            ->when($this->hospitalCode, function ($query, $hospitalCode) {
                return $query->where('R.to_hospital_code', '=', $hospitalCode);
            })
            ->groupBy('HOS.name', 'HOS.code')
            ->orderBy('HOS.code', 'asc')
            ->get();


        $total_excision = $merged->sum('reson_diagnosis_excision');
        $total_ct = $merged->sum('reson_diagnosis_ct');
        $total_mri = $merged->sum('reson_diagnosis_mri');
        $total_bone = $merged->sum('reson_diagnosis_bone');
        $total_mammogram = $merged->sum('reson_diagnosis_mammogram');
        $total_ultra = $merged->sum('reson_diagnosis_ultra');
        $total_diagnosis_other = $merged->sum('reson_diagnosis_other');
        $total_treat_radiation = $merged->sum('reson_treat_radiation');
        $total_treat_surgery = $merged->sum('reson_treat_surgery');
        $total_treat_chemo = $merged->sum('reson_treat_chemo');
        $total_treat_pallative = $merged->sum('reson_treat_pallative');
        $total_treat_other = $merged->sum('reson_treat_other');
        $total_right = $merged->sum('reson_right');
        $total_wanted = $merged->sum('reson_wanted');
        $total_other = $merged->sum('reson_other');
        $merged->push((object)[
            'code' => '',
            'name' => 'รวม',
            'reson_diagnosis_excision' => $total_excision,
            'reson_diagnosis_ct' => $total_ct,
            'reson_diagnosis_mri' => $total_mri,
            'reson_diagnosis_bone' => $total_bone,
            'reson_diagnosis_mammogram' => $total_mammogram,
            'reson_diagnosis_ultra' => $total_ultra,
            'reson_diagnosis_other' => $total_diagnosis_other,
            'reson_treat_radiation' => $total_treat_radiation,
            'reson_treat_surgery' => $total_treat_surgery,
            'reson_treat_chemo' => $total_treat_chemo,
            'reson_treat_pallative' => $total_treat_pallative,
            'reson_treat_other' => $total_treat_other,
            'reson_right' => $total_right,
            'reson_wanted' => $total_wanted,
            'reson_other' => $total_other

        ]);

        return $merged;
    }

    public function headings(): array
    {
        return [
            ['เหตุผลการส่ง refer'],
            ['โรงพยาบาลรับ refer', '', 'วินิจฉัย', '', '', '', '', '', '', 'การรักษา', '', '', '', '', 'สถิติอื่นๆ'],
            ['รหัสโรงพยาบาล', 'ชื่อโรงพยาบาล', 'การตัดชิ้นเนื้อ', 'CT', 'MRI', 'การสแกนกระดูก', 'Mammogram', 'Ultrasound', 'อื่นๆ', 'การฉายรังสี', 'การผ่าตัด', 'เคมีบำบัด', 'การดูแลแบบประคับประคอง', 'อื่นๆ', 'Right Count', 'Wanted Count', 'Other Count'],
        ];
    }

    public function title(): string
    {
        return $this->hospitalCode ? "Hospital Code {$this->hospitalCode} Report" : "All Hospitals Report";
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->mergeCells('A1:Q1');
        $sheet->mergeCells('A2:B2');
        $sheet->getStyle('A1')->getFont()->setBold(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A2')->getAlignment()->setHorizontal('center');
        $sheet->mergeCells('J2:N2');
        $sheet->getStyle('J2')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('J2')->getFont()->setBold(true);
        $sheet->mergeCells('C2:I2');
        $sheet->getStyle('C2')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('C2')->getFont()->setBold(true);
        $sheet->mergeCells('O2:Q2');
        $sheet->getStyle('O2')->getAlignment()->setHorizontal('center');
        $sheet->getStyle('O2')->getFont()->setBold(true);
        foreach (range('A', 'R') as $column) {
            $sheet->getColumnDimension($column)->setWidth(20);
        }
    }
}
