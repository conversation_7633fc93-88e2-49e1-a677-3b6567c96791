<?php

namespace App\Exports;

use App\Models\DataCancer;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class HospitalData implements FromCollection, WithHeadings
{
    public function collection()
    {
        return DataCancer::where('hospital_code', '12250')->limit(1000)->get();
    }

    public function headings(): array
    {
        return DB::getSchemaBuilder()->getColumnListing('data_cancer');
    }
}
