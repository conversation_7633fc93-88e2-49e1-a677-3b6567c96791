<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithTitle;

class ReportStageExport implements FromCollection, WithHeadings, WithStrictNullComparison, WithTitle
{
    protected $input;

    // Constructor to pass parameters
    public function __construct($input)
    {
        $this->input = $input;
    }

    // Fetch the data for the export
    public function collection()
    {
        $input = $this->input;
        $start_date = $this->input->query('date_start');
        $end_date = $this->input->query('date_end');
        $gender_code = $this->input->query('gender_code');
        $area_level_id = $this->input->query('area_level_id');
        $area_id = $this->input->query('area_id');
        $behaviour_code_start = $this->input->query('behaviour_code_start');
        $behaviour_code_end = $this->input->query('behaviour_code_end');
        $stage_code_start = $this->input->query('stage_code_start');
        $stage_code_end = $this->input->query('stage_code_end');
        $morpho_start = $this->input->query('morpho_start');
        $morpho_end = $this->input->query('morpho_end');
        $treatment_code = $this->input->query('treatment_code');
        $age_start = $this->input->query('age_start');
        $age_end = $this->input->query('age_end');
        $recurent = $this->input->query('recurent');
        $deathcase = $this->input->query('deathcase');
        $icd10_group_id = array_filter($this->input->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                # code...
                break;

            case 2:
                # code...
                break;

            case 3:
                # code...
                break;

            case 4:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }

                        if (isset($input['gender_code'])) {
                            $query->where('data_patient.sex_code', $input['gender_code']);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('bd_hospital.code', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();
                break;

            default:
                # code...
                break;
        }

        $bd_stage_group = DB::table('bd_stage_group')
            ->select(
                'bd_stage_group.code',
                'bd_stage_group.name',
                DB::raw('0 as male_count'),
                DB::raw('0 AS male_percentage'),
                DB::raw('0 AS female_count'),
                DB::raw('0 AS female_percentage'),
                DB::raw('0 AS total_count'),
                DB::raw('0 AS total_percentage')
            )->get();

        $merged = $results->merge($bd_stage_group)->unique('code')->sortBy('code');

        $total_male = $merged->sum('male_count');
        $total_female = $merged->sum('female_count');
        $total_count = $merged->sum('total_count');

        foreach ($merged as $key => $value) {
            $merged[$key]->male_percentage = $total_male > 0
                ? round($value->male_count * 100.0 / $total_male, 2)
                : 0;

            $merged[$key]->female_percentage = $total_female > 0
                ? round($value->female_count * 100.0 / $total_female, 2)
                : 0;

            $merged[$key]->total_percentage = $total_count > 0
                ? round($value->total_count * 100.0 / $total_count, 2)
                : 0;
        }

        $total_male_per = $merged->sum('male_percentage');
        $total_female_per = $merged->sum('female_percentage');
        $total_per = $merged->sum('total_percentage');

        $merged->push((object)[
            'code' => '',
            'name' => 'รวม',
            'male_count' => $total_male,
            'male_percentage' => $total_male_per,
            'female_count' => $total_female,
            'female_percentage' => $total_female_per,
            'total_count' => $total_count,
            'total_percentage' => $total_per,
        ]);

        return $merged;
    }

    public function headings(): array
    {
        return [
            'รหัส',
            'ระยะของโรค',
            'ชาย(ราย)',
            'ชาย (%)',
            'หญิง(ราย)',
            'หญิง (%)',
            'รวม(ราย)',
            'รวม (%)'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'RP05';
    }
}
