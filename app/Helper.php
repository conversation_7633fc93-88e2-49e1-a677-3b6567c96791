<?php
if (!function_exists('getDateTimeENtoTH')) {
    function getDateTimeENtoTH($DateEN, $full_year = true)
    {
        if (!$DateEN) {
            return null;
        }

        if (strlen($DateEN) == 10) { // yyyy-mm-dd
            $year = intval(substr($DateEN, 0, 4));
            if ($year % 4 == 0 && substr($DateEN, 4, 6) == '-02-29') {
                return '29/02/' . (($year + 543) - ($full_year ? 0 : 2500));
            } else {
                if ($full_year) {
                    return \Carbon\Carbon::createFromFormat('Y-m-d', $DateEN)->addYears(543)->format('d/m/Y');
                } else {
                    return \Carbon\Carbon::createFromFormat('Y-m-d', $DateEN)->addYears(543)->format('d/m/y');
                }
            }
        } else if (strlen($DateEN) == 19) { // yyyy-mm-dd hh:ii:ss
            $year = intval(substr($DateEN, 0, 4));
            if ($year % 4 == 0 && substr($DateEN, 4, 6) == '-02-29') {
                return '29/02/' . (($year + 543) - ($full_year ? 0 : 2500)) . substr($DateEN, 10, 6);
            } else {
                if ($full_year) {
                    return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $DateEN)->addYears(543)->format('d/m/Y H:i');
                } else {
                    return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $DateEN)->addYears(543)->format('d/m/y H:i');
                }
            }
        } else {
            return null;
        }
    }
}

if (!function_exists('getDateTimeTHtoEN')) {
    /**
     * แปลงสตริงวันที่/เวลาปฏิทินไทย (พุทธศักราช) เป็นสตริงวันที่/เวลาปฏิทินสากล (คริสต์ศักราช).
     *
     * ฟังก์ชันนี้รองรับรูปแบบอินพุต 2 แบบ:
     * - 'dd/mm/yyyy' (เฉพาะวันที่)
     * - 'dd/mm/yyyy hh:ii' (วันที่และเวลา รวมถึง ชั่วโมง และนาที)
     *
     * มันจะแปลงปีพุทธศักราชเป็นปีคริสต์ศักราชโดยการลบ 543 ปี.
     * ฟังก์ชันนี้ใช้ไลบรารี Carbon เพื่อการวิเคราะห์และจัดรูปแบบวันที่/เวลาที่เชื่อถือได้.
     *
     * @param string|null $DateTH สตริงวันที่หรือเวลาที่ต้องการแปลงในรูปแบบไทย (เช่น '25/02/2567' หรือ '25/02/2567 14:30').
     * หากอินพุตเป็น null หรืออยู่ในรูปแบบที่ไม่รองรับ ฟังก์ชันจะคืนค่า null.
     * @return string|null สตริงวันที่หรือเวลาที่ถูกแปลงแล้วในรูปแบบสากล (เช่น '2024-02-25' หรือ '2024-02-25 14:30:00'),
     * หรือ null หากอินพุตไม่ถูกต้องหรือเป็นค่าว่าง.
     */
    function getDateTimeTHtoEN($DateTH)
    {
        if (!$DateTH) {
            return null;
        }

        if (strlen($DateTH) == 10) { // dd/mm/yyyy
            $year = intval(substr($DateTH, 6, 4));
            if ($year % 4 == 3 && substr($DateTH, 0, 5) == '29/02') {
                return ($year - 543) . '-02-29';
            } else {
                return \Carbon\Carbon::createFromFormat('d/m/Y', $DateTH)->subYears(543)->format('Y-m-d');
            }
        } else if (strlen($DateTH) == 16) { // dd/mm/yyyy hh:ii
            $year = intval(substr($DateTH, 6, 4));
            if ($year % 4 == 3 && substr($DateTH, 0, 5) == '29/02') {
                return ($year - 543) . '-02-29' . substr($DateTH, 10, 6) . ':00';
            } else {
                return \Carbon\Carbon::createFromFormat('d/m/Y H:i', $DateTH)->subYears(543)->format('Y-m-d H:i:00');
            }
        } else {
            return null;
        }
    }
}

// 20240101
if (!function_exists('getDateTimeFormStr')) {
    function getDateTimeFormStr($DateStr)
    {
        if (!$DateStr) {
            return null;
        }

        if (strlen($DateStr) == 8) { // yyyymmdd
            return substr($DateStr, 0, 4) . '-' . substr($DateStr, 4, 2) . '-' . substr($DateStr, 6, 2);
        } else {
            return null;
        }
    }
}
if (!function_exists('convertThaiDateToIso')) {
    function convertThaiDateToIso(string $thaiDate): ?string
    {
        if (strlen($thaiDate) !== 8 || !is_numeric($thaiDate)) {
            return null; // หรือโยน Exception ตามต้องการ
        }

        $day = substr($thaiDate, 6, 2);
        $month = substr($thaiDate, 4, 2);
        $yearThai = substr($thaiDate, 0, 4);
        $year = $yearThai - 543;

        try {
            $date = \Carbon\Carbon::createFromFormat('Y-m-d', "$year-$month-$day");
            return $date->format('Y-m-d'); // 2024-12-23
        } catch (\Exception $e) {
            return null; // วันที่ไม่ถูกต้อง
        }
    }
}

if (!function_exists('validateDateTimeEN')) {
    function validateDateTimeEN($DateTimeEN)
    {
        try {
            return \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $DateTimeEN);
        } catch (\InvalidArgumentException $th) {
            return null;
        }
    }
}

if (!function_exists('validateDateEN')) {
    function validateDateEN($DateEN)
    {
        try {
            return \Carbon\Carbon::createFromFormat('Y-m-d', $DateEN);
        } catch (\InvalidArgumentException $th) {
            return null;
        }
    }
}

if (!function_exists('sendSMSThaiBulk')) {
    function sendSMSThaiBulk($body = [])
    {
        $apiKey = env('SMS_API_KEY', 'default_value');;
        $apiSecretKey = env('SMS_API_SECRET_KEY', 'default_value');;
        $options = [];

        $token = base64_encode("$apiKey:$apiSecretKey");
        $api = array_key_exists('api', $options) ? $options['api'] : 'https://api-v2.thaibulksms.com';

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL =>  "$api/sms",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($body),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
                'Authorization: Basic ' .  $token
            ),
        ));

        $response = curl_exec($curl);
        $httpStatusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        $resData = json_decode($response);

        $res = (object)[
            'httpStatusCode' => $httpStatusCode
        ];

        if ($httpStatusCode == 201) {
            $res->data = $resData;
        } else {
            $res->error = $resData->error;
        }

        return $res;
    }
}

if (!function_exists('sendSMSMKT')) {
    function sendSMSMKT($body = [])
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://portal-otp.smsmkt.com/api/send-message',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                "Content-Type: application/json",
                "api_key:fd8be9b886a604375f45993123d11755",
                "secret_key:hAo7gU1MYpRmneqO",
            ),
            CURLOPT_POSTFIELDS => json_encode($body),
        ));

        $response = curl_exec($curl);
        $httpStatusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        $resData = json_decode($response);

        $res = (object)[
            'httpStatusCode' => $httpStatusCode
        ];

        if ($httpStatusCode == 200) {
            $res->data = $resData;
        } else {
            dd($resData);
            // $res->error = $resData->error;
        }

        return $res;
    }
}

if (!function_exists('calculateAge')) {
    function calculateAge($birthDate, $referenceDate = null)
    {
        $birth = \Carbon\Carbon::parse($birthDate);
        $reference = $referenceDate ? \Carbon\Carbon::parse($referenceDate) : \Carbon\Carbon::now();

        return $birth->diffInYears($reference);
    }
}
