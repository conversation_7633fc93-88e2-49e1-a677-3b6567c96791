<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class bd_area extends Model
{
    use HasFactory,SoftDeletes;
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];
    
    public function area_codes(){
        return $this->hasMany(data_patient::class, 'area_code','code');
    }
    public function permanent_area_codes(){
        return $this->hasMany(data_patient::class, 'permanent_area_code','code');
    }
}
