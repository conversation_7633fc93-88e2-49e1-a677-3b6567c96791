<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class bd_hospital extends Model
{
    use HasFactory,SoftDeletes;
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];
    
    public function data_patient(){
        return $this->hasMany(data_patient::class,'hospital_code','code');
    }
    public function prop_durables(){
        return $this->hasMany(prop_durables::class,'hospital_code','code');
    }
    public function prop_staffs(){
        return $this->hasMany(prop_staffs::class,'hospital_code','code');
    }
    public function data_base_hospital(){
        return $this->hasMany(data_base_hospital::class,'hospital_code','code');
    }
}
