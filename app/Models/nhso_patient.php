<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class nhso_patient extends Model
{
    use HasFactory,SoftDeletes;
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];
    
    public function nhso_radi(){
        return $this->hasMany(nhso_radi::class,'pid','pid');
    }
    public function nhso_chemo(){
        return $this->hasMany(nhso_chemo::class,'pid','pid');
    }
    public function nhso_waitingtime_tb(){
        return $this->hasMany(nhso_waitingtime_tb::class,'pid','pid');
    }
    public function nhso_first_diag(){
        return $this->hasMany(nhso_first_diag::class,'pid','pid');
    }
    public function nhso_person(){
        return $this->hasMany(nhso_person::class,'pid','pid');
    }
    public function nhso_register(){
        return $this->hasMany(nhso_register::class,'pid','pid');
    }
}
