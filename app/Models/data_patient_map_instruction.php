<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class data_patient_map_instruction extends Model
{
    use HasFactory,SoftDeletes;
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];
    
    public function data_patient(){
        return $this->belongsTo(data_patient::class,'patient_id','id');
    }
    public function bd_sitegroup(){
        return $this->belongsTo(bd_sitegroup::class,'icd10_site_group_id','group_id');
    }
}
