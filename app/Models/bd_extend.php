<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class bd_extend extends Model
{
    use HasFactory,SoftDeletes;
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];
    
    public function data_cancer_summary(){
        return $this->hasMany(data_cancer_summary::class,'extension_code','code');
    }
    public function data_cancer(){
        return $this->hasMany(data_cancer::class,'extension_code','code');
    }
}
