<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class data_cancer_summary extends Model
{
    use HasFactory,SoftDeletes;
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];
    
    public function data_patient(){
        return $this->belongsTo(data_patient::class,'patient_id','id');
    }
    public function bd_topo(){
        return $this->belongsTo(bd_topo::class,'topo_code','code');
    }
    public function bd_t(){
        return $this->belongsTo(bd_t::class,'t_code','code');
    }
    public function bd_stage(){
        return $this->belongsTo(bd_stage::class,'stage_code','code');
    }
    public function bd_n(){
        return $this->belongsTo(bd_n::class,'n_code','code');
    }
    public function bd_m(){
        return $this->belongsTo(bd_m::class,'m_code','code');
    }
    public function bd_diagnosis(){
        return $this->belongsTo(bd_diagnosis::class,'diagnosis_code','code');
    }
    public function bd_behaviour(){
        return $this->belongsTo(bd_behaviour::class,'behaviour_code','code');
    }
    public function bd_grade(){
        return $this->belongsTo(bd_grade::class,'grade_code','code');
    }
    public function bd_lifestatus(){
        return $this->belongsTo(bd_lifestatus::class,'lifestatus_code','code');
    }
    public function bd_finance_support(){
        return $this->belongsTo(bd_finance_support::class,'finance_support_code','code');
    }
    public function bd_extend(){
        return $this->belongsTo(bd_extend::class,'extension_code','code');
    }
    public function bd_mor(){
        return $this->belongsTo(bd_mor::class,'morphology_code','key');
    }
}
