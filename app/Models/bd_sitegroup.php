<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class bd_sitegroup extends Model
{
    use HasFactory,SoftDeletes;
    protected $softDelete = true;
    protected $hidden = ['deleted_at'];
    
    public function data_patient_map_instruction(){
        return $this->hasMany(data_patient_map_instruction::class,'icd10_site_group_id','group_id');
    }
    public function ref_instruction(){
        return $this->hasMany(ref_instruction::class,'icd10_site_group_id','group_id');
    }
    public function data_file(){
        return $this->hasMany(data_file::class,'file_group_id','group_id');
    }
}
