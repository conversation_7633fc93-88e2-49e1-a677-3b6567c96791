<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class LogRequestPayload
{
    // รายการ URL ที่จะไม่ถูก log
    protected $except = [
        'api/auth/login',
    ];

    public function handle(Request $request, Closure $next)
    {
        if (
            in_array($request->method(), ['POST', 'PUT', 'PATCH']) &&
            !$this->inExceptArray($request)
        ) {
            Log::notice('Incoming Request Payload', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'payload' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
        }

        return $next($request);
    }

    protected function inExceptArray(Request $request)
    {
        foreach ($this->except as $except) {
            if ($request->is($except)) {
                return true;
            }
        }

        return false;
    }
}
