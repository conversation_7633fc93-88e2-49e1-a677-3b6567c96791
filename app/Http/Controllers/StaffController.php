<?php

namespace App\Http\Controllers;

use Exception;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Database\Query\JoinClause;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function Store_Or_Update(Request $request)
    {
        $user = Auth::user();
        $input = $request->all();

        // ตรวจสอบข้อมูลที่จำเป็น
        if (!$request->has(['year_id', 'hospital_name_text'])) {
            return response()->json([
                'status' => false,
                'message' => 'กรุณาระบุ year_id และ hospital_name_text',
            ], 400);
        }


        $val_staff = $this->getValStaff($input);

        if ($val_staff['hospital_code'] == '') {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบรหัสสถานพยาบาลที่ระบุ กรุณาเลือกจากตัวเลือกเท่านั้น!',
            ], 422);
        }

        $existingData = DB::table('prop_staffs')
            ->where('year_id', $val_staff['year_id'])
            ->where('hospital_code', $val_staff['hospital_code'])
            ->first();

        $val_staff['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
        $val_staff['updated_by'] = $user->id;

        if ($existingData) { // ถ้ามีข้อมูลเดิม แก้ไขข้อมูล
            DB::table('prop_staffs')
                ->where('id', $existingData->id)
                ->update($val_staff);

            return response()->json([
                'status' => true,
                'message' => 'แก้ไขข้อมูลสำเร็จ.',
                'id' => $existingData->id,
            ], 200);
        } else { // ถ้าไม่มีข้อมูลเดิม เพิ่มข้อมูลใหม่
            $val_staff['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
            $val_staff['created_by'] = $user->id;

            $staff_id = DB::table('prop_staffs')->insertGetId($val_staff);

            return response()->json([
                'status' => true,
                'message' => 'บันทึกข้อมูลสำเร็จ.',
                'id' => $staff_id,
            ], 200);
        }
    }



    /**
     * Display the specified resource.
     */
    public function show(Request $request, $id)
    { {
            $item = DB::table('prop_staffs')
                ->where('prop_staffs.id', $id)
                ->first();
            // dd($item);
            $hospital = DB::table('bd_hospital')
                ->select(['code', 'name'])
                ->where('code', $item->hospital_code)
                ->first();

            $item->hospital_name_text = $hospital->code . ' ' . $hospital->name;

            return response()->json($item, 200, []);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $input = $request->all();

        $val_staff = $this->getValStaff($input);
        // dd($val_staff);
        if ($val_staff['hospital_code'] == '') {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบรหัสสถานพยาบาลที่ระบุ กรุณาเลือกจากตัวเลือกเท่านั้น!'
            ], 422);
        }

        $val_staff['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
        $val_staff['updated_by'] = $user->id;
        // dd($val_staff);
        try {
            DB::table('prop_staffs')->where('id', $id)->update($val_staff);
        } catch (Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        return response()->json([
            'status' => true,
            'message' => 'บันทึกข้อมูลสำเร็จ.',
            'id' => $id
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        DB::table('prop_staffs')->where('id', $id)->delete();

        return response()->json([
            'status' => true,
            'message' => 'ลบข้อมูลสำเร็จ.'
        ], 200);
    }

    private function getValStaff($input)
    {
        $hospital_code = substr(trim($input['hospital_name_text']), 0, 5);
        // dd($hospital_code);
        $hospitals = DB::table('bd_hospital')->where('code', $hospital_code)->first();
        // dd($hospitals);
        $val = [
            'hospital_code' => $hospitals ? $hospital_code : '',
            'year_id'       => $input['year_id'],
            'f_1_1'         => $input['f_1_1'] != '' ? $input['f_1_1'] : null,
            'f_1_2'         => $input['f_1_2'] != '' ? $input['f_1_2'] : null,
            'f_2_1'         => $input['f_2_1'] != '' ? $input['f_2_1'] : null,
            'f_2_2'         => $input['f_2_2'] != '' ? $input['f_2_2'] : null,
            'f_2_3'         => $input['f_2_3'] != '' ? $input['f_2_3'] : null,
            'f_3_1'         => $input['f_3_1'] != '' ? $input['f_3_1'] : null,
            'f_3_2'         => $input['f_3_2'] != '' ? $input['f_3_2'] : null,
            'f_3_3'         => $input['f_3_3'] != '' ? $input['f_3_3'] : null,
            'f_4_1'         => $input['f_4_1'] != '' ? $input['f_4_1'] : null,
            'f_4_2'         => $input['f_4_2'] != '' ? $input['f_4_2'] : null,
            'f_4_3'         => $input['f_4_3'] != '' ? $input['f_4_3'] : null,
            'f_5_1'         => $input['f_5_1'] != '' ? $input['f_5_1'] : null,
            'f_5_2'         => $input['f_5_2'] != '' ? $input['f_5_2'] : null,
            'f_5_3'         => $input['f_5_3'] != '' ? $input['f_5_3'] : null,
            'f_5_4'         => $input['f_5_4'] != '' ? $input['f_5_4'] : null,
            'f_5_5'         => $input['f_5_5'] != '' ? $input['f_5_5'] : null,
            'f_6_1'         => $input['f_6_1'] != '' ? $input['f_6_1'] : null,
            'f_6_2'         => $input['f_6_2'] != '' ? $input['f_6_2'] : null,
            'f_6_3'         => $input['f_6_3'] != '' ? $input['f_6_3'] : null,
            'f_6_4'         => $input['f_6_4'] != '' ? $input['f_6_4'] : null,
            'f_6_5'         => $input['f_6_5'] != '' ? $input['f_6_5'] : null,
            'f_6_6'         => $input['f_6_6'] != '' ? $input['f_6_6'] : null,
            'f_6_7'         => $input['f_6_7'] != '' ? $input['f_6_7'] : null,
            'f_6_8'         => $input['f_6_8'] != '' ? $input['f_6_8'] : null,
            'f_6_9'         => $input['f_6_9'] != '' ? $input['f_6_9'] : null,
            'f_6_10'        => $input['f_6_10'] != '' ? $input['f_6_10'] : null,
            'f_7_1'         => $input['f_7_1'] != '' ? $input['f_7_1'] : null,
            'f_7_2'         => $input['f_7_2'] != '' ? $input['f_7_2'] : null,
            'f_7_3'         => $input['f_7_3'] != '' ? $input['f_7_3'] : null,
            'f_7_4'         => $input['f_7_4'] != '' ? $input['f_7_4'] : null,
            'f_7_5'         => $input['f_7_5'] != '' ? $input['f_7_5'] : null,
        ];

        return $val;
    }

    public function getReport(Request $request)
    {
        $year_id = $request->query('year_id');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');

        if (!$year_id)
        {
            return response()->json([
                'data' => [],
            ]);
        }

        $fields = [
            (object)['field' => DB::raw('sum(prop_staffs.f_1_1) as f_1_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_1_2) as f_1_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_2_1) as f_2_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_2_2) as f_2_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_2_3) as f_2_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_3_1) as f_3_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_3_2) as f_3_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_3_3) as f_3_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_4_1) as f_4_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_4_2) as f_4_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_4_3) as f_4_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_1) as f_5_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_2) as f_5_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_3) as f_5_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_4) as f_5_4')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_5) as f_5_5')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_1) as f_6_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_2) as f_6_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_3) as f_6_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_4) as f_6_4')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_5) as f_6_5')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_6) as f_6_6')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_7) as f_6_7')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_8) as f_6_8')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_9) as f_6_9')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_10) as f_6_10')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_1) as f_7_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_2) as f_7_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_3) as f_7_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_4) as f_7_4')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_5) as f_7_5')],
        ];

        $data_table = null;

        // กำหนดการดึงข้อมูลตามระดับพื้นที่ (area_level_id)
        switch ($area_level_id) {
            case 1: // ระดับเขตสุขภาพ
                $data_table = DB::table('ref_health_regions')
                    ->select(array_merge(
                        [
                            DB::raw('ref_health_regions.id AS id'),
                            DB::raw("CONCAT('เขต ', ref_health_regions.id) AS name"),
                        ],
                        array_map(fn($d) => $d->field, $fields)
                    ))
                    ->leftJoin('bd_hospital', 'ref_health_regions.id', '=', 'bd_hospital.health_region_id')
                    ->leftJoin('prop_staffs', function (JoinClause $join) use ($year_id) {
                        $join->on('prop_staffs.hospital_code', '=', 'bd_hospital.code')
                            ->where('prop_staffs.year_id', $year_id);
                    })
                    ->groupBy('ref_health_regions.id', DB::raw("CONCAT('เขต ', ref_health_regions.id)"))
                    ->orderBy('ref_health_regions.id')
                    ->get();
                break;

            case 2: // ระดับจังหวัด
            case 3: // ระดับอำเภอ
                $data_table = DB::table('prop_staffs')
                    ->select(array_merge(
                        [
                            DB::raw('bd_hospital.code AS id'),
                            DB::raw("bd_hospital.name AS name"),
                        ],
                        array_map(fn($d) => $d->field, $fields)
                    ))
                    ->leftJoin('bd_hospital', 'prop_staffs.hospital_code', '=', 'bd_hospital.code')
                    ->where(function ($query) use ($area_level_id, $area_id, $year_id) {
                        if ($area_level_id == 2) {
                            $query->where('bd_hospital.health_region_id', $area_id)
                                ->where('prop_staffs.year_id', $year_id);
                        } elseif ($area_level_id == 3) {
                            $query->where('bd_hospital.province_id', $area_id)
                                ->where('prop_staffs.year_id', $year_id);
                        }
                    })
                    ->groupBy('bd_hospital.code', 'bd_hospital.name')
                    ->orderBy('bd_hospital.code')
                    ->get();
                break;

            default:
                return response()->json(['error' => 'Invalid area_level_id'], 400);
        }

        // ส่งผลลัพธ์กลับ
        return response()->json([
            'data' => $data_table,
        ]);
    }

    public function Cancer_personnel_information(Request $request)
    {
        $year_id = $request->query('year_id');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');

        if (!$year_id)
        {
            return response()->json([
                'data' => [],
            ]);
        }

        $fields = [
            (object)['field' => DB::raw('sum(prop_staffs.f_1_1) as f_1_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_1_2) as f_1_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_2_1) as f_2_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_2_2) as f_2_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_2_3) as f_2_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_3_1) as f_3_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_3_2) as f_3_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_3_3) as f_3_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_4_1) as f_4_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_4_2) as f_4_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_4_3) as f_4_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_1) as f_5_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_2) as f_5_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_3) as f_5_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_4) as f_5_4')],
            (object)['field' => DB::raw('sum(prop_staffs.f_5_5) as f_5_5')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_1) as f_6_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_2) as f_6_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_3) as f_6_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_4) as f_6_4')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_5) as f_6_5')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_6) as f_6_6')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_7) as f_6_7')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_8) as f_6_8')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_9) as f_6_9')],
            (object)['field' => DB::raw('sum(prop_staffs.f_6_10) as f_6_10')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_1) as f_7_1')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_2) as f_7_2')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_3) as f_7_3')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_4) as f_7_4')],
            (object)['field' => DB::raw('sum(prop_staffs.f_7_5) as f_7_5')],
        ];

        $data_table = null;

        switch ($area_level_id) {
            case 1: // เขตสุขภาพ
            case 2: // จังหวัด
            case 3: // อำเภอ
                $data_table = DB::table('ref_health_regions')
                    ->select(array_merge(
                        [
                            DB::raw('ref_health_regions.health_region_name AS health_region_name'),
                            DB::raw('ref_provinces.province_name_th AS province_name'),
                            DB::raw('bd_hospital.code AS hospital_code'),
                            DB::raw('bd_hospital.name AS hospital_name'),
                            DB::raw('prop_staffs.year_id AS year_id'),
                        ],
                        array_map(fn($d) => $d->field, $fields),
                        [
                            DB::raw('prop_staffs.created_at AS created_at'),
                            DB::raw('users.name AS created_by_user'),
                            DB::raw('prop_staffs.updated_at AS updated_at'),
                            DB::raw('users.name AS updated_by_user'),
                        ]
                    ))
                    ->leftJoin('bd_hospital', 'ref_health_regions.id', '=', 'bd_hospital.health_region_id')
                    ->where('bd_hospital.much_keyin', '=', 1)
                    ->leftJoin('ref_provinces', 'bd_hospital.province_id', '=', 'ref_provinces.id')
                    ->leftJoin('prop_staffs', function (JoinClause $join) use ($year_id) {
                        $join->on('prop_staffs.hospital_code', '=', 'bd_hospital.code')
                            ->where('prop_staffs.year_id', $year_id);
                    })
                    ->leftJoin('users', 'prop_staffs.created_by', '=', 'users.id')
                    ->when($area_level_id == 2, function ($query) use ($area_id) { // เงื่อนไขระดับจังหวัด
                        return $query->where('bd_hospital.health_region_id', '=', $area_id);
                    })
                    ->when($area_level_id == 3, function ($query) use ($area_id) { // เงื่อนไขระดับอำเภอ
                        return $query->where('bd_hospital.province_id', '=', $area_id);
                    })
                    ->groupBy(
                        'ref_health_regions.id',
                        'ref_health_regions.health_region_name',
                        'ref_provinces.province_name_th',
                        'bd_hospital.code',
                        'bd_hospital.name',
                        'prop_staffs.year_id',
                        'users.name',
                        'prop_staffs.created_at',
                        'prop_staffs.updated_at'
                    )
                    ->orderBy('ref_health_regions.id', 'ASC')
                    ->orderBy('ref_provinces.id', 'ASC')
                    ->get();

                break;

            default:
                return response()->json(['error' => 'Invalid area_level_id'], 400);
        }

        return response()->json([
            'data' => $data_table,
        ]);
    }


    private function getStaff($input)
    {
        $dataset = DB::table('prop_staffs')
            ->select([
                'prop_staffs.id',
                'prop_staffs.year_id',
                'prop_staffs.hospital_code',
                DB::raw('bd_hospital.name as hospital_name'),
                DB::raw('ref_health_regions.health_region_name'),
                DB::raw("concat('จ.', ref_provinces.province_name_th) as province_name"),
                'prop_staffs.created_at',
                'prop_staffs.updated_at',
                'prop_staffs.f_1_1',
                'prop_staffs.f_1_2',
                'prop_staffs.f_2_1',
                'prop_staffs.f_2_2',
                'prop_staffs.f_2_3',
                'prop_staffs.f_3_1',
                'prop_staffs.f_3_2',
                'prop_staffs.f_3_3',
                'prop_staffs.f_4_1',
                'prop_staffs.f_4_2',
                'prop_staffs.f_4_3',
                'prop_staffs.f_5_1',
                'prop_staffs.f_5_2',
                'prop_staffs.f_5_3',
                'prop_staffs.f_5_4',
                'prop_staffs.f_5_5',
                'prop_staffs.f_6_1',
                'prop_staffs.f_6_2',
                'prop_staffs.f_6_3',
                'prop_staffs.f_6_4',
                'prop_staffs.f_6_5',
                'prop_staffs.f_6_6',
                'prop_staffs.f_6_7',
                'prop_staffs.f_6_8',
                'prop_staffs.f_6_9',
                'prop_staffs.f_6_10',
                'prop_staffs.f_7_1',
                'prop_staffs.f_7_2',
                'prop_staffs.f_7_3',
                'prop_staffs.f_7_4',
                'prop_staffs.f_7_5',
                DB::raw("concat(uc.name, ' ', uc.lastName) as created_by_name"),
                DB::raw("concat(uu.name, ' ', uu.lastName) as updated_by_name"),
            ])
            ->leftJoin('bd_hospital', 'prop_staffs.hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('ref_health_regions', 'bd_hospital.health_region_id', '=', 'ref_health_regions.id')
            ->leftJoin('ref_provinces', 'bd_hospital.province_id', '=', 'ref_provinces.id')
            ->leftJoin(DB::raw('users uc'), 'prop_staffs.created_by', '=', DB::raw('uc.id'))
            ->leftJoin(DB::raw('users uu'), 'prop_staffs.updated_by', '=', DB::raw('uu.id'))
            ->orderBy('prop_staffs.year_id', 'desc')
            ->orderBy('bd_hospital.health_region_id')
            ->orderBy('bd_hospital.province_id')
            ->orderBy('prop_staffs.hospital_code')
            ->get();

        return [
            'dataset' => $dataset
        ];
    }

    public function getHospitalList()
    {
        $hospitals = DB::table('bd_hospital')
            ->select(['code', 'name'])
            ->whereNotIn('type', [
                '02-สำนักงานสาธารณสุขอำเภอ',
                '03-สถานีอนามัย',
                '04-สถานบริการสาธารณสุขชุมชน',
                '08-ศูนย์สุขภาพชุมชน ของ รพ.',
                '10-ศูนย์วิชาการ',
                '13-ศูนย์บริการสาธารณสุข',
                '16-คลินิกเอกชน',
                '18-โรงพยาบาลส่งเสริมสุขภาพตำบล'
            ])
            ->get();

        $result = [];
        foreach ($hospitals as $hos) {
            $result[$hos->code] = $hos->code . ' ' . $hos->name;
        }

        return $result;
    }
}
