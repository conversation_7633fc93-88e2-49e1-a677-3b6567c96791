<?php

namespace App\Http\Controllers;

use App\Models\Otp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class UserController extends Controller
{
    public function createUser(Request $request)
    {
        // Validate input
        $validator = Validator::make($request->all(), [
            // 'name' => 'required|string|max:255',
            // 'lastName' => 'nullable|string|max:255',
            'username' => 'required|string|max:105|unique:users,username',
            'password' => 'required|string',
            // 'email' => 'nullable|email|max:255|unique:users,email',
            // 'phoneNumber' => 'nullable|string|regex:/^(\+?\d{1,3}[- ]?)?\d{10}$/|unique:users,phoneNumber',
            'hosCode' => 'nullable|string|max:255',
            'hosName' => 'nullable|string|max:255',
            // 'hospitalPosition' => 'nullable|string|max:255',
            // 'hospitalDepartment' => 'nullable|string|max:255',
            'roleCaw' => 'nullable|in:Y,N',
            'roleCancer' => 'nullable|in:Y,N',
            'roleRefer' => 'nullable|in:Y,N',
            'roleProvince' => 'nullable|in:Y,N',
            'roleHealthRegion' => 'nullable|in:Y,N',
            'roleAdmin' => 'nullable|in:Y,N',
            // 'roleSuperAdmin' => 'nullable|in:Y,N',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $user = Auth::user();

        // Save user
        try {
            DB::table('users')->insert([
                'name' => $request->name,
                'lastName' => $request->lastName,
                'username' => $request->username,
                'password' => Hash::make($request->password),
                'email' => $request->email,
                'phoneNumber' => $request->phoneNumber,
                'hosCode' => $request->hosCode,
                'hosName' => $request->hosName,
                'hospitalPosition' => $request->hospitalPosition,
                'hospitalDepartment' => $request->hospitalDepartment,
                'active' => 'Y',
                'completePassword' => 'N',
                'completeInformation' => 'N',
                'completeOTPConfirm' => 'N',
                'created_at' => now(),
                'updated_at' => now(),
                'roleCaw' => $request->roleCaw ?? 'N',
                'roleCancer' => $request->roleCancer ?? 'N',
                'roleRefer' => $request->roleRefer ?? 'N',
                'roleProvince' => $request->roleProvince ?? 'N',
                'roleHealthRegion' => $request->roleHealthRegion ?? 'N',
                'roleSuperAdmin' => 'N',
                'roleAdmin' => $request->roleAdmin ?? 'N',
            ]);

            return response()->json(['status' => true, 'message' => 'User created successfully!']);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to save user: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function login(Request $request)
    {
        // Validate input
        $request->validate([
            'username' => 'required|string|max:105',
            'password' => 'required|string',
        ]);

        // Find user by username
        $user = DB::table('users')->where('username', $request->username)->first();

        if (!$user) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid username or password.',
            ], 401);
        }

        // Check password
        if ($user->password !== md5($request->password)) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid username or password.',
            ], 401);
        }

        // Check if OTP confirmation is required
        if (is_null($user->completeOTPConfirm)) {
            return response()->json([
                'status' => false,
                'message' => 'Please confirm your OTP before proceeding.',
            ], 403);
        }

        // Check if password update is required
        if ($user->completePassword === 'N') {
            return response()->json([
                'status' => true,
                'message' => 'Please update your password.',
            ], 200);
        }

        // Successful login
        return response()->json([
            'status' => true,
            'message' => 'Login successful.',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'lastName' => $user->lastName,
                'username' => $user->username,
                'email' => $user->email,
                'phoneNumber' => $user->phoneNumber,
                'roleCaw' => $user->roleCaw,
                'roleCancer' => $user->roleCancer,
                'roleRefer' => $user->roleRefer,
                'roleProvince' => $user->roleProvince,
                'roleHealthRegion' => $user->roleHealthRegion,
                'roleSuperAdmin' => $user->roleSuperAdmin,
                'roleAdmin' => $user->roleAdmin
            ],
        ]);
    }

    public function resetPassword(Request $request)
    {
        // Validate input
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|exists:users,username', // ตรวจสอบว่ามี username อยู่ในระบบ
            'new_password' => 'required|string|confirmed',  // ต้องการ new_password และ new_password_confirmation
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors(),
            ], 422);
        }

        try {
            // Find the user by username
            $user = DB::table('users')->where('username', $request->username)->first();

            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'User not found.',
                ], 404);
            }

            // Update password
            DB::table('users')->where('username', $request->username)->update([
                'password' => Hash::make($request->new_password), // เข้ารหัสรหัสผ่านใหม่
                'completePassword' => 'Y', // อัปเดตสถานะว่าเปลี่ยนรหัสผ่านแล้ว
                'updated_at' => now(),
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Password has been reset successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to reset password: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function updateUser(Request $request, $id)
    {
        // Validate input
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'lastName' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255|unique:users,email,' . $id, // ตรวจสอบว่า email ไม่ซ้ำ ยกเว้นของ user นี้
            'phoneNumber' => 'nullable|string|max:255',
            'hosCode' => 'nullable|string|max:255',
            'hosName' => 'nullable|string|max:255',
            'hospitalPosition' => 'nullable|string|max:255',
            'hospitalDepartment' => 'nullable|string|max:255',
            'roleCaw' => 'nullable|string|in:Y,N',
            'roleCancer' => 'nullable|string|in:Y,N',
            'roleRefer' => 'nullable|string|in:Y,N',
            'roleProvince' => 'nullable|string|in:Y,N',
            'roleHealthRegion' => 'nullable|string|in:Y,N',
            'roleAdmin' => 'nullable|string|in:Y,N',
            // 'roleSuperAdmin' => 'nullable|string|in:Y,N',
            'active' => 'nullable|string|in:Y,N',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors(),
            ], 422);
        }

        try {
            // ค้นหา user ตาม id
            $user = DB::table('users')->where('id', $id)->first();

            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'User not found.',
                ], 404);
            }

            // อัปเดตข้อมูลผู้ใช้
            DB::table('users')->where('id', $id)->update([
                'name' => $request->name,
                'lastName' => $request->lastName,
                'email' => $request->email,
                'phoneNumber' => $request->phoneNumber,
                'hosCode' => $request->hosCode,
                'hosName' => $request->hosName,
                'hospitalPosition' => $request->hospitalPosition,
                'hospitalDepartment' => $request->hospitalDepartment,
                'roleCaw' => $request->roleCaw,
                'roleCancer' => $request->roleCancer,
                'roleRefer' => $request->roleRefer,
                'roleProvince' => $request->roleProvince,
                'roleHealthRegion' => $request->roleHealthRegion,
                'roleAdmin' => $request->roleAdmin,
                // 'roleSuperAdmin' => $request->roleSuperAdmin,
                'active' => $request->active,
                'updated_at' => now(),
            ]);

            return response()->json([
                'status' => true,
                'message' => 'User information updated successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update user: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function deleteUser($id)
    {
        try {
            // ค้นหา user ตาม id
            $user = DB::table('users')->where('id', $id)->first();

            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'User not found.',
                ], 404);
            }

            // ลบข้อมูลผู้ใช้
            DB::table('users')->where('id', $id)->delete();

            return response()->json([
                'status' => true,
                'message' => 'User deleted successfully.',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete user: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function getUserById($id)
    {
        try {
            // ค้นหาผู้ใช้ตาม id
            $user = DB::table('users')->where('id', $id)->first();

            // ตรวจสอบว่าไม่พบผู้ใช้
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'User not found.',
                ], 404);
            }

            // ซ่อนรหัสผ่านก่อนส่งข้อมูล
            unset($user->password);

            // ส่งข้อมูลผู้ใช้กลับ
            return response()->json([
                'status' => true,
                'data' => $user,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve user: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function userDataTable(Request $request)
    {
        $user    = Auth::user();
        $input = $request->all();
        $perPage = $request->input('length', 10);  // จำนวนข้อมูลต่อหน้า
        $page = $request->input('start', 0);       // หน้าที่เริ่มต้น
        $search = $request->input('search', '');   // คำค้นหาจาก DataTables

        // คอลัมน์ที่สามารถค้นหาได้
        $searchable = [
            'users.username',
            'users.hosName',
            DB::raw("concat(users.name, ' ', users.lastName)"),
            'users.phoneNumber',
            'users.hospitalDepartment',
            'users.hospitalPosition'
        ];

        // ตัวแปร filter ที่รับมาจาก payload
        $filters = $request->input('filter', []);

        // การกรองตามค่าใน filter
        $activeFilter = isset($filters['active']) ? $filters['active'] : null;
        $rolesFilter = isset($filters['roles']) ? $filters['roles'] : [];
        $hosCodeFilter = isset($filters['hosCode']) ? $filters['hosCode'] : null;

        // การจัดเรียงข้อมูล
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        // การดึงข้อมูลจากตาราง `users`
        $dataset = DB::table('users')->select([
            'users.id',
            'users.username',
            'users.hosName',
            'users.hospitalDepartment',
            'users.hospitalPosition',
            DB::raw("concat(users.name, ' ', users.lastName) as full_name"),
            'users.phoneNumber',
            'users.active',
            'users.created_at',
            'users.updated_at',
            DB::raw('null as action'),
        ])
            ->when($hosCodeFilter, function ($query, $hosCodeFilter) {
                return $query->where('users.hosCode', $hosCodeFilter);
            })
            ->when($activeFilter, function ($query, $activeFilter) {
                return $query->where('users.active', $activeFilter);
            })
            ->when(!empty($rolesFilter), function ($query) use ($rolesFilter) {
                foreach ($rolesFilter as $role) {
                    $query->where('users.' . $role, 'Y');
                }
            })
            ->where(function ($query) use ($searchable, $search) {
                if (isset($search['value']) && !empty($search['value'])) {
                    foreach ($searchable as $field) {
                        $query->orWhere($field, 'LIKE', '%' . $search['value'] . '%');
                    }
                }
            })
            ->when($user->roleSuperAdmin != 'Y', function ($query) use ($user) {
                $query->where('users.hosCode', '=', $user->hosCode);
            })
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])  // การจัดเรียง
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);  // การแบ่งหน้า

        // ส่งข้อมูลกลับในรูปแบบที่ DataTables ต้องการ
        return $dataset;
    }

    public function verifyOtp(Request $request)
    {
        $refno = $request->input('refno');
        $otp = $request->input('otp');

        $fine_token = Otp::where('refno', $refno)->whereNull('deleted_at')->orderBy('created_at', 'desc')->first();
        if (!$fine_token) {
            return response()->json(['error' => 'ไม่พบข้อมูล RefNo '], 400);
        }

        if ($fine_token->expiration_date <= date('Y-m-d H:i:s')) {
            return response()->json(['message' => 'รหัส OTP หมดอายุ'], 400);
        }

        // ตรวจสอบว่า OTP ถูกต้อง
        if ($fine_token->pin !== $otp) {
            return response()->json(['message' => 'Invalid OTP.'], 400);
        }

        DB::table('otps')
            ->where('id', $fine_token->id)
            ->update([
                'deleted_at' => Carbon::now(),
            ]);

        return response()->json([
            'status'  => true,
            'message' => 'OTP verified successfully.'
        ]);
    }

    public function forceChangePassword(Request $request, $id)
    {
        DB::table('users')->where('id', $id)->update([
            'password' => Hash::make('admin'),
            'completePassword' => 'N',
            'completeInformation' => 'N',
            'completeOTPConfirm' => 'N',
        ]);

        return response()->json([
            'status'  => true,
            'message' => 'Password changed successfully.'
        ]);
    }
}
