<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Exports\ReportStageExport;
use App\Exports\Rp01Export;
use App\Exports\AllDocExport;
use App\Exports\ExportCancerDetail;
use App\Exports\WaitingTime;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ForwardExport;
use App\Exports\Refuse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ReportController extends Controller
{
    public function reportStage(Request $request)
    {
        $input = $request->all();

        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $gender_code = $request->query('gender_code');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('null AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('null AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('null AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    // ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return  $query->where('data_patient.sex_code', $gender_code);
                    })
                    // ->when(isset($area_id), function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', $area_id);
                    // })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();

                break;

            case 2:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('null AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('null AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('null AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    // ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return  $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();

                break;

            case 3:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('null AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('null AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('null AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    // ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return  $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.province_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();

                break;

            case 4:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('null AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('null AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('null AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    // ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return  $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('data_cancer_summary.hospital_code', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();

                break;

            default:
                # code...
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json([
            'data' => $results
        ], 200, []);
    }

    public function reportStagePop(Request $request)
    {
        $input = $request->all();

        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $gender_code = $request->query('gender_code');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('null AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('null AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('null AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    // ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return  $query->where('data_patient.sex_code', $gender_code);
                    })
                    // ->when(isset($area_id), function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', $area_id);
                    // })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();

                break;

            case 2:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('null AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('null AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('null AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('ref_provinces as prov', 'prov.id', '=', 'data_patient.address_province_id')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return  $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('prov.health_region_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();

                break;

            case 3:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('null AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('null AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('null AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    // ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return  $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('data_patient.address_province_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();

                break;

            case 4:
                $results = DB::table('bd_stage_group')
                    ->select(
                        'bd_stage_group.code',
                        'bd_stage_group.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('null AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('null AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('null AS total_percentage')
                    )
                    ->leftJoin('bd_stage', 'bd_stage_group.code', '=', 'bd_stage.stage_group_id')
                    ->leftJoin('data_cancer_summary', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    // ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return  $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('data_cancer_summary.hospital_code', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('bd_stage_group.code', 'bd_stage_group.name')
                    ->orderBy('bd_stage_group.code')
                    ->get();

                break;

            default:
                # code...
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json([
            'data' => $results
        ], 200, []);
    }

    public function rp01(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'hr.id as code',
                        'hr.health_region_name as name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_hospital as hos', 's.hospital_code', '=', 'hos.code')
                    ->leftJoin('ref_health_regions as hr', 'hr.id', '=', 'hos.health_region_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code');

                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('hr.id', 'hr.health_region_name')
                    ->get();

                break;

            case 2:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'prov.id as code',
                        'prov.province_name_th as name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_hospital as hos', 's.hospital_code', '=', 'hos.code')
                    ->leftJoin('ref_provinces as prov', 'prov.id', '=', 'hos.province_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code');

                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('prov.id', 'prov.province_name_th')
                    ->get();

                break;

            case 3:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'hos.code as code',
                        'hos.name as name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_hospital as hos', 's.hospital_code', '=', 'hos.code')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.province_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code');

                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('hos.code', 'hos.name')
                    ->get();

                break;

            case 4:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'hos.code',
                        'hos.name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage'),
                        DB::raw("(
                            SELECT COUNT(*) 
                            FROM data_cancer_summary AS sub 
                            WHERE sub.hospital_code = s.hospital_code 
                              AND sub.behaviour_code = 3
                        ) AS all_count")
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_hospital as hos', 's.hospital_code', '=', 'hos.code')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.code', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code');

                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('hos.code', 'hos.name')
                    ->get();

                break;

            default:
                # code...
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json([
            'data' => $results
        ], 200, []);
    }

    public function rp01Pop(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'hr.id as code',
                        'hr.health_region_name as name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_hospital as hos', 's.hospital_code', '=', 'hos.code')
                    ->leftJoin('ref_health_regions as hr', 'hr.id', '=', 'hos.health_region_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code');

                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('hr.id', 'hr.health_region_name')
                    ->get();

                break;

            case 2:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'prov.id as code',
                        'prov.province_name_th as name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('ref_provinces as prov', 'prov.id', '=', 'p.address_province_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('prov.health_region_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code');

                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('prov.id', 'prov.province_name_th')
                    ->get();

                break;

            case 3:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'hos.code as code',
                        'hos.name as name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_hospital as hos', 's.hospital_code', '=', 'hos.code')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('p.address_province_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code');

                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('hos.code', 'hos.name')
                    ->get();

                break;

            case 4:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'hos.code',
                        'hos.name',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage'),
                        DB::raw("(
                            SELECT COUNT(*) 
                            FROM data_cancer_summary AS sub 
                            WHERE sub.hospital_code = s.hospital_code 
                              AND sub.behaviour_code = 3
                        ) AS all_count")
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_hospital as hos', 's.hospital_code', '=', 'hos.code')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.code', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        $query->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code');

                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy('hos.code', 'hos.name')
                    ->get();

                break;

            default:
                # code...
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json([
            'data' => $results
        ], 200, []);
    }

    public function rp01ExcelDetail(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = DB::table('data_cancer_summary', 's')
            ->select(
                'p.hospital_code',
                'p.hn_no',
                't.name as title_name',
                'p.name',
                'p.last_name',
                'p.cid',
                'p.birth_date',
                'sex.name as sex_name',
                'n.name as nationality_name',
                'p.death_date',
                'dc.name as deathcause_name',
                'p.address_no',
                'p.address_moo',
                'ref_sub_districts.sub_district_name_th as address_sub_district_name',
                'ref_districts.district_name_th as address_district_name',
                'ref_provinces.province_name_th as address_province_name',
                'p.address_zipcode',
                'p.permanent_address_no',
                'p.permanent_address_moo',
                'psd.sub_district_name_th as permanent_address_sub_district_name',
                'pd.district_name_th as permanent_address_district_name',
                'pp.province_name_th as permanent_address_province_name',
                'p.permanent_address_zipcode',
                'p.telephone_1',
                'p.telephone_2',
                's.first_entrance_date',
                's.finance_support_text',
                's.diagnosis_date',
                's.age',
                's.diagnosis_text',
                's.diagnosis_out',
                's.excision_in_cut_date',
                's.excision_in_read_date',
                's.topo_text',
                's.recurrent',
                's.recurrent_date',
                's.morphology_text',
                's.behaviour_text',
                's.grade_text',
                'bd_t.name as t_name',
                'bd_n.name as n_name',
                'bd_m.name as m_name',
                's.tnm_date',
                'st.name as stage_name',
                'bd_extend.name as extend_name',
                DB::raw("concat(sg.group_text, ' (', sg.group_desc, ')') as group_name"),
                's.icd10_code',
                's.icd10_text',
                's.met_1',
                's.met_1_date',
                's.met_2',
                's.met_2_date',
                's.met_3',
                's.met_3_date',
                's.met_4',
                's.met_4_date',
                's.met_5',
                's.met_5_date',
                's.met_6',
                's.met_6_date',
                's.met_7',
                's.met_7_date',
                's.met_7_other',
                's.clinical_summary',
            )
            ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
            ->leftJoin('bd_title as t', 'p.title_code', '=', 't.code')
            ->leftJoin('bd_sex as sex', 'p.sex_code', '=', 'sex.code')
            ->leftJoin('bd_national as n', 'p.nationality_code', '=', 'n.code')
            ->leftJoin('bd_deathcause as dc', 'p.deathcause_code', '=', 'dc.code')
            ->leftJoin('ref_sub_districts', 'ref_sub_districts.id', '=', 'p.address_sub_district_id')
            ->leftJoin('ref_districts', 'ref_districts.id', '=', 'p.address_district_id')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', 'p.address_province_id')
            ->leftJoin('ref_sub_districts as psd', 'psd.id', '=', 'p.permanent_address_sub_district_id')
            ->leftJoin('ref_districts as pd', 'pd.id', '=', 'p.permanent_address_district_id')
            ->leftJoin('ref_provinces as pp', 'pp.id', '=', 'p.permanent_address_province_id')
            ->leftJoin('bd_t', 's.t_code', '=', 'bd_t.code')
            ->leftJoin('bd_n', 's.n_code', '=', 'bd_n.code')
            ->leftJoin('bd_m', 's.m_code', '=', 'bd_m.code')
            ->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code')
            ->leftJoin('bd_extend', 's.extension_code', '=', 'bd_extend.code')
            ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
            ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
            ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                    $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $query->where('s.behaviour_code', 3);
                }
            })
            ->when(isset($gender_code), function ($query) use ($gender_code) {
                return $query->where('p.sex_code', $gender_code);
            })
            ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                return $query->whereIn('s.icd10_group_id', $icd10_group_id);
            })
            ->when(isset($area_id), function ($query) use ($area_id) {
                return $query->where('s.hospital_code', $area_id);
            })
            ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
            })
            ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
            })
            ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                return $query->whereBetween('s.age', [$age_start, $age_end]);
            })
            ->when((isset($recurent)), function ($query) use ($recurent) {
                if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                    return $query->where('s.recurrent', 1);
                }
            })
            ->when((isset($deathcase)), function ($query) use ($deathcase) {
                if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                    return $query->whereNotNull('p.death_date');
                }
            })
            ->get();

        $date = date('d-m-Y');

        return Excel::download(new ExportCancerDetail($results), 'rp01_' . $date . '.xlsx');
    }

    public function rp01ExcelDetailPop(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $data_type = $request->query('data_type');

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'p.hospital_code',
                        'p.hn_no',
                        't.name as title_name',
                        'p.name',
                        'p.last_name',
                        'p.cid',
                        'p.birth_date',
                        'sex.name as sex_name',
                        'n.name as nationality_name',
                        'p.death_date',
                        'dc.name as deathcause_name',
                        'p.address_no',
                        'p.address_moo',
                        'ref_sub_districts.sub_district_name_th as address_sub_district_name',
                        'ref_districts.district_name_th as address_district_name',
                        'ref_provinces.province_name_th as address_province_name',
                        'p.address_zipcode',
                        'p.permanent_address_no',
                        'p.permanent_address_moo',
                        'psd.sub_district_name_th as permanent_address_sub_district_name',
                        'pd.district_name_th as permanent_address_district_name',
                        'pp.province_name_th as permanent_address_province_name',
                        'p.permanent_address_zipcode',
                        'p.telephone_1',
                        'p.telephone_2',
                        's.first_entrance_date',
                        's.finance_support_text',
                        's.diagnosis_date',
                        's.age',
                        's.diagnosis_text',
                        's.diagnosis_out',
                        's.excision_in_cut_date',
                        's.excision_in_read_date',
                        's.topo_text',
                        's.recurrent',
                        's.recurrent_date',
                        's.morphology_text',
                        's.behaviour_text',
                        's.grade_text',
                        'bd_t.name as t_name',
                        'bd_n.name as n_name',
                        'bd_m.name as m_name',
                        's.tnm_date',
                        'st.name as stage_name',
                        'bd_extend.name as extend_name',
                        DB::raw("concat(sg.group_text, ' (', sg.group_desc, ')') as group_name"),
                        's.icd10_code',
                        's.icd10_text',
                        's.met_1',
                        's.met_1_date',
                        's.met_2',
                        's.met_2_date',
                        's.met_3',
                        's.met_3_date',
                        's.met_4',
                        's.met_4_date',
                        's.met_5',
                        's.met_5_date',
                        's.met_6',
                        's.met_6_date',
                        's.met_7',
                        's.met_7_date',
                        's.met_7_other',
                        's.clinical_summary',
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_title as t', 'p.title_code', '=', 't.code')
                    ->leftJoin('bd_sex as sex', 'p.sex_code', '=', 'sex.code')
                    ->leftJoin('bd_national as n', 'p.nationality_code', '=', 'n.code')
                    ->leftJoin('bd_deathcause as dc', 'p.deathcause_code', '=', 'dc.code')
                    ->leftJoin('ref_sub_districts', 'ref_sub_districts.id', '=', 'p.address_sub_district_id')
                    ->leftJoin('ref_districts', 'ref_districts.id', '=', 'p.address_district_id')
                    ->leftJoin('ref_provinces', 'ref_provinces.id', '=', 'p.address_province_id')
                    ->leftJoin('ref_sub_districts as psd', 'psd.id', '=', 'p.permanent_address_sub_district_id')
                    ->leftJoin('ref_districts as pd', 'pd.id', '=', 'p.permanent_address_district_id')
                    ->leftJoin('ref_provinces as pp', 'pp.id', '=', 'p.permanent_address_province_id')
                    ->leftJoin('bd_t', 's.t_code', '=', 'bd_t.code')
                    ->leftJoin('bd_n', 's.n_code', '=', 'bd_n.code')
                    ->leftJoin('bd_m', 's.m_code', '=', 'bd_m.code')
                    ->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code')
                    ->leftJoin('bd_extend', 's.extension_code', '=', 'bd_extend.code')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->get();
                break;

            case 2:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'p.hospital_code',
                        'p.hn_no',
                        't.name as title_name',
                        'p.name',
                        'p.last_name',
                        'p.cid',
                        'p.birth_date',
                        'sex.name as sex_name',
                        'n.name as nationality_name',
                        'p.death_date',
                        'dc.name as deathcause_name',
                        'p.address_no',
                        'p.address_moo',
                        'ref_sub_districts.sub_district_name_th as address_sub_district_name',
                        'ref_districts.district_name_th as address_district_name',
                        'ref_provinces.province_name_th as address_province_name',
                        'p.address_zipcode',
                        'p.permanent_address_no',
                        'p.permanent_address_moo',
                        'psd.sub_district_name_th as permanent_address_sub_district_name',
                        'pd.district_name_th as permanent_address_district_name',
                        'pp.province_name_th as permanent_address_province_name',
                        'p.permanent_address_zipcode',
                        'p.telephone_1',
                        'p.telephone_2',
                        's.first_entrance_date',
                        's.finance_support_text',
                        's.diagnosis_date',
                        's.age',
                        's.diagnosis_text',
                        's.diagnosis_out',
                        's.excision_in_cut_date',
                        's.excision_in_read_date',
                        's.topo_text',
                        's.recurrent',
                        's.recurrent_date',
                        's.morphology_text',
                        's.behaviour_text',
                        's.grade_text',
                        'bd_t.name as t_name',
                        'bd_n.name as n_name',
                        'bd_m.name as m_name',
                        's.tnm_date',
                        'st.name as stage_name',
                        'bd_extend.name as extend_name',
                        DB::raw("concat(sg.group_text, ' (', sg.group_desc, ')') as group_name"),
                        's.icd10_code',
                        's.icd10_text',
                        's.met_1',
                        's.met_1_date',
                        's.met_2',
                        's.met_2_date',
                        's.met_3',
                        's.met_3_date',
                        's.met_4',
                        's.met_4_date',
                        's.met_5',
                        's.met_5_date',
                        's.met_6',
                        's.met_6_date',
                        's.met_7',
                        's.met_7_date',
                        's.met_7_other',
                        's.clinical_summary',
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_title as t', 'p.title_code', '=', 't.code')
                    ->leftJoin('bd_sex as sex', 'p.sex_code', '=', 'sex.code')
                    ->leftJoin('bd_national as n', 'p.nationality_code', '=', 'n.code')
                    ->leftJoin('bd_deathcause as dc', 'p.deathcause_code', '=', 'dc.code')
                    ->leftJoin('ref_sub_districts', 'ref_sub_districts.id', '=', 'p.address_sub_district_id')
                    ->leftJoin('ref_districts', 'ref_districts.id', '=', 'p.address_district_id')
                    ->leftJoin('ref_provinces', 'ref_provinces.id', '=', 'p.address_province_id')
                    ->leftJoin('ref_sub_districts as psd', 'psd.id', '=', 'p.permanent_address_sub_district_id')
                    ->leftJoin('ref_districts as pd', 'pd.id', '=', 'p.permanent_address_district_id')
                    ->leftJoin('ref_provinces as pp', 'pp.id', '=', 'p.permanent_address_province_id')
                    ->leftJoin('bd_t', 's.t_code', '=', 'bd_t.code')
                    ->leftJoin('bd_n', 's.n_code', '=', 'bd_n.code')
                    ->leftJoin('bd_m', 's.m_code', '=', 'bd_m.code')
                    ->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code')
                    ->leftJoin('bd_extend', 's.extension_code', '=', 'bd_extend.code')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('ref_provinces.health_region_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->get();
                break;

            case 3:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'p.hospital_code',
                        'p.hn_no',
                        't.name as title_name',
                        'p.name',
                        'p.last_name',
                        'p.cid',
                        'p.birth_date',
                        'sex.name as sex_name',
                        'n.name as nationality_name',
                        'p.death_date',
                        'dc.name as deathcause_name',
                        'p.address_no',
                        'p.address_moo',
                        'ref_sub_districts.sub_district_name_th as address_sub_district_name',
                        'ref_districts.district_name_th as address_district_name',
                        'ref_provinces.province_name_th as address_province_name',
                        'p.address_zipcode',
                        'p.permanent_address_no',
                        'p.permanent_address_moo',
                        'psd.sub_district_name_th as permanent_address_sub_district_name',
                        'pd.district_name_th as permanent_address_district_name',
                        'pp.province_name_th as permanent_address_province_name',
                        'p.permanent_address_zipcode',
                        'p.telephone_1',
                        'p.telephone_2',
                        's.first_entrance_date',
                        's.finance_support_text',
                        's.diagnosis_date',
                        's.age',
                        's.diagnosis_text',
                        's.diagnosis_out',
                        's.excision_in_cut_date',
                        's.excision_in_read_date',
                        's.topo_text',
                        's.recurrent',
                        's.recurrent_date',
                        's.morphology_text',
                        's.behaviour_text',
                        's.grade_text',
                        'bd_t.name as t_name',
                        'bd_n.name as n_name',
                        'bd_m.name as m_name',
                        's.tnm_date',
                        'st.name as stage_name',
                        'bd_extend.name as extend_name',
                        DB::raw("concat(sg.group_text, ' (', sg.group_desc, ')') as group_name"),
                        's.icd10_code',
                        's.icd10_text',
                        's.met_1',
                        's.met_1_date',
                        's.met_2',
                        's.met_2_date',
                        's.met_3',
                        's.met_3_date',
                        's.met_4',
                        's.met_4_date',
                        's.met_5',
                        's.met_5_date',
                        's.met_6',
                        's.met_6_date',
                        's.met_7',
                        's.met_7_date',
                        's.met_7_other',
                        's.clinical_summary',
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_title as t', 'p.title_code', '=', 't.code')
                    ->leftJoin('bd_sex as sex', 'p.sex_code', '=', 'sex.code')
                    ->leftJoin('bd_national as n', 'p.nationality_code', '=', 'n.code')
                    ->leftJoin('bd_deathcause as dc', 'p.deathcause_code', '=', 'dc.code')
                    ->leftJoin('ref_sub_districts', 'ref_sub_districts.id', '=', 'p.address_sub_district_id')
                    ->leftJoin('ref_districts', 'ref_districts.id', '=', 'p.address_district_id')
                    ->leftJoin('ref_provinces', 'ref_provinces.id', '=', 'p.address_province_id')
                    ->leftJoin('ref_sub_districts as psd', 'psd.id', '=', 'p.permanent_address_sub_district_id')
                    ->leftJoin('ref_districts as pd', 'pd.id', '=', 'p.permanent_address_district_id')
                    ->leftJoin('ref_provinces as pp', 'pp.id', '=', 'p.permanent_address_province_id')
                    ->leftJoin('bd_t', 's.t_code', '=', 'bd_t.code')
                    ->leftJoin('bd_n', 's.n_code', '=', 'bd_n.code')
                    ->leftJoin('bd_m', 's.m_code', '=', 'bd_m.code')
                    ->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code')
                    ->leftJoin('bd_extend', 's.extension_code', '=', 'bd_extend.code')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('p.address_province_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->get();

                break;

            case 4:
                $results = DB::table('data_cancer_summary', 's')
                    ->select(
                        'p.hospital_code',
                        'p.hn_no',
                        't.name as title_name',
                        'p.name',
                        'p.last_name',
                        'p.cid',
                        'p.birth_date',
                        'sex.name as sex_name',
                        'n.name as nationality_name',
                        'p.death_date',
                        'dc.name as deathcause_name',
                        'p.address_no',
                        'p.address_moo',
                        'ref_sub_districts.sub_district_name_th as address_sub_district_name',
                        'ref_districts.district_name_th as address_district_name',
                        'ref_provinces.province_name_th as address_province_name',
                        'p.address_zipcode',
                        'p.permanent_address_no',
                        'p.permanent_address_moo',
                        'psd.sub_district_name_th as permanent_address_sub_district_name',
                        'pd.district_name_th as permanent_address_district_name',
                        'pp.province_name_th as permanent_address_province_name',
                        'p.permanent_address_zipcode',
                        'p.telephone_1',
                        'p.telephone_2',
                        's.first_entrance_date',
                        's.finance_support_text',
                        's.diagnosis_date',
                        's.age',
                        's.diagnosis_text',
                        's.diagnosis_out',
                        's.excision_in_cut_date',
                        's.excision_in_read_date',
                        's.topo_text',
                        's.recurrent',
                        's.recurrent_date',
                        's.morphology_text',
                        's.behaviour_text',
                        's.grade_text',
                        'bd_t.name as t_name',
                        'bd_n.name as n_name',
                        'bd_m.name as m_name',
                        's.tnm_date',
                        'st.name as stage_name',
                        'bd_extend.name as extend_name',
                        DB::raw("concat(sg.group_text, ' (', sg.group_desc, ')') as group_name"),
                        's.icd10_code',
                        's.icd10_text',
                        's.met_1',
                        's.met_1_date',
                        's.met_2',
                        's.met_2_date',
                        's.met_3',
                        's.met_3_date',
                        's.met_4',
                        's.met_4_date',
                        's.met_5',
                        's.met_5_date',
                        's.met_6',
                        's.met_6_date',
                        's.met_7',
                        's.met_7_date',
                        's.met_7_other',
                        's.clinical_summary',
                    )
                    ->leftJoin('data_patient as p', 'p.id', '=', 's.patient_id')
                    ->leftJoin('bd_title as t', 'p.title_code', '=', 't.code')
                    ->leftJoin('bd_sex as sex', 'p.sex_code', '=', 'sex.code')
                    ->leftJoin('bd_national as n', 'p.nationality_code', '=', 'n.code')
                    ->leftJoin('bd_deathcause as dc', 'p.deathcause_code', '=', 'dc.code')
                    ->leftJoin('ref_sub_districts', 'ref_sub_districts.id', '=', 'p.address_sub_district_id')
                    ->leftJoin('ref_districts', 'ref_districts.id', '=', 'p.address_district_id')
                    ->leftJoin('ref_provinces', 'ref_provinces.id', '=', 'p.address_province_id')
                    ->leftJoin('ref_sub_districts as psd', 'psd.id', '=', 'p.permanent_address_sub_district_id')
                    ->leftJoin('ref_districts as pd', 'pd.id', '=', 'p.permanent_address_district_id')
                    ->leftJoin('ref_provinces as pp', 'pp.id', '=', 'p.permanent_address_province_id')
                    ->leftJoin('bd_t', 's.t_code', '=', 'bd_t.code')
                    ->leftJoin('bd_n', 's.n_code', '=', 'bd_n.code')
                    ->leftJoin('bd_m', 's.m_code', '=', 'bd_m.code')
                    ->leftJoin('bd_stage as st', 's.stage_code', '=', 'st.code')
                    ->leftJoin('bd_extend', 's.extension_code', '=', 'bd_extend.code')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('s.hospital_code', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('st.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->get();
                break;

            default:
                # code...
                break;
        }

        $date = date('d-m-Y');

        if ($data_type == 'json') {
            $fields = [
                'รหัสโรงพยาบาล',
                'HN',
                'คำนำหน้า',
                'ชื่อ',
                'นามสกุล',
                'เลขบัตรประชาชน',
                'วันเกิด',
                'เพศ',
                'สัญชาติ',
                'วันที่เสียชีวิต',
                'สาเหตุการเสียชีวิต',
                'บ้านเลขที่',
                'หมู่ที่',
                'ตำบล',
                'อำเภอ',
                'จังหวัด',
                'รหัสไปรษณีย์',
                'บ้านเลขที่',
                'หมู่ที่',
                'ตำบล',
                'อำเภอ',
                'จังหวัด',
                'รหัสไปรษณีย์',
                'เบอร์โทร 1',
                'เบอร์โทร 2',
                'วันที่เข้ารับบริการครั้งแรก',
                'สิทธิการรักษา',
                'วันที่วินิจฉัย',
                'อายุ',
                'วิธีวินิจฉัย',
                'วินิจฉัยนอก รพ.',
                'วันที่ตัดชิ้นเนื้อ',
                'วันที่อ่านชิ้นเนื้อ',
                'Topography',
                'Recurrent',
                'วันที่ Recurrent',
                'Morphology',
                'Behaviour',
                'Grade',
                'T',
                'N',
                'M',
                'วันที่ TNM',
                'Stage',
                'Extend',
                'ICD-10 Group',
                'รหัส ICD-10',
                'ชื่อ ICD-10',
                'Bone',
                'Bone วันที่',
                'Brain',
                'Brain วันที่',
                'Liver',
                'Liver วันที่',
                'Lung',
                'Lung วันที่',
                'Lymph Node',
                'Lymph Node วันที่',
                'Peritoneum',
                'Peritoneum วันที่',
                'Other',
                'Other วันที่',
                'ระบุ',
                'Clinical Summary',
            ];

            return response()->json(['status' => true, 'data' => ['datasource' => $results, 'fields' => $fields]], 200);
        }

        return Excel::download(new ExportCancerDetail($results), 'rp01_pop_' . $date . '.xlsx');
    }

    public function Forward_data(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $health_region = $request->query('health_region');

        $user = Auth::user();
        // $myHos = DB::table('bd_hospital')->where('code', $user->hosCode)->first();

        $query = DB::table('data_refer as R')
            ->leftJoin('bd_hospital as HOS', 'R.to_hospital_code', '=', 'HOS.code')
            ->select(
                'HOS.code',
                'HOS.name',
                'HOS.health_region_id',
                'ref_provinces.province_name_th AS province_name',
                DB::raw('COUNT(R.id) AS total_refer_count'), // นับจำนวน refer ทั้งหมด
                DB::raw('SUM(R.reson_diagnosis_excision) AS reson_diagnosis_excision'),
                DB::raw('SUM(R.reson_diagnosis_ct) AS reson_diagnosis_ct'),
                DB::raw('SUM(R.reson_diagnosis_mri) AS reson_diagnosis_mri'),
                DB::raw('SUM(R.reson_diagnosis_bone) AS reson_diagnosis_bone'),
                DB::raw('SUM(R.reson_diagnosis_mammogram) AS reson_diagnosis_mammogram'),
                DB::raw('SUM(R.reson_diagnosis_ultra) AS reson_diagnosis_ultra'),
                DB::raw('SUM(R.reson_diagnosis_other) AS reson_diagnosis_other'),
                DB::raw('SUM(R.reson_treat_radiation) AS reson_treat_radiation'),
                DB::raw('SUM(R.reson_treat_surgery) AS reson_treat_surgery'),
                DB::raw('SUM(R.reson_treat_chemo) AS reson_treat_chemo'),
                DB::raw('SUM(R.reson_treat_pallative) AS reson_treat_pallative'),
                DB::raw('SUM(R.reson_treat_other) AS reson_treat_other'),
                DB::raw('SUM(R.reson_right) AS reson_right'),
                DB::raw('SUM(R.reson_wanted) AS reson_wanted'),
                DB::raw('SUM(R.reson_other) AS reson_other'),
                DB::raw('SUM(R.back) AS reson_back'),
                DB::raw('SUM(R.out) AS reson_out')
            )
            ->join('ref_provinces', 'HOS.province_id', '=', 'ref_provinces.id')
            ->whereBetween(DB::raw('DATE(R.created_at)'), [$start_date, $end_date])
            ->where('R.from_hospital_code', $user->hosCode)
            ->groupBy('HOS.name', 'HOS.code', 'HOS.health_region_id', 'ref_provinces.province_name_th')
            ->orderBy('HOS.code', 'asc');

        // ตรวจสอบค่า $health_region
        // if ($health_region == 'true') {
        //     $query->where('HOS.health_region_id', $myHos->health_region_id);
        // } else {
        //     $query->where('HOS.health_region_id', '<>', $myHos->health_region_id);
        // }

        $data = $query->get();

        return response()->json([
            'status' => true,
            'message' => 'success',
            'data' => $data
        ], 200, []);
    }

    public function send_data(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $health_region = $request->query('health_region');

        $user = Auth::user();
        // $myHos = DB::table('bd_hospital')->where('code', $user->hosCode)->first();

        $query = DB::table('data_refer as R')
            ->leftJoin('bd_hospital as HOS', 'R.from_hospital_code', '=', 'HOS.code')
            ->select(
                'HOS.code',
                'HOS.name',
                'HOS.health_region_id',
                'ref_provinces.province_name_th AS province_name',
                DB::raw('COUNT(R.id) AS total_refer_count'), // นับจำนวน refer ทั้งหมด
                DB::raw('SUM(R.reson_diagnosis_excision) AS reson_diagnosis_excision'),
                DB::raw('SUM(R.reson_diagnosis_ct) AS reson_diagnosis_ct'),
                DB::raw('SUM(R.reson_diagnosis_mri) AS reson_diagnosis_mri'),
                DB::raw('SUM(R.reson_diagnosis_bone) AS reson_diagnosis_bone'),
                DB::raw('SUM(R.reson_diagnosis_mammogram) AS reson_diagnosis_mammogram'),
                DB::raw('SUM(R.reson_diagnosis_ultra) AS reson_diagnosis_ultra'),
                DB::raw('SUM(R.reson_diagnosis_other) AS reson_diagnosis_other'),
                DB::raw('SUM(R.reson_treat_radiation) AS reson_treat_radiation'),
                DB::raw('SUM(R.reson_treat_surgery) AS reson_treat_surgery'),
                DB::raw('SUM(R.reson_treat_chemo) AS reson_treat_chemo'),
                DB::raw('SUM(R.reson_treat_pallative) AS reson_treat_pallative'),
                DB::raw('SUM(R.reson_treat_other) AS reson_treat_other'),
                DB::raw('SUM(R.reson_right) AS reson_right'),
                DB::raw('SUM(R.reson_wanted) AS reson_wanted'),
                DB::raw('SUM(R.reson_other) AS reson_other'),
                DB::raw('SUM(R.back) AS reson_back'),
                DB::raw('SUM(R.out) AS reson_out')
            )
            ->join('ref_provinces', 'HOS.province_id', '=', 'ref_provinces.id')
            ->whereBetween(DB::raw('DATE(R.created_at)'), [$start_date, $end_date])
            ->where('R.to_hospital_code', $user->hosCode)
            ->where('R.status', 2)
            ->groupBy('HOS.name', 'HOS.code', 'HOS.health_region_id', 'ref_provinces.province_name_th')
            ->orderBy('HOS.code', 'asc');

        // ตรวจสอบค่า $health_region
        // if ($health_region == 'true') {
        //     $query->where('HOS.health_region_id', $myHos->health_region_id);
        // } else {
        //     $query->where('HOS.health_region_id', '<>', $myHos->health_region_id);
        // }

        $data = $query->get();

        return response()->json([
            'data' => $data
        ], 200, []);
    }

    public function Refuse(Request $request)
    {
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');
        $health_region = $request->query('health_region');

        $user = Auth::user();
        $myHos = DB::table('bd_hospital')->where('code', $user->hosCode)->first();

        $query = DB::table('data_refer AS R')
            ->leftJoin('bd_hospital AS HOS', 'R.from_hospital_code', '=', 'HOS.code')
            ->select(
                'HOS.code',
                'HOS.name',
                'HOS.health_region_id',
                DB::raw('COALESCE(SUM(R.reson_reject_1), 0) AS reson_reject_1'),
                DB::raw('COALESCE(SUM(R.reson_reject_2), 0) AS reson_reject_2'),
                DB::raw('COALESCE(SUM(R.reson_reject_3), 0) AS reson_reject_3')
            )
            ->where('R.to_hospital_code', $user->hosCode)
            ->whereBetween(DB::raw('DATE(R.created_at)'), [$date_start, $date_end])
            ->groupBy('HOS.name', 'HOS.code', 'HOS.health_region_id')
            ->orderBy('HOS.code', 'ASC');

        // ตรวจสอบค่า $health_region
        if ($health_region == 'true') {
            $query->where('HOS.health_region_id', $myHos->health_region_id);
        } else {
            $query->where('HOS.health_region_id', '<>', $myHos->health_region_id);
        }

        $data = $query->get();

        return response()->json([
            'data' => $data
        ], 200, []);
    }
    public function rp01Excel(Request $request)
    {
        return Excel::download(new Rp01Export($request), 'rp01.xlsx');
    }
    public function stageTOExcel(Request $request)
    {
        return Excel::download(new ReportStageExport($request), 'reportStageExport_data.xlsx');
    }

    public function allExcel(Request $request)
    {
        return Excel::download(new AllDocExport($request), 'TCB_All_Cancer_Report.xlsx');
    }

    public function waitingTime(Request $request)
    {
        $input = $request->all();

        // ค้นหา Cancer Sum ที่อยู่ในช่วง start - end
        $results = DB::table('');
    }

    // public function waitingTime(Request $request)
    // {
    //     $input = $request->all();

    //     $results = DB::table('data_patient')
    //         ->select(
    //             'data_patient.cid',
    //             'data_patient.name',
    //             'dcs.hospital_code',
    //             'dcs.icd10_text',
    //             'dcs.diagnosis_date',
    //             'dcs.entrance_date',
    //             'dcs.excision_in_cut_date',
    //             'dcst.treatment_code',
    //             DB::raw('IF(MIN(dcst.treatment_date_end) < MIN(dcst.treatment_date), MIN(dcst.treatment_date_end), MIN(dcst.treatment_date)) AS date_start'),
    //             DB::raw('IF(MAX(dcst.treatment_date_end) > MAX(dcst.treatment_date), MAX(dcst.treatment_date_end), MAX(dcst.treatment_date)) AS date_end')
    //         )
    //         ->leftJoin('data_cancer_summary AS dcs', 'data_patient.id', '=', 'dcs.patient_id')
    //         ->leftJoin('data_cancer_summary_treatment AS dcst', function ($join) {
    //             $join->on('dcs.icd10_code', '=', 'dcst.icd10_code')
    //                 ->on('dcs.patient_id', '=', 'dcst.patient_id');
    //         })
    //         ->where('dcs.hospital_code', $input['hospital_code'])
    //         ->whereBetween('dcs.diagnosis_date', [$input['date_start'], $input['date_end']])
    //         ->groupBy([
    //             'data_patient.cid',
    //             'data_patient.name',
    //             'dcs.hospital_code',
    //             'dcs.icd10_text',
    //             'dcs.diagnosis_date',
    //             'dcs.entrance_date',
    //             'dcs.excision_in_cut_date',
    //             'dcst.treatment_code',
    //         ])
    //         ->get();


    //     $group = [];
    //     foreach ($results as $value) {
    //         if (!isset($group[$value->cid])) {
    //             $group[$value->cid] = [
    //                 'cid' => $value->cid,
    //                 'name' => $value->name,
    //                 // 'hospital_code' => $value->hospital_code,
    //                 'icd10_text' => $value->icd10_text,
    //                 'entrance_date' => $value->entrance_date,
    //                 'diagnosis_date' => $value->diagnosis_date,
    //                 'excision_in_cut_date' => $value->excision_in_cut_date,
    //                 // 'sur_treatment_id' => null,
    //                 'sur_treatment_start_date' => null,
    //                 'sur_treatment_end_date' => null,
    //                 // 'rad_treatment_id' => null,
    //                 'rad_treatment_start_date' => null,
    //                 'rad_treatment_end_date' => null,
    //                 // 'che_treatment_id' => null,
    //                 'che_treatment_start_date' => null,
    //                 'che_treatment_end_date' => null,
    //             ];
    //         }

    //         if ($value->treatment_code == '1') {
    //             // $group[$value->cid]['sur_treatment_id'] = $value->treatment_code;
    //             $group[$value->cid]['sur_treatment_start_date'] = $value->date_start;
    //             $group[$value->cid]['sur_treatment_end_date'] = $value->date_end;
    //         }

    //         if ($value->treatment_code == '2') {
    //             // $group[$value->cid]['rad_treatment_id'] = $value->treatment_code;
    //             $group[$value->cid]['rad_treatment_start_date'] = $value->date_start;
    //             $group[$value->cid]['rad_treatment_end_date'] = $value->date_end;
    //         }

    //         if ($value->treatment_code == '3') {
    //             // $group[$value->cid]['che_treatment_id'] = $value->treatment_code;
    //             $group[$value->cid]['che_treatment_start_date'] = $value->date_start;
    //             $group[$value->cid]['che_treatment_end_date'] = $value->date_end;
    //         }
    //     }

    //     $finalGroup = array_values($group);

    //     // return $finalGroup;
    //     $now = Carbon::now();

    //     return Excel::download(new WaitingTime($finalGroup), 'waiting_time ' . $now->toDateTimeString() .'.xlsx');
    // }
    public function Total_dashboard(Request $request)
    {
        $user = Auth::user();
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $finan = $request->query('finance_support_text');

        $totalPatients = DB::table('data_cancer_summary AS C')->where('C.hospital_code', $user->hosCode)->where('C.behaviour_code', 3)->count();

        $totalNewPatients = DB::table('data_cancer_summary AS C')
            ->where('C.hospital_code', $user->hosCode)
            ->where('C.behaviour_code', 3)
            ->whereBetween(DB::raw('DATE(C.diagnosis_date)'), [$start_date, $end_date]);

        $totalRefer_From = DB::table('data_refer AS R')
            ->where('R.from_hospital_code', $user->hosCode)
            ->whereBetween(DB::raw('DATE(R.created_at)'), [$start_date, $end_date]);

        $totalRefer_To = DB::table('data_refer AS R')
            ->where('R.to_hospital_code', $user->hosCode)
            ->whereBetween(DB::raw('DATE(R.received_at)'), [$start_date, $end_date]);

        if ($finan && $finan != 'null') {
            $totalNewPatients->where('C.finance_support_code', $finan);
            $totalRefer_From->where('R.finance_support_code', $finan);
            $totalRefer_To->where('R.finance_support_code', $finan);
        }

        $totalNewPatients = $totalNewPatients->count('C.id');
        $totalRefer_From = $totalRefer_From->count('R.id');
        $totalRefer_To = $totalRefer_To->count('R.id');

        return response()->json([
            'data' => [
                'total_patients' => $totalPatients,
                'total_new_patients' => $totalNewPatients,
                'total_refer_from' => $totalRefer_From,
                'total_refer_to' => $totalRefer_To
            ]
        ], 200);
    }

    public function Total_dashboardPop(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $finance_support_code = $request->query('finance_support_text');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $totalPatients = DB::table('data_patient AS P')
            ->leftJoin('data_cancer_summary AS C', 'P.id', '=', 'C.patient_id')
            ->where('C.behaviour_code', 3)
            // ->whereBetween(DB::raw('DATE(C.diagnosis_date)'), [$start_date, $end_date])
            ->when($finance_support_code, function ($query) use ($finance_support_code) {
                $query->where('C.finance_support_code', $finance_support_code);
            })
            // ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
            //     $query->whereIn('C.icd10_group_id', $icd10_group_id);
            // })
            ->count();

        $totalNewPatients = 0;
        $totalRefer_From = 0;
        $totalRefer_To = 0;

        switch ($area_level_id) {
            case 1:
                $totalNewPatients = DB::table('data_cancer_summary AS C')
                    ->leftJoin('data_patient AS P', 'P.id', '=', 'C.patient_id')
                    ->where('C.behaviour_code', 3)
                    ->whereBetween(DB::raw('DATE(C.diagnosis_date)'), [$start_date, $end_date])
                    ->when($finance_support_code, function ($query) use ($finance_support_code) {
                        $query->where('C.finance_support_code', $finance_support_code);
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        $query->whereIn('C.icd10_group_id', $icd10_group_id);
                    })
                    ->count();

                $totalRefer_From = DB::table('data_refer AS R')
                    ->whereBetween(DB::raw('DATE(R.created_at)'), [$start_date, $end_date])
                    ->count();

                $totalRefer_To = DB::table('data_refer AS R')
                    ->whereBetween(DB::raw('DATE(R.received_at)'), [$start_date, $end_date])
                    ->count();

                break;

            case 2:
                $totalNewPatients = DB::table('data_cancer_summary AS C')
                    ->leftJoin('data_patient AS P', 'P.id', '=', 'C.patient_id')
                    ->leftJoin('bd_hospital as hos', 'C.hospital_code', '=', 'hos.code')
                    ->where('C.behaviour_code', 3)
                    ->whereBetween(DB::raw('DATE(C.diagnosis_date)'), [$start_date, $end_date])
                    ->when($finance_support_code, function ($query) use ($finance_support_code) {
                        $query->where('C.finance_support_code', $finance_support_code);
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        $query->whereIn('C.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', $area_id);
                    })
                    ->count();

                $totalRefer_From = DB::table('data_refer AS R')
                    ->leftJoin('bd_hospital as hos', 'R.from_hospital_code', '=', 'hos.code')
                    ->whereBetween(DB::raw('DATE(R.created_at)'), [$start_date, $end_date])
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', $area_id);
                    })
                    ->count();

                $totalRefer_To = DB::table('data_refer AS R')
                    ->leftJoin('bd_hospital as hos', 'R.to_hospital_code', '=', 'hos.code')
                    ->whereBetween(DB::raw('DATE(R.received_at)'), [$start_date, $end_date])
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', $area_id);
                    })
                    ->count();

                break;

            case 3:
                $totalNewPatients = DB::table('data_cancer_summary AS C')
                    ->leftJoin('data_patient AS P', 'P.id', '=', 'C.patient_id')
                    ->leftJoin('bd_hospital as hos', 'C.hospital_code', '=', 'hos.code')
                    ->where('C.behaviour_code', 3)
                    ->whereBetween(DB::raw('DATE(C.diagnosis_date)'), [$start_date, $end_date])
                    ->when($finance_support_code, function ($query) use ($finance_support_code) {
                        $query->where('C.finance_support_code', $finance_support_code);
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        $query->whereIn('C.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.province_id', $area_id);
                    })
                    ->count();

                $totalRefer_From = DB::table('data_refer AS R')
                    ->leftJoin('bd_hospital as hos', 'R.from_hospital_code', '=', 'hos.code')
                    ->whereBetween(DB::raw('DATE(R.created_at)'), [$start_date, $end_date])
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.province_id', $area_id);
                    })
                    ->count();

                $totalRefer_To = DB::table('data_refer AS R')
                    ->leftJoin('bd_hospital as hos', 'R.to_hospital_code', '=', 'hos.code')
                    ->whereBetween(DB::raw('DATE(R.received_at)'), [$start_date, $end_date])
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.province_id', $area_id);
                    })
                    ->count();

                break;

            case 4:
                $totalNewPatients = DB::table('data_cancer_summary AS C')
                    ->leftJoin('data_patient AS p', 'C.patient_id', '=', 'p.id')
                    ->where('C.behaviour_code', 3)
                    ->whereBetween(DB::raw('DATE(C.diagnosis_date)'), [$start_date, $end_date])
                    ->when($finance_support_code, function ($query) use ($finance_support_code) {
                        $query->where('C.finance_support_code', $finance_support_code);
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        $query->whereIn('C.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('C.hospital_code', $area_id);
                    })
                    ->count();

                $totalRefer_From = DB::table('data_refer AS R')
                    ->whereBetween(DB::raw('DATE(R.created_at)'), [$start_date, $end_date])
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('R.from_hospital_code', $area_id);
                    })
                    ->count();

                $totalRefer_To = DB::table('data_refer AS R')
                    ->whereBetween(DB::raw('DATE(R.received_at)'), [$start_date, $end_date])
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('R.to_hospital_code', $area_id);
                    })
                    ->count();

                break;

            default:
                break;
        }


        return response()->json([
            'data' => [
                'total_patients' => $totalPatients,
                'total_new_patients' => $totalNewPatients,
                'total_refer_from' => $totalRefer_From,
                'total_refer_to' => $totalRefer_To
            ]
        ], 200);
    }

    public function Waiting(Request $request)
    {
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');
        $treatment_type = $request->query('treatment_type');
        $finance_support_text = $request->query('finance_support_text');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $user = Auth::user();

        $result = DB::table('treatment_group')
            ->where('hospital_code', $user->hosCode)
            ->whereBetween('start_date', [$date_start, $date_end])
            ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                return $query->whereIn('treatment_group.icd10_group', $icd10_group_id);
            })
            ->select('treatment_code', 'waiting_flag')
            ->selectRaw('COUNT(*) AS total')
            ->groupBy('treatment_code', 'waiting_flag')
            ->when($treatment_type, function ($query) use ($treatment_type) {
                if ($treatment_type == 'EXTERNAL') {
                    return $query->where('from', '=', 'external');
                } else if ($treatment_type == 'INTERNAL') {
                    return $query->where('from', '=', 'internal');
                } else {
                    return $query;
                }
            })
            // ->when($finance_support_text, function ($query) use ($finance_support_text) {
            //     return $query->where('finance_support_code', $finance_support_text);
            // })
            ->get();

        $data = [
            'hosName' => $user->hosName,
            'diag_pass' => 0,
            'diag_fail' => 0,
            'diag_No_pass' => 0,
            'diag_total' => 0,
            'sur_pass' => 0,
            'sur_fail' => 0,
            'sur_No_pass' => 0,
            'sur_total' => 0,
            'chemo_pass' => 0,
            'chemo_fail' => 0,
            'chemo_No_pass' => 0,
            'chemo_total' => 0,
            'radi_pass' => 0,
            'radi_fail' => 0,
            'radi_No_pass' => 0,
            'radi_total' => 0,
        ];

        foreach ($result as $value) {
            $prefix = match ($value->treatment_code) {
                '0 Biopsy' => 'diag',
                '1 Surgery' => 'sur',
                '2 Radiation' => 'radi',
                '3 Chemotherapy' => 'chemo',
                default => null,
            };

            if ($prefix) {
                $key = match ($value->waiting_flag) {
                    'PASS' => "{$prefix}_pass",
                    'FAIL' => "{$prefix}_fail",
                    'NO' => "{$prefix}_No_pass",
                    default => null, // 'NO' จะถูกคำนวณแทนที่จะเก็บ
                };

                if ($key) {
                    $data[$key] = $value->total;
                }
            }
        }

        // คำนวณ total ใหม่จาก pass + No_pass
        foreach (['diag', 'sur', 'chemo', 'radi'] as $prefix) {
            $data["{$prefix}_total"] = $data["{$prefix}_pass"] + $data["{$prefix}_No_pass"] + $data["{$prefix}_fail"];
        }

        return response()->json([
            'data' => $data
        ]);
    }
    public function ForwardExcel(Request $request)
    {
        return Excel::download(new ForwardExport($request), 'forward_report.xlsx');
    }
    public function RefuseExcel(Request $request)
    {
        $hospitalCode = $request->input('hospital_code');
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');

        return Excel::download(new Refuse($hospitalCode, $date_start, $date_end), 'รายงานการปฏิเสธ.xlsx');
    }

    public function Split_code(Request $request)
    {
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));
        $treatment_type = $request->query('treatment_type');

        $user = Auth::user();

        $records1 = DB::table('treatment_group')
            ->where('hospital_code', $user->hosCode)
            ->where('waiting_flag', '!=', '-')
            ->whereBetween('start_date', [$date_start, $date_end])
            ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                return $query->whereIn('treatment_group.icd10_group', $icd10_group_id);
            })
            ->when($treatment_type, function ($query) use ($treatment_type) {
                if ($treatment_type == 'EXTERNAL') {
                    return $query->where('from', '=', 'external');
                } else if ($treatment_type == 'INTERNAL') {
                    return $query->where('from', '=', 'internal');
                } else {
                    return $query;
                }
            })
            ->get();

        $result = [];

        foreach ($records1 as $record) {

            $key = explode('#', $record->key_);
            $hos_code = $key[0];
            $cid = $key[1];
            $icd10 = $key[2];

            $result[] = [
                'cid' => $cid ?: '-',
                'name' => $record->name ?? '-',
                'last_name' => $record->last_name ?? '-',
                'icd_10' => $icd10 ?? '-',
                'treatment_code' => $record->treatment_code ?? '-',
                'start_date' => $record->start_date ?? '-',
                'end_date' => $record->end_date ?? '-',
                'previous_date' => $record->previous_date ?? '-',
                'waiting_days' => $record->waiting_days ?? '-',
                'waiting_flag' => $record->waiting_flag ?? '-',
                'from' => $record->from ?? '-',
            ];
        }

        // $records2 = DB::table('treatment_group')
        //     ->where('hospital_code', $user->hosCode)
        //     ->where('waiting_flag', '!=', '-')
        //     ->whereBetween('start_date', [$date_start, $date_end])
        //     // ->limit(1000)
        //     ->where('treatment_code', '=', '0 Biopsy')
        //     ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
        //         return $query->whereIn('treatment_group.icd10_group', $icd10_group_id);
        //     })
        //     ->get();

        // foreach ($records2 as $record) {

        //     $key = explode('#', $record->key_);
        //     $hos_code = $key[0];
        //     $cid = $key[1];
        //     $icd10 = $key[2];

        //     $result[] = [
        //         'cid' => $cid ?: '-',
        //         'name' => $record->name ?? '-',
        //         'last_name' => $record->last_name ?? '-',
        //         'icd_10' => $icd10 ?? '-',
        //         'treatment_code' => $record->treatment_code ?? '-',
        //         'start_date' => $record->start_date ?? '-',
        //         'end_date' => $record->end_date ?? '-',
        //         'previous_date' => $record->previous_date ?? '-',
        //         'waiting_days' => $record->waiting_days ?? '-',
        //         'waiting_flag' => $record->waiting_flag ?? '-',
        //         'from' => $record->from ?? '-',
        //     ];
        // }

        // ตรวจสอบกรณีไม่มีข้อมูลเลย
        // if (empty($result)) {
        //     $result[] = [
        //         'cid' => '-',
        //         'name' => '-',
        //         'last_name' => '-',
        //         'icd_10' => '-',
        //         'treatment_code' => '-',
        //         'start_date' => '-',
        //         'end_date' => '-',
        //         'previous_date' => '-',
        //         'waiting_days' => '-',
        //         'waiting_flag' => '-',
        //     ];
        // }

        return response()->json([
            'data' => $result
        ], 200);
    }

    public function reportPathology(Request $request)
    {
        $input = $request->all();

        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $gender_code = $request->query('gender_code');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary as cs')
                    ->select([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4) AS morphology_code'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6) AS morphology_text'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(cs.id) AS total_count'),
                    ])
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', DB::raw('FIND_IN_SET(SUBSTRING(cs.icd10_code, 1, 3), sg.icd10_list)'), '>', DB::raw('0'))
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'cs.hospital_code')
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(cs.diagnosis_date, COALESCE(cs.first_entrance_date, cs.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('cs.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('cs.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('cs.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('cs.icd10_group_id', $icd10_group_id);
                    })
                    // ->when(isset($area_id), function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', $area_id);
                    // })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('cs.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('cs.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6)'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc'
                    ])
                    ->get();

                break;

            case 2:
                $results = DB::table('data_cancer_summary as cs')
                    ->select([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4) AS morphology_code'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6) AS morphology_text'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(cs.id) AS total_count'),
                    ])
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', DB::raw('FIND_IN_SET(SUBSTRING(cs.icd10_code, 1, 3), sg.icd10_list)'), '>', DB::raw('0'))
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'cs.hospital_code')
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(cs.diagnosis_date, COALESCE(cs.first_entrance_date, cs.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('cs.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('cs.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('cs.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('cs.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('cs.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('cs.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6)'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc'
                    ])
                    ->get();

                break;

            case 3:
                $results = DB::table('data_cancer_summary as cs')
                    ->select([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4) AS morphology_code'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6) AS morphology_text'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(cs.id) AS total_count'),
                    ])
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', DB::raw('FIND_IN_SET(SUBSTRING(cs.icd10_code, 1, 3), sg.icd10_list)'), '>', DB::raw('0'))
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'cs.hospital_code')
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(cs.diagnosis_date, COALESCE(cs.first_entrance_date, cs.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('cs.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('cs.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('cs.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('cs.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('hos.province_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('cs.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('cs.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6)'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc'
                    ])
                    ->get();

                break;

            case 4:
                $results = DB::table('data_cancer_summary as cs')
                    ->select([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4) AS morphology_code'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6) AS morphology_text'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(cs.id) AS total_count'),
                    ])
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', DB::raw('FIND_IN_SET(SUBSTRING(cs.icd10_code, 1, 3), sg.icd10_list)'), '>', DB::raw('0'))

                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(cs.diagnosis_date, COALESCE(cs.first_entrance_date, cs.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('cs.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('cs.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('cs.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('cs.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('cs.hospital_code', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('cs.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('cs.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6)'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc'
                    ])
                    ->get();


                break;

            default:
                # code...
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        foreach ($results as $result) {
            $icd10_text = explode(':', $result->icd10_text);
            if (count($icd10_text) > 1) {
                $result->icd10_text = trim($icd10_text[1]);
            } else {
                $result->icd10_text = '';
            }
        }

        return response()->json([
            'data' => $results
        ], 200, []);
    }

    public function reportPathologyPop(Request $request)
    {
        $input = $request->all();

        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $gender_code = $request->query('gender_code');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary as cs')
                    ->select([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4) AS morphology_code'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6) AS morphology_text'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(cs.id) AS total_count'),
                    ])
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', DB::raw('FIND_IN_SET(SUBSTRING(cs.icd10_code, 1, 3), sg.icd10_list)'), '>', DB::raw('0'))
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'cs.hospital_code')
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(cs.diagnosis_date, COALESCE(cs.first_entrance_date, cs.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('cs.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('cs.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('cs.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('cs.icd10_group_id', $icd10_group_id);
                    })
                    // ->when(isset($area_id), function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', $area_id);
                    // })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('cs.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('cs.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6)'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc'
                    ])
                    ->get();

                break;

            case 2:
                $results = DB::table('data_cancer_summary as cs')
                    ->select([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4) AS morphology_code'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6) AS morphology_text'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(cs.id) AS total_count'),
                    ])
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', DB::raw('FIND_IN_SET(SUBSTRING(cs.icd10_code, 1, 3), sg.icd10_list)'), '>', DB::raw('0'))
                    ->leftJoin('ref_provinces as prov', 'prov.id', '=', 'p.address_province_id')
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(cs.diagnosis_date, COALESCE(cs.first_entrance_date, cs.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('cs.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('cs.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('cs.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('cs.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('prov.health_region_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('cs.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('cs.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6)'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc'
                    ])
                    ->get();

                break;

            case 3:
                $results = DB::table('data_cancer_summary as cs')
                    ->select([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4) AS morphology_code'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6) AS morphology_text'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(cs.id) AS total_count'),
                    ])
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', DB::raw('FIND_IN_SET(SUBSTRING(cs.icd10_code, 1, 3), sg.icd10_list)'), '>', DB::raw('0'))
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(cs.diagnosis_date, COALESCE(cs.first_entrance_date, cs.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('cs.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('cs.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('cs.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('cs.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('p.address_province_id', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('cs.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('cs.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6)'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc'
                    ])
                    ->get();

                break;

            case 4:
                $results = DB::table('data_cancer_summary as cs')
                    ->select([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4) AS morphology_code'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6) AS morphology_text'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc',
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('COUNT(cs.id) AS total_count'),
                    ])
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', DB::raw('FIND_IN_SET(SUBSTRING(cs.icd10_code, 1, 3), sg.icd10_list)'), '>', DB::raw('0'))

                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(cs.diagnosis_date, COALESCE(cs.first_entrance_date, cs.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('cs.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->where(function ($query) use ($input) {
                        if ((isset($input['behaviour_code_start']) && isset($input['behaviour_code_end']))) {
                            $query->whereBetween('cs.behaviour_code', [$input['behaviour_code_start'], $input['behaviour_code_end']]);
                        } else {
                            $query->where('cs.behaviour_code', 3);
                        }
                    })
                    ->when(isset($gender_code), function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('cs.icd10_group_id', $icd10_group_id);
                    })
                    ->when(isset($area_id), function ($query) use ($area_id) {
                        return $query->where('cs.hospital_code', $area_id);
                    })
                    ->when((isset($stage_code_start) && isset($stage_code_end)), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when((isset($morpho_start) && isset($morpho_end)), function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when((isset($age_start) && isset($age_end)), function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('cs.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('cs.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->groupBy([
                        DB::raw('SUBSTRING(cs.morphology_text, 1, 4)'),
                        DB::raw('SUBSTRING(cs.morphology_text, 6)'),
                        'cs.icd10_code',
                        'cs.icd10_text',
                        'sg.group_text',
                        'sg.group_desc'
                    ])
                    ->get();


                break;

            default:
                # code...
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        foreach ($results as $result) {
            $icd10_text = explode(':', $result->icd10_text);
            if (count($icd10_text) > 1) {
                $result->icd10_text = trim($icd10_text[1]);
            } else {
                $result->icd10_text = '';
            }
        }

        return response()->json([
            'data' => $results
        ], 200, []);
    }

    public function getLogDetail(Request $request)
    {

        // Validate request parameters
        $validator = Validator::make($request->all(), [
            // 'hos_code' => 'required|string',
            'date_start' => 'required|date',
            'date_end' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors()->first(), 400);
        }

        $user = Auth::user();

        // Get parameters from request
        $hosCode = $user->hosCode;
        $startDate = $request->input('date_start');
        $endDate = $request->input('date_end');

        try {
            // Execute the query
            $results = DB::table('vw_log_user')
                ->select(
                    DB::raw('log AS `name`'),
                    DB::raw('COUNT(*) AS count')
                )
                ->where('hos_code', $hosCode)
                ->whereBetween('log_dt', [$startDate, $endDate])
                ->groupBy('log')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'Log counts retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve log counts: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getLogAll(Request $request)
    {

        // Validate request parameters
        $validator = Validator::make($request->all(), [
            // 'hos_code' => 'required|string',
            'date_start' => 'required|date',
            'date_end' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors()->first(), 400);
        }

        $user = Auth::user();

        // Get parameters from request
        $hosCode = $user->hosCode;
        $startDate = $request->input('date_start');
        $endDate = $request->input('date_end');

        try {
            // Execute the query
            $results = DB::table('vw_log_user')
                ->select(
                    'name',
                    'last_name',
                    'position',
                    'log',
                    'log_dt_text'
                )
                ->where('hos_code', $hosCode)
                ->whereBetween('log_dt', [$startDate, $endDate])
                ->get();

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'Log counts retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve log counts: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getLogUser(Request $request)
    {

        // Validate request parameters
        $validator = Validator::make($request->all(), [
            // 'hos_code' => 'required|string',
            'date_start' => 'required|date',
            'date_end' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors()->first(), 400);
        }

        $user = Auth::user();

        // Get parameters from request
        $hosCode = $user->hosCode;
        $startDate = $request->input('date_start');
        $endDate = $request->input('date_end');

        try {
            // Execute the query
            $results = DB::table('vw_log_user')
                ->select(
                    'name',
                    'last_name',
                    'position',
                    'log',
                    DB::raw('COUNT(*) AS count')
                )
                ->where('hos_code', $hosCode)
                ->whereBetween('log_dt', [$startDate, $endDate])
                ->groupBy(
                    'name',
                    'last_name',
                    'position',
                    'log',
                )
                ->get();

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'Log counts retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve log counts: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getAllMuskeyinAPI()
    {
        $data = DB::table('vw_hos_muskeyin_api')->get();
        return response()->json([
            'data' => $data
        ], 200, []);
    }

    public function newPatientPop(Request $request)
    {
        $year_start = $request->query('year_start');
        $year_end = $request->query('year_end');
        $province_id = $request->query('province_id');
        $gender_code = $request->query('gender_code');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        // 1. สร้างปีจำลอง
        $years = collect(range($year_start, $year_end))
            ->map(fn($year) => "SELECT $year AS year");

        $yearQuery = DB::table(DB::raw('( ' . $years->implode(' UNION ALL ') . ' ) as years'));

        // 2. สร้าง subquery cancer_summary พร้อม filter
        $cancerSummary = DB::table('data_cancer_summary as c')
            ->join('data_patient as p', 'c.patient_id', '=', 'p.id')
            ->join('bd_hospital as h', 'p.hospital_code', '=', 'h.code')
            ->select([
                'h.name as hospital_name',
                DB::raw('YEAR(c.diagnosis_date) as year'),
                DB::raw('COUNT(c.id) as total')
            ])
            ->whereBetween(DB::raw('YEAR(c.diagnosis_date)'), [$year_start, $year_end])
            ->when(!empty($province_id), fn($q) => $q->where('h.province_id', $province_id))
            ->when(!empty($gender_code), fn($q) => $q->where('p.sex_code', $gender_code))
            ->when(!empty($icd10_group_id), fn($q) => $q->whereIn('c.icd10_group_id', $icd10_group_id))
            ->groupBy('h.name', DB::raw('YEAR(c.diagnosis_date)'));

        // 3. JOIN bd_hospital × ปี และ LEFT JOIN กับ summary
        $result = DB::table('bd_hospital as h')
            ->crossJoinSub($yearQuery, 'y')
            ->leftJoinSub($cancerSummary, 'cs', function ($join) {
                $join->on('cs.hospital_name', '=', 'h.name')
                    ->on('cs.year', '=', 'y.year');
            })
            ->select([
                'h.name as hospital_name',
                'y.year',
                DB::raw('COALESCE(cs.total, 0) as total')
            ])
            ->when(!empty($province_id), fn($q) => $q->where('h.province_id', $province_id))
            ->where('h.dropdown', 1)
            ->orderBy('h.name')
            ->orderBy('y.year')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $result,
        ]);
    }

    public function summaryPMV(Request $request)
    {
        $year_start = $request->query('year_start');
        $year_end = $request->query('year_end');
        $province_id = $request->query('province_id');
        $gender_code = $request->query('gender_code');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $bd_sitegroup = DB::table('bd_sitegroup')
            ->orderBy('group_id')
            ->get()
            ->map(function ($item) {
                return [
                    'icd10_group_id' => $item->group_id,
                    'group_text' => $item->group_text,
                    'group_desc' => $item->group_desc,
                    'total' => 0,
                    'case' => 0,
                    'percent' => 0,
                ];
            })
            ->values();

        $data = DB::table('data_cancer_summary as c')
            ->join('data_patient as p', 'c.patient_id', '=', 'p.id')
            ->join('bd_mor as mor', 'c.morphology_code', '=', 'mor.key')
            ->join('bd_sitegroup', 'c.icd10_group_id', '=', 'bd_sitegroup.group_id')
            ->distinct()
            ->select(
                'p.cid',
                'p.address_province_id',
                DB::raw("YEAR(c.diagnosis_date) as year"),
                'c.topo_code',
                'mor.code as morphology',
                'c.icd10_group_id',
                'bd_sitegroup.group_text',
                'bd_sitegroup.group_desc'
            )
            ->whereYear('c.diagnosis_date', '>=', $year_start)
            ->whereYear('c.diagnosis_date', '<=', $year_end)
            ->when(!empty($province_id), fn($q) => $q->where('p.address_province_id', $province_id))
            ->when(!empty($gender_code), fn($q) => $q->where('p.sex_code', $gender_code))
            ->when(!empty($icd10_group_id), fn($q) => $q->whereIn('c.icd10_group_id', $icd10_group_id))
            ->orderBy('p.cid', 'asc')
            ->get()
            ->groupBy(fn($item) => $item->cid . $item->topo_code)
            ->map(function ($group) {

                $select = null;
                foreach ($group as $key => $value) {
                    if ($select == null) {
                        $select = $value;
                        continue;
                    }

                    if ($value->morphology > $select->morphology) {
                        $select = $value;
                    }
                }

                return $select;
            })
            ->values();

        // จัดรูปแบบข้อมูล
        $total = $data
            ->groupBy('icd10_group_id')
            ->map(function ($item) {
                $total = $item->count();
                $case = $item->where('morphology', '>', '8000')->count();

                return [
                    'icd10_group_id' => $item->first()->icd10_group_id,
                    'group_text' => $item->first()->group_text,
                    'group_desc' => $item->first()->group_desc,
                    'total' => $total,
                    'case' => $case,
                    'percent' => round($case / $total * 100, 2),
                ];
            })
            ->values();

        // กรองเฉพาะรายการที่ไม่มีในอีกอัน โดยใช้ id เปรียบเทียบ
        $result = $bd_sitegroup->reject(fn($item) => $total->contains('icd10_group_id', $item['icd10_group_id']))
            ->values();

        // รวม Array
        $result = $total->merge($result)
            ->sortBy('icd10_group_id')
            ->values();

        return response()->json([
            'success' => true,
            'data' => $result,
        ]);
    }

    public function summaryDCO(Request $request)
    {
        $year_start = $request->query('year_start');
        $year_end = $request->query('year_end');
        $province_id = $request->query('province_id');
        $gender_code = $request->query('gender_code');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $bd_sitegroup = DB::table('bd_sitegroup')
            ->orderBy('group_id')
            ->get()
            ->map(function ($item) {
                return [
                    'icd10_group_id' => $item->group_id,
                    'group_text' => $item->group_text,
                    'group_desc' => $item->group_desc,
                    'total' => 0,
                    'case' => 0,
                    'percent' => 0,
                ];
            })
            ->values();

        $data = DB::table('data_cancer_summary as c')
            ->join('data_patient as p', 'c.patient_id', '=', 'p.id')
            ->join('bd_mor as mor', 'c.morphology_code', '=', 'mor.key')
            ->join('bd_sitegroup', 'c.icd10_group_id', '=', 'bd_sitegroup.group_id')
            ->distinct()
            ->select(
                'p.cid',
                'p.address_province_id',
                DB::raw("YEAR(c.diagnosis_date) as year"),
                'c.topo_code',
                'mor.code as morphology',
                'c.diagnosis_code',
                'c.icd10_group_id',
                'bd_sitegroup.group_text',
                'bd_sitegroup.group_desc'
            )
            ->whereYear('c.diagnosis_date', '>=', $year_start)
            ->whereYear('c.diagnosis_date', '<=', $year_end)
            ->when(!empty($province_id), fn($q) => $q->where('p.address_province_id', $province_id))
            ->when(!empty($gender_code), fn($q) => $q->where('p.sex_code', $gender_code))
            ->when(!empty($icd10_group_id), fn($q) => $q->whereIn('c.icd10_group_id', $icd10_group_id))
            ->orderBy('p.cid', 'asc')
            ->get()
            ->groupBy(fn($item) => $item->cid . $item->topo_code)
            ->map(function ($group) {

                $select = null;
                foreach ($group as $key => $value) {
                    if ($select == null) {
                        $select = $value;
                        continue;
                    }

                    if ($value->morphology > $select->morphology) {
                        $select = $value;
                    }
                }

                return $select;
            })
            ->values();

        // จัดรูปแบบข้อมูล
        $total = $data
            ->groupBy('icd10_group_id')
            ->map(function ($item) {
                $total = $item->count();
                $case = $item->where('diagnosis_code', '=', '0')->count();

                return [
                    'icd10_group_id' => $item->first()->icd10_group_id,
                    'group_text' => $item->first()->group_text,
                    'group_desc' => $item->first()->group_desc,
                    'total' => $total,
                    'case' => $case,
                    'percent' => round($case / $total * 100, 2),
                ];
            })
            ->values();

        // กรองเฉพาะรายการที่ไม่มีในอีกอัน โดยใช้ id เปรียบเทียบ
        $result = $bd_sitegroup->reject(fn($item) => $total->contains('icd10_group_id', $item['icd10_group_id']))
            ->values();

        // รวม Array
        $result = $total->merge($result)
            ->sortBy('icd10_group_id')
            ->values();

        return response()->json([
            'success' => true,
            'data' => $result,
        ]);
    }
}
