<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{

    public function getListAddress(Request $request)
    {
        $dataset = [];
        $input = $request->all();

        if (isset($input['district_id'])) {
            $dataset = DB::table('ref_sub_districts')->where('district_id', $input['district_id'])->orderBy('id')->get()->toArray();
            $dataset = array_map(fn ($n) => ['value' => $n->id, 'text' => substr($n->id, 4) . ' ต.' . $n->sub_district_name_th], $dataset);
            $dataset = array_merge([['value' => '', 'text' => '* ทุกตำบล']], $dataset);
        }

        return $dataset;
    }

    public function getList(Request $request)
    {
        $user = Auth::user();
        $dataset = [];
        $input = $request->all();

        if (isset($input['hospital_code'])) {
        } else if (isset($input['district_id'])) {
            $dataset = DB::table('bd_hospital')
                ->whereNotIn('type', ['16-คลินิกเอกชน', '18-โรงพยาบาลส่งเสริมสุขภาพตำบล'])
                ->where('district_id', $input['district_id'])
                ->orderBy('code')
                ->get()
                ->toArray();
            $dataset = array_map(function ($n) {
                return ['value' => $n->code, 'text' => $n->code . ' ' . $n->name];
            }, $dataset);
            $dataset = array_merge([['value' => '', 'text' => '* ทุกสถานพยาบาล']], $dataset);
        } else if (isset($input['province_id'])) {
            $dataset = DB::table('ref_districts')->where('province_id', $input['province_id'])->orderBy('id')->get()->toArray();
            $dataset = array_map(function ($n) {
                return ['value' => $n->id, 'text' => substr($n->id, 2) . ' อ.' . $n->district_name_th];
            }, $dataset);
            $dataset = array_merge([['value' => '', 'text' => '* ทุกอำเภอ']], $dataset);
        } else if (isset($input['health_region_id'])) {
            $dataset = DB::table('ref_provinces')->where('health_region_id', $input['health_region_id'])->orderBy('id')->get()->toArray();
            $dataset = array_map(function ($n) {
                return ['value' => $n->id, 'text' => $n->id . ' จ.' . $n->province_name_th];
            }, $dataset);
            $dataset = array_merge([['value' => '', 'text' => '* ทุกจังหวัด']], $dataset);
        }

        return $dataset;
    }

    public function getAcl()
    {
        $user               = Auth::user();
        $hospital           = DB::table('bd_hospital')->where('code', $user->hosCode)->first();
        $province           = DB::table('ref_provinces')->where('id', substr($hospital->area_code, 0, 2))->first();

        $health_region_id   = $province->health_region_id;
        $province_id        = substr($hospital->area_code, 0, 2);
        $district_id        = substr($hospital->area_code, 0, 4);
        $sub_district_id    = $hospital->area_code;
        $hospital_code      = $hospital->code;

        $health_regions     = DB::table('ref_health_regions')->get()->toArray();
        $provinces          = DB::table('ref_provinces')->where('health_region_id', $health_region_id)->orderBy('id')->get()->toArray();
        $districts          = DB::table('ref_districts')->where('province_id', $province_id)->orderBy('id')->get()->toArray();
        $sub_districts      = DB::table('ref_sub_districts')->where('district_id', $district_id)->orderBy('id')->get()->toArray();
        $hospitals          = DB::table('bd_hospital')->where('area_code', $sub_district_id)->orderBy('code')->get()->toArray();

        $health_regions =  array_map(function ($n) {
            return ['value' => $n->id, 'text' => $n->health_region_name];
        }, $health_regions);
        $provinces      =  array_map(function ($n) {
            return ['value' => $n->id, 'text' => $n->id . ' จ.' . $n->province_name_th];
        }, $provinces);
        $districts      = array_map(function ($n) {
            return ['value' => $n->id, 'text' => substr($n->id, 2) . ' อ.' . $n->district_name_th];
        }, $districts);
        $sub_districts  = array_map(function ($n) {
            return ['value' => $n->id, 'text' => substr($n->id, 4) . ' ต.' . $n->sub_district_name_th];
        }, $sub_districts);
        $hospitals      =  array_map(function ($n) {
            return ['value' => $n->code, 'text' => $n->code . ' ' . $n->name];
        }, $hospitals);

        return [
            'level_id'          => 2, //$user->level_id,
            'area_id'           => $sub_district_id,
            'health_region_id'  => $health_region_id,
            'health_regions'    => array_merge([['value' => '', 'text' => '* ทั่วประเทศ']], $health_regions),
            'province_id'       => $province_id,
            'provinces'         => array_merge([['value' => '', 'text' => '* ทุกจังหวัด']], $provinces),
            'district_id'       => $district_id,
            'districts'         => array_merge([['value' => '', 'text' => '* ทุกอำเภอ']], $districts),
            'sub_district_id'   => $sub_district_id,
            'sub_districts'     => array_merge([['value' => '', 'text' => '* ทุกตำบล']], $sub_districts),
            'hospital_code'     => $hospital_code,
            'hospitals'         => array_merge([['value' => '', 'text' => '* ทุกสถานพยาบาล']], $hospitals),
        ];
    }

    public function getStat(Request $request)
    {
        return [
            'stat_top_10'   => $this->getStatTop10($request),
            'stat_top'      => $this->getStatTop($request),
            'stat_day'      => $this->getStatDay($request),
            'stat_area'     => $this->getStatArea($request)
        ];
    }

    public function getStatArea(Request $request)
    {
        $user = Auth::user();
        $input = $request->all();

        $field_name = '';
        if (isset($input['health_region_id'])) {
            if ($input['health_region_id'] == '') {
                // $field_name = 'ref_health_regions.health_region_name';
            } else {
                $field_name = 'ref_provinces.province_name_th';
            }
        } else {
            if (isset($input['province_id'])) {
                if ($input['province_id'] == '') {
                    // $field_name = 'ref_provinces.province_name_th';
                } else {
                    $field_name = 'ref_districts.district_name_th';
                }
            } else {
                if (isset($input['district_id'])) {
                    if ($input['district_id'] == '') {
                        // $field_name = 'ref_districts.district_name_th';
                    } else {
                        $field_name = 'bd_hospital.name';
                    }
                } else {
                    $field_name = 'ref_health_regions.health_region_name';
                }
            }
        }

        // Patients *******************************************************************************
        $patients = DB::table('state_patient')
            ->select([
                DB::raw($field_name . ' as area_name'),
                DB::raw('ifnull(sum(state_patient.patients),0) as patients'),
                DB::raw('ifnull(sum(state_patient.cancers),0) as cancers')
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'state_patient.hospital_code')
            ->leftJoin('ref_districts', 'ref_districts.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 4)'))
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 2)'))
            ->leftJoin('ref_health_regions', 'ref_health_regions.id', '=', 'ref_provinces.health_region_id')
            ->where(function ($query) use ($input) {
                if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    if ($input['health_region_id'] == '') {
                        // get all
                    } else {
                        $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                    }
                }

                if (isset($input['start_date']) && isset($input['end_date'])) {
                    $query->whereBetween('state_patient.curr_date', [$input['start_date'], $input['end_date']]);
                }
            })
            ->whereNotNull($field_name)
            ->groupBy($field_name)
            ->orderBy($field_name)
            ->get();

        return $patients;
    }

    public function getStatTop10(Request $request)
    {
        $user = Auth::user();
        $input = $request->all();

        // Patients *******************************************************************************
        $patients = DB::table('state_patient')
            ->select([
                'bd_hospital.name',
                DB::raw('ifnull(sum(state_patient.patients),0) as patients')
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'state_patient.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 2)'))
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('state_patient.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('bd_hospital.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }

                if (isset($input['start_date']) && isset($input['end_date'])) {
                    $query->whereBetween('state_patient.curr_date', [$input['start_date'], $input['end_date']]);
                }
            })
            ->groupBy('bd_hospital.name')
            ->having(DB::raw('ifnull(sum(state_patient.patients),0)'), '>', 0)
            ->orderBy('patients', 'desc')
            ->limit(20)
            ->get();

        // Cancers ********************************************************************************
        $cancers = DB::table('state_patient')
            ->select([
                'bd_hospital.name',
                DB::raw('ifnull(sum(state_patient.cancers),0) as cancers')
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'state_patient.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 2)'))
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('state_patient.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('bd_hospital.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }

                if (isset($input['start_date']) && isset($input['end_date'])) {
                    $query->whereBetween('state_patient.curr_date', [$input['start_date'], $input['end_date']]);
                }
            })
            ->groupBy('bd_hospital.name')
            ->having(DB::raw('ifnull(sum(state_patient.cancers),0)'), '>', 0)
            ->orderBy('cancers', 'desc')
            ->limit(20)
            ->get();

        // Refers From ****************************************************************************
        $refers_from = DB::table('state_patient')
            ->select([
                'bd_hospital.name',
                DB::raw('ifnull(sum(state_patient.refers_from),0) as refers_from')
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'state_patient.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 2)'))
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('state_patient.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('bd_hospital.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }

                if (isset($input['start_date']) && isset($input['end_date'])) {
                    $query->whereBetween('state_patient.curr_date', [$input['start_date'], $input['end_date']]);
                }
            })
            ->groupBy('bd_hospital.name')
            ->having(DB::raw('ifnull(sum(state_patient.refers_from),0)'), '>', 0)
            ->orderBy('refers_from', 'desc')
            ->limit(20)
            ->get();

        // Refers To ******************************************************************************
        $refers_to = DB::table('state_patient')
            ->select([
                'bd_hospital.name',
                DB::raw('ifnull(sum(state_patient.refers_to),0) as refers_to')
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'state_patient.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 2)'))
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('state_patient.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('bd_hospital.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }

                if (isset($input['start_date']) && isset($input['end_date'])) {
                    $query->whereBetween('state_patient.curr_date', [$input['start_date'], $input['end_date']]);
                }
            })
            ->groupBy('bd_hospital.name')
            ->having(DB::raw('ifnull(sum(state_patient.refers_to),0)'), '>', 0)
            ->orderBy('refers_to', 'desc')
            ->limit(20)
            ->get();

        // API ************************************************************************************
        $apis_patients = DB::table(DB::raw('data_patient p'))
            ->select(['h.code', 'h.name', DB::raw('count(p.id) as all_patients'), DB::raw("'-' as all_cancers")])
            ->leftJoin(DB::raw('bd_hospital h'), 'h.code', '=', 'p.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(h.area_code, 1, 2)'))
            ->where('p.created_by', -1)
            ->whereBetween('p.created_at', [$input['start_date'], $input['end_date']])
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('p.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('h.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(h.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(h.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }
            })
            ->groupBy('h.code', 'h.name')
            ->get();

        $apis_cancers = DB::table(DB::raw('data_cancer c'))
            ->select(['h.code', 'h.name', DB::raw('null as all_patients'), DB::raw('count(c.id) as all_cancers')])
            ->leftJoin(DB::raw('bd_hospital h'), 'h.code', '=', 'c.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(h.area_code, 1, 2)'))
            ->where('c.created_by', -1)
            ->whereBetween('c.created_at', [$input['start_date'], $input['end_date']])
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('c.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('h.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(h.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(h.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }
            })
            ->groupBy('h.code', 'h.name')
            ->get();

        $apis = [];
        foreach ($apis_patients as $d) {
            $apis[$d->code] = $d;
        }

        foreach ($apis_cancers as $d) {
            $apis[$d->code] = (object)[
                'code'          => $d->code,
                'name'          => $d->name,
                'all_patients'  => isset($apis[$d->code]) ? $apis[$d->code]->all_patients : '-',
                'all_cancers'   => $d->all_cancers
            ];
        }

        return [
            'patients'      => $patients,
            'cancers'       => $cancers,
            'refers_from'   => $refers_from,
            'refers_to'     => $refers_to,
            'apis'          => array_values($apis),
        ];
    }

    public function getStatDay(Request $request)
    {
        $user = Auth::user();
        $input = $request->all();
        $now = Carbon::now()->addDays()->format('Y-m-d');

        // เดือนปัจจุบัน **************************************************************************
        $day = DB::table('state_patient')
            ->select([
                'state_patient.curr_date',
                DB::raw('ifnull(sum(state_patient.patients),0) as patients'),
                DB::raw('ifnull(sum(state_patient.cancers),0) as cancers'),
                DB::raw('ifnull(sum(state_patient.refers_from),0) as refers')
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'state_patient.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 2)'))
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('state_patient.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('bd_hospital.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }
            })
            ->whereNotNull('state_patient.curr_date')
            ->where('state_patient.curr_date', '<=', $now)
            ->groupBy('state_patient.curr_date')
            ->orderBy('state_patient.curr_date', 'desc')
            ->limit(7)
            ->get();

        $result = [];

        foreach ($day as $val) {
            $result = array_merge([$val], $result);
        }

        return $result;
    }

    public function getStatTop(Request $request)
    {
        $user = Auth::user();
        $input = $request->all();

        // ทั้งหมด ********************************************************************************
        $all = DB::table('state_patient')
            ->select([DB::raw('ifnull(sum(patients),0) as total')])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'state_patient.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 2)'))
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('state_patient.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('bd_hospital.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }
            })
            ->first();

        // เดือนปัจจุบัน **************************************************************************
        $month = DB::table('state_patient')
            ->select([
                DB::raw('ifnull(sum(state_patient.patients),0) as patient_month'),
                DB::raw('ifnull(sum(state_patient.cancers),0) as cancer_month'),
                DB::raw('ifnull(sum(state_patient.refers_from),0) as refer_send'),
                DB::raw('ifnull(sum(state_patient.refers_to),0) as refer_receive')
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'state_patient.hospital_code')
            ->leftJoin('ref_provinces', 'ref_provinces.id', '=', DB::raw('substring(bd_hospital.area_code, 1, 2)'))
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where('state_patient.hospital_code', $input['hospital_code']);
                } else if (isset($input['sub_district_id'])) {
                    $query->where('bd_hospital.area_code', $input['sub_district_id']);
                } else if (isset($input['district_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 4)'), $input['district_id']);
                } else if (isset($input['province_id'])) {
                    $query->where(DB::raw('substring(bd_hospital.area_code, 1, 2)'), $input['province_id']);
                } else if (isset($input['health_region_id'])) {
                    $query->where('ref_provinces.health_region_id', $input['health_region_id']);
                }

                if (isset($input['start_date']) && isset($input['end_date'])) {
                    $query->whereBetween('state_patient.curr_date', [$input['start_date'], $input['end_date']]);
                }
            })
            ->first();

        return [
            'patient_all'   => $all->total,
            'patient_month' => $month->patient_month,
            'cancer_month'  => $month->cancer_month,
            'refer_receive' => $month->refer_receive,
            'refer_send'    => $month->refer_send
        ];
    }
}
