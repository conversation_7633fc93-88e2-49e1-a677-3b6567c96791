<?php

namespace App\Http\Controllers\OTP;

use App\Http\Controllers\Controller;
use App\Models\Otp;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class OTPController extends Controller
{
    private const OTP_EXPIRATION_MINUTES = 10;
    private const SMS_SENDER = 'NCI';
    private const SMS_API_URL = 'https://apis2.cat4sms.com/cat4api/api.php';

    public function Request_OTP(Request $request)
    {
        $request->validate([
            'phone' => 'required|regex:/^[0-9]{10}$/', // Adjust regex as per your phone number format
        ]);

        $phone = $request->phone;
        $token = Str::random(32);
        $refno = Str::random(6);
        $pin = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);

        $otpData = [
            'token' => $token,
            'refno' => $refno,
            'phone' => $phone,
            'pin' => $phone == '0000000000' ? '123456' : $pin,
            'expiration_date' => Carbon::now()->addMinutes(self::OTP_EXPIRATION_MINUTES),
        ];

        try {
            // Save OTP
            $otp = Otp::create($otpData);
            // Send SMS
            if ($phone == '0000000000') {
                return response()->json([
                    'success' => true,
                    'message' => 'กรุณายืนยันรหัส OTP',
                    'data' => $refno,
                ], 200);
            }

            $response = $this->sendOtpSms($otp);

            if ($response->successful()) {

                $cleanResponse = trim($response->body(), "\xEF\xBB\xBF");
                $data = json_decode($cleanResponse);

                if ($data->result->success) {
                    return response()->json([
                        'success' => true,
                        'message' => 'กรุณายืนยันรหัส OTP',
                        'data' => $refno,
                    ], 200);
                }

                Log::error('OTP Request Error', ['error' => $data->result]);

                return response()->json(['error' => 'เกิดข้อผิดพลาดไม่สามารถส่ง SMS ได้'], 400);
            }

            return response()->json(['error' => 'Provider Error'], 400);
        } catch (\Throwable $e) {
            Log::error('OTP Request Error', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'], 400);
        }
    }

    public function Verify_OTP(Request $request)
    {
        $pin = $request->pin;
        $refno = $request->refno;

        $cid = $request->cid;
        $phone = $request->phone;

        $device_no = $request->device_no;
        $notify_token = $request->notify_token;

        $fine_token = Otp::where('refno', $refno)->whereNull('deleted_at')->orderBy('created_at', 'desc')->first();
        if (!$fine_token) {
            return response()->json(['error' => 'ไม่พบข้อมูล RefNo '], 400);
        }

        if ($fine_token->expiration_date <= date('Y-m-d H:i:s')) {
            return response()->json(['error' => 'รหัส OTP หมดอายุ'], 400);
        }
        // ตรวจสอบสถานะการโหลด
        // if ($response->successful()) {
        if ($pin == $fine_token->pin) {

            $patienDevice = DB::table('patient_device')->where('cid', $cid)->where('tel', $phone)->where('device_no', $device_no)->first();

            if ($patienDevice) {

                DB::table('patient_device')
                    ->where('cid', $cid)
                    ->where('tel', $phone)
                    ->where('device_no', $device_no)
                    ->update([
                        // 'tel' => $phone,
                        // 'device_no' => $device_no,
                        'notify_token' => $notify_token,
                        'updated_at' => Carbon::now(),
                    ]);
            } else {

                DB::table('patient_device')->insert([
                    'cid' => $cid,
                    'tel' => $phone,
                    'device_no' => $device_no,
                    'notify_token' => $notify_token,
                    'created_at' => Carbon::now(),
                ]);
            }

            DB::table('otps')
                ->where('token', $fine_token->token)
                ->where('refno', $fine_token->refno)
                ->where('pin', $fine_token->pin)
                ->update([
                    'deleted_at' => Carbon::now(),
                ]);

            return response()->json(['data' => $fine_token], 200);
        } else {
            return response()->json(['error' => 'OTP ไม่ถูกต้อง'], 400);
        }
    }

    private function sendOtpSms($otp)
    {
        $response = Http::asForm()->post(self::SMS_API_URL, [
            'method' => 'send',
            'username' => env('SMS_USERNAME'),
            'password' => env('SMS_PASSWORD'),
            'message' => 'รหัสยืนยัน ' . $otp->pin . ' (Refno) ' . $otp->refno,
            'destination' => $otp->phone,
            'sender' => self::SMS_SENDER,
        ]);

        return $response;
    }

    public function RequestOTPUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phoneNumber' => 'required|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => true,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        $phone = $request->phoneNumber;
        $token = Str::random(32);
        $refno = Str::random(6);
        $pin = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);

        $otpData = [
            'token' => $token,
            'refno' => $refno,
            'phone' => $phone,
            'pin' => $phone == '0000000000' ? '123456' : $pin,
            'expiration_date' => Carbon::now()->addMinutes(self::OTP_EXPIRATION_MINUTES),
        ];

        try {
            // Save OTP
            $otp = Otp::create($otpData);
            // Send SMS
            if ($phone == '0000000000') {
                return response()->json([
                    'success' => true,
                    'message' => 'กรุณายืนยันรหัส OTP',
                    'data' => $refno,
                ], 200);
            }

            $response = $this->sendOtpSms($otp);

            if ($response->successful()) {

                $cleanResponse = trim($response->body(), "\xEF\xBB\xBF");
                $data = json_decode($cleanResponse);

                if ($data->result->success) {
                    return response()->json([
                        'success' => true,
                        'message' => 'กรุณายืนยันรหัส OTP',
                        'data' => $refno,
                    ], 200);
                }

                Log::error('OTP Request Error', ['error' => $data->result]);

                return response()->json(['error' => 'เกิดข้อผิดพลาดไม่สามารถส่ง SMS ได้'], 400);
            }

            return response()->json(['error' => 'Provider Error'], 400);
        } catch (\Throwable $e) {
            Log::error('OTP Request Error', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'], 400);
        }
    }
}
