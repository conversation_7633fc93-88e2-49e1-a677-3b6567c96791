<?php

namespace App\Http\Controllers;

use App\Imports\CancerDataImport;
use App\Imports\PatientDeadImport;
use App\Jobs\ProcessImportDaed;
use App\Jobs\ProcessImportCancer;
use App\Models\DataPatient;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Jobs\ProcessImportDeadPatient;
use Illuminate\Support\Facades\Storage;

class ImportController extends Controller
{
    public function importCancer(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->errors()->first(),], 400);
        }

        $file = $request->file('file');

        $fileSave = $this->saveFile($file);

        $import_id = DB::table('import_table')->insertGetId([
            'detail'        => 'นำเข้าข้อมูลโรคมะเร็ง',
            'path'          => $fileSave['file_path'],
            'created_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'    => $user->id,
            'updated_by'    => $user->id,
        ]);

        ProcessImportCancer::dispatch($import_id, $fileSave['file_path'], $user->id);

        return response()->json([
            'status' => true,
            'message' => 'เริ่มการนำเข้าข้อมูลแล้ว กรุณารอสักครู่',
            'import_id' => $import_id
        ]);
    }

    public function PatientDeadImport(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->errors()->first(),], 400);
        }

        $file = $request->file('file');

        $fileSave = $this->saveFile($file);

        $import_id = DB::table('import_table')->insertGetId([
            'detail'        => 'นำเข้าผู้เสียชีวิต',
            'path'          => $fileSave['file_path'],
            'created_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'    => $user->id,
            'updated_by'    => $user->id,
        ]);

        ProcessImportDeadPatient::dispatch($import_id, $fileSave['file_path']);

        return response()->json([
            'status' => true,
            'message' => 'นำเข้าข้อมูลสำเร็จ',
        ]);
    }

    public function getImportData()
    {
        $user = Auth::user();

        $import = DB::table('import_table')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'status' => true,
            'data' => $import,
        ]);
    }

    public function getImportStatus($id)
    {
        $import = DB::table('import_table')->where('id', $id)->first();

        if (!$import) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูลการ import',
            ], 404);
        }

        return response()->json([
            'status' => true,
            'data' => $import,
        ]);
    }

    public function downloadErrorFile($id)
    {
        $import = DB::table('import_table')->where('id', $id)->first();

        if (!$import || !$import->error_file) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบไฟล์ error',
            ], 404);
        }

        $filePath = public_path($import->error_file);

        if (!file_exists($filePath)) {
            return response()->json([
                'status' => false,
                'message' => 'ไฟล์ไม่พบในระบบ',
            ], 404);
        }

        return response()->download($filePath);
    }


    function columnLetterFromIndex($index)
    {
        $letter = '';
        while ($index > 0) {
            $index--;
            $letter = chr(65 + ($index % 26)) . $letter;
            $index = intval($index / 26);
        }
        return $letter;
    }

    public function saveFile($file)
    {
        $filename = Carbon::now()->timestamp . '-' . $file->getClientOriginalName();

        $path = 'imports/' . Carbon::now()->format('Ymd');

        $file->storeAs($path, $filename, 'public');

        $filepath = $path . '/' . $filename;

        $fileUrl = env('APP_URL') . '/upload/' . $filepath;

        return [
            'file_path' => $filepath,
            'file_url' => $fileUrl,
        ];
    }

    public function deleteImportData($id)
    {
        $data = DB::table('import_table')->where('id', $id)->first();

        if (!$data) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูล',
            ], 404);
        }

        //delete file
        Storage::disk('public')->delete($data->path);

        DB::table('import_table')->where('id', $id)->delete();

        return response()->json([
            'status' => true,
            'message' => 'ลบข้อมูลสำเร็จ',
        ]);
    }
}
