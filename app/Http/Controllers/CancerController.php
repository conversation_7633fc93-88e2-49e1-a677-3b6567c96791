<?php

namespace App\Http\Controllers;

use Exception;
use App\Http\Controllers\Controller;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Arr;

class CancerController extends Controller
{
    public function getFirstApproveTable1(Request $request)
    {
        $data = $this->getFirstApproveTable($request);

        return response()->json($data, 200, []);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if ($request->has('t')) {
            switch ($request->get('t')) {
                case 'get-cancer-by-patient_id':
                    $patient = DB::table('data_patient')->where('id', $request->get('patient_id'))->first();
                    if ($patient && $patient->cid != '0-0000-00000-00-0') {
                        return response()->json($this->getCancerByCid($patient->cid), 200, []);
                    } else {
                        return response()->json($this->getCancerByPatientId($request->get('patient_id')), 200, []);
                    }
                    break;

                case 'get-patient-cancer':
                    $dataset = DB::table('data_cancer')->where('patient_id', $request->get('patient_id'))->orderBy('entrance_date', 'desc')->get()->toArray();
                    return response()->json($dataset, 200, []);
                    break;

                case 'get-patient-cancer-approve':
                    return response()->json($this->getPatientCancerApprove($request), 200, []);
                    break;

                case 'get-first-approve-table':
                    return response()->json($this->getFirstApproveTable($request), 200, []);
                    break;

                case 'get-exist-approve-table':
                    return response()->json($this->getExistApproveTable($request), 200, []);
                    break;

                // case 'get-reference':
                //     $dataset = $this->getReference();
                //     return response()->json($dataset, 200, []);
                //     break;

                default:
                    break;
            }
        }

        if ($request->has('patient_id')) {
            if ($request->has('history')) {
                $cancers = DB::table('data_cancer')->where('patient_id', $request->get('patient_id'))->orderBy('entrance_date', 'desc')->limit(5)->get();

                foreach ($cancers as $idx => $cancer) {
                    $treatments = DB::table('data_treatment')->where('cancer_id', $cancer->id)->orderBy('treatment_date', 'asc')->get();
                    $cancers[$idx]->treatments = [];
                    foreach ($treatments as $treat) {
                        $cancers[$idx]->treatments[] = $treat;
                    }
                    $files = DB::table('data_file')->where('cancer_id', $cancer->id)->get();
                    $cancers[$idx]->files = [];
                    foreach ($files as $file) {
                        $cancers[$idx]->files[] = $file;
                    }
                }

                return response()->json($cancers, 200, []);
            }

            $cancer = DB::table('data_cancer')->where('patient_id', $request->get('patient_id'))->orderBy('id', 'desc')->first();
            return response()->json($cancer, 200, [], JSON_FORCE_OBJECT);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        $user = Auth::user();

        // $validator = Validator::make($request->all(), [
        //     'treatment_start_date' => 'required',
        //     'treatment_code'       => 'required',
        //     'visit_date'           => 'required',
        //     'cid'                  => 'required',
        //     'icd10_code'           => 'required',
        // ], [
        //     'treatment_start_date.required' => "Missing or doesn't match the format or invalid date",
        //     'treatment_code.required'       => 'Missing or not in list, please see reference',
        //     'visit_date.required'           => "Missing or doesn't match the format or invalid date",
        //     'cid.required'                  => "Missing or doesn't match the format",
        //     'icd10_code.required'           => "Missing or not in list, please see reference"
        // ]);

        // if ($validator->fails()) {
        //     return response()->json($validator->errors()->toArray(), 400);
        // }

        $input = $request->all();

        try {
            $type_id        = 0;
            $patient_id     = $input['patient_id'];
            $topo_code      = static::getCodeFromText($input, 'topo_text', 'bd_topo');
            $topo_id        = $topo_code ? substr($topo_code, 0, 2) : null;

            if (isset($input['morphology_text']) && $input['morphology_text'] != '') {
                $mor = DB::table('bd_mor')->where(DB::raw("concat(code, ' ', name)"), $input['morphology_text'])->first();
                if ($mor) {
                    $mor_key = $mor->key;
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'Morphology ไม่ถูกต้อง กรุณาเลือกจากตัวเลือกเท่านั้น'
                    ], 422);
                }
            } else {
                $mor_key = null;
            }

            // ถ้า User ไม่ได้เป็น ทะเบียนมะเร็ง ให้บันทึกข้อมูลโรคมะเร็งเลยทันที
            if ($user->roleCancer == 'N') {
                $val_cancer = $this->storeDataCancer($request, $patient_id, $input, $mor_key);
                return response()->json([
                    'status'            => true,
                    'type_id'           => 5,
                    'message'           => 'บันทึกข้อมูลสำเร็จ.'
                ], 200);
            }

            // กำหนดว่า Cancer ถูกติ๊กว่าผ่านการอนุมัติแล้ว
            $input['user_cancer'] = 1;

            // เตรียมข้อมูลส่งไปตรวจสอบกับ API
            $cancer_send = (object)[
                'cancerData1' => $this->getCancerFromInputSentToApi($patient_id, $input),
                'cancerData2' => $this->getCancerFromTableSendToApi([
                    'table_name'    => 'data_cancer_summary',
                    'patient_id'    => $patient_id,
                    'topo_id'       => $topo_id
                ])
            ];
            // ถ้าไม่พบใบสรุปที่ตรงกับ ผู้ป่วย และ Topo ปัจจุบัน
            if ($cancer_send->cancerData2 == null) {
                $val_cancer = $this->storeDataCancer($request, $patient_id, $input, $mor_key);
                unset($val_cancer['cancer_id']);
                unset($val_cancer['source_id']);

                $val_cancer['diagnosis_date'] = $this->determineDiagnosisDate($input);

                $cancer_summary_id = DB::table('data_cancer_summary')->insertGetId($val_cancer);

                // Treatments
                if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {

                    foreach ($input['curr_treatments'] as $val_treatment) {
                        $val_treat = [
                            'patient_id'            => $patient_id,
                            'cancer_id'             => $cancer_summary_id,
                            'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                            'treatment_code'        => $val_treatment['treatment_code'],
                            'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                            'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                            'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                            'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                            'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                            'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                            'last_update_date'      => Carbon::now()->format('Y-m-d H:i:s'),
                            'updated_by'            => $user->id,
                            'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                        ];
                        DB::table('data_cancer_summary_treatment')->insert($val_treat);
                    }
                }

                // กำหนดว่า Patient นี้ผ่านการอนุมัติแล้ว
                DB::table('data_patient')->where('id', $patient_id)->update(['checked_api' => 1]);

                return response()->json([
                    'status'            => true,
                    'message'           => 'บันทึกข้อมูลสำเร็จ.',
                    'type_id'           => 4,
                    'id'                => $patient_id,
                    'cancer_send'       => $cancer_send,
                    'cancer_receive'    => null
                ], 200);
            }

            // ส่งข้อมูลไปตรวจสอบกับ API
            $cancer_receive = $this->checkCancerFromApi($cancer_send);

            // ถ้าตรวจสอบแล้ว valid = true
            if ($cancer_receive['valid'] && $cancer_receive['cancerData']) {
                // ถ้าตรวจสอบแล้ว multiple_primary = true ต้องสร้างข้อมูลสรุปโรคมะเร็งขึ้นมาใหม่
                if ($cancer_receive['multiple_primary']) {
                    $val_cancer = $this->storeDataCancer($request, $patient_id, $input, $mor_key);
                    unset($val_cancer['cancer_id']);
                    unset($val_cancer['source_id']);

                    $val_cancer['diagnosis_date'] = $this->determineDiagnosisDate($input);

                    $cancer_summary_id = DB::table('data_cancer_summary')->insertGetId($val_cancer);

                    // Treatments
                    if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {

                        foreach ($input['curr_treatments'] as $val_treatment) {
                            $val_treat = [
                                'patient_id'            => $patient_id,
                                'cancer_id'             => $cancer_summary_id,
                                'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                                'treatment_code'        => $val_treatment['treatment_code'],
                                'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                                'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                                'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                                'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                                'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                                'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                                'last_update_date'      => Carbon::now()->format('Y-m-d H:i:s'),
                                'updated_by'            => $user->id,
                                'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                            ];
                            DB::table('data_cancer_summary_treatment')->insert($val_treat);
                        }
                    }

                    // ถ้าตรวจสอบแล้ว multiple_primary = false ให้ Update ข้อมูลโรคมะเร็งเดิม
                } else {
                    $bd_diag = DB::table('bd_diagnosis')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['basis'])->first();
                    $bd_topo = DB::table('bd_topo')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['topography'])->first();
                    $bd_morp = DB::table('bd_mor')->select([DB::raw("concat(code, ' ', name) as name"), 'key'])->where('code', $cancer_receive['cancerData']['morphology'])->first();
                    $bd_beha = DB::table('bd_behaviour')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['behaviour'])->first();
                    $bd_grad = DB::table('bd_grade')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['grade'])->first();

                    $cancer_receive['cancerData']['diagnosis_text']     = $bd_diag ? $bd_diag->name : null; // DB::table('bd_diagnosis')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['basis'])->first()->name;
                    $cancer_receive['cancerData']['topo_text']          = $bd_topo ? $bd_topo->name : null; // DB::table('bd_topo')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['topography'])->first()->name;
                    $cancer_receive['cancerData']['morphology_text']    = $bd_morp ? $bd_morp->name : null; // DB::table('bd_mor')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['morphology'])->first()->name;
                    $cancer_receive['cancerData']['behaviour_text']     = $bd_beha ? $bd_beha->name : null; // DB::table('bd_behaviour')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['behaviour'])->first()->name;
                    $cancer_receive['cancerData']['grade_text']         = $bd_grad ? $bd_grad->name : null; // DB::table('bd_grade')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['grade'])->first()->name;

                    $cancer_summary_2 = DB::table('data_cancer_summary')
                        ->where('patient_id', $patient_id)
                        ->where('topo_id', $topo_id)
                        ->first();

                    $cancer_summary_2->met_1 = $cancer_receive['cancerData']['metastasis_bone'];
                    $cancer_summary_2->met_1_date = $cancer_receive['cancerData']['metastasis_bone_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_bone_date'])
                        : null;

                    $cancer_summary_2->met_2 = $cancer_receive['cancerData']['metastasis_brain'];
                    $cancer_summary_2->met_2_date = $cancer_receive['cancerData']['metastasis_brain_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_brain_date'])
                        : null;

                    $cancer_summary_2->met_3 = $cancer_receive['cancerData']['metastasis_liver'];
                    $cancer_summary_2->met_3_date = $cancer_receive['cancerData']['metastasis_brain_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_brain_date'])
                        : null;

                    $cancer_summary_2->met_4 = $cancer_receive['cancerData']['metastasis_lung'];
                    $cancer_summary_2->met_4_date = $cancer_receive['cancerData']['metastasis_lung_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_lung_date'])
                        : null;

                    $cancer_summary_2->met_5 = $cancer_receive['cancerData']['metastasis_lymph_node'];
                    $cancer_summary_2->met_5_date = $cancer_receive['cancerData']['metastasis_lymph_node_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_lymph_node_date'])
                        : null;

                    $cancer_summary_2->met_6 = $cancer_receive['cancerData']['metastasis_peritoneum'];
                    $cancer_summary_2->met_6_date = $cancer_receive['cancerData']['metastasis_peritoneum_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_peritoneum_date'])
                        : null;
                    $cancer_summary_2->met_7 = $cancer_receive['cancerData']['metastasis_other'];
                    $cancer_summary_2->met_7_date = $cancer_receive['cancerData']['metastasis_other_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_other_date'])
                        : null;
                    $cancer_summary_2->met_7_other = $cancer_receive['cancerData']['metastasis_other_orgen'];

                    $cancer_summary_2->diagnosis_date = $cancer_receive['cancerData']['diag_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['diag_date'])
                        : null;

                    $cancer_summary_2->behaviour_code = $cancer_receive['cancerData']['behaviour'];
                    $cancer_summary_2->behaviour_text = $cancer_receive['cancerData']['behaviour_text'];
                    $cancer_summary_2->morphology_code = $bd_morp ? $bd_morp->key : null;;
                    $cancer_summary_2->morphology_text = $cancer_receive['cancerData']['morphology_text'];
                    $cancer_summary_2->topo_id = substr($cancer_receive['cancerData']['topography'], 0, 2);
                    $cancer_summary_2->topo_code = $cancer_receive['cancerData']['topography'];
                    $cancer_summary_2->topo_text = $cancer_receive['cancerData']['topo_text'];
                    $cancer_summary_2->diagnosis_code = $cancer_receive['cancerData']['basis'];
                    $cancer_summary_2->diagnosis_text = $cancer_receive['cancerData']['diagnosis_text'];
                    $cancer_summary_2->grade_code = $cancer_receive['cancerData']['grade'];
                    $cancer_summary_2->grade_text = $cancer_receive['cancerData']['grade_text'];
                    $cancer_summary_2->stage_code = $cancer_receive['cancerData']['stage'];
                    $cancer_summary_2->extension_code = $cancer_receive['cancerData']['extend'];

                    //convert icd10
                    $suth_url       = 'https://caw.canceranywhere.com/convert_icdo';
                    $auth_username  = 'tcb_2022';
                    $auth_password  = 'tcb2022HAusdf7ew7YHyqw76jhjqwe';

                    $params = (object)[
                        'sex' => $cancer_receive['cancerData']['sex'],
                        'birth_date' => $cancer_receive['cancerData']['birth_date'],
                        'diag_date' => $cancer_receive['cancerData']['diag_date'],
                        'age' => $cancer_receive['cancerData']['age'],
                        'topography' => $cancer_receive['cancerData']['topography'],
                        'morphology' => $cancer_receive['cancerData']['morphology'],
                        'behaviour' => $cancer_receive['cancerData']['behaviour'],
                    ];

                    $body = null;
                    $cancer_summary_2->icd10_code = null;
                    $cancer_summary_2->icd10_text = null;

                    try {
                        $response = Http::withoutVerifying()
                            ->withBasicAuth($auth_username, $auth_password)
                            ->withBody(json_encode($params), 'application/json')
                            ->post($suth_url);

                        $body = $response->json();

                        Log::info('CONVERT ICDO Response ' . json_encode($body));

                        if ($body['valid']) {
                            $icd10_text = static::getIcd10Text($body['icd10']);

                            $cancer_summary_2->icd10_code = $body['icd10'];
                            $cancer_summary_2->icd10_text = $icd10_text;
                            $cancer_summary_2->icd10_group_id = static::getIcd10group($body['icd10']);
                        }
                    } catch (\Exception $e) {
                        Log::info('CONVERT ICDO Error ' . $e);
                    }

                    // TODO: Revise again
                    $cancer_input                                   = $this->getValCancer($patient_id, $input, $mor_key);

                    $cancer_summary_2->diagnosis_date               = $this->determineDiagnosisDate($input);

                    $cancer_summary_2->diagnosis_out                = $cancer_input['diagnosis_out'] ?? null;

                    $cancer_summary_2->finance_support_text         = max($cancer_input['finance_support_text'] ?? null,  $cancer_summary_2->finance_support_text ?? null);
                    $cancer_summary_2->finance_support_code         = max($cancer_input['finance_support_code'] ?? null,  $cancer_summary_2->finance_support_code ?? null);
                    $cancer_summary_2->recurrent                    = $cancer_input['recurrent'] !=  $cancer_summary_2->recurrent ? null : $cancer_summary_2->recurrent;
                    $cancer_summary_2->recurrent_date               = min($cancer_input['recurrent_date'] ?? null,  $cancer_summary_2->recurrent_date ?? null);

                    $cancer_summary_2->t_code                       = $cancer_input['t_code'] ?? null;
                    $cancer_summary_2->n_code                       = $cancer_input['n_code'] ?? null;
                    $cancer_summary_2->m_code                       = $cancer_input['m_code'] ?? null;
                    $cancer_summary_2->tnm_date                     = $cancer_input['tnm_date'] ?? null;
                    $cancer_summary_2->last_update_by               = $user->id;
                    $cancer_summary_2->last_update_date             = Carbon::now()->format('Y-m-d H:i:s');
                    $cancer_summary_2->first_entrance_date          = $cancer_input['first_entrance_date'] ?? null;
                    $cancer_summary_2->excision_in_cut_date         = $cancer_input['excision_in_cut_date'] ?? null;
                    $cancer_summary_2->excision_in_read_date        = $cancer_input['excision_in_read_date'] ?? null;
                    $cancer_summary_2->entrance_date                = $cancer_input['entrance_date'] ?? null;
                    $cancer_summary_2->clinical_summary             = $cancer_input['clinical_summary'] . '\n--------------------------\n'  .  $cancer_summary_2->clinical_summary;
                    $cancer_summary_2->updated_at                   = Carbon::now()->format('Y-m-d H:i:s');
                    $cancer_summary_2->updated_by                   = $user->id;


                    //Save to cancer visit
                    $val_cancer     = $this->storeDataCancer($request, $patient_id, $input, $mor_key);

                    //Sava to cancer summary
                    DB::table('data_cancer_summary')
                        ->where('id', $cancer_summary_2->id)
                        ->update(get_object_vars($cancer_summary_2));

                    // Treatments
                    if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {

                        foreach ($input['curr_treatments'] as $val_treatment) {
                            $val_treat = [
                                'patient_id'            => $patient_id,
                                'cancer_id'             => $cancer_summary_2->id,
                                'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                                'treatment_code'        => $val_treatment['treatment_code'],
                                'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                                'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                                'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                                'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                                'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                                'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                                'last_update_date'      => Carbon::now()->format('Y-m-d H:i:s'),
                                'updated_by'            => $user->id,
                                'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                            ];
                            DB::table('data_cancer_summary_treatment')->insert($val_treat);
                        }
                    }

                    // ถ้า cancerData1->topography == cancerData2->topography ให้ทำการ update ใบสรุปนั้นเลย
                    // if ($cancer_send->cancerData1->topography == $cancer_send->cancerData2->topography) {

                    //     // ถ้า cancerData1->topography != cancerData2->topography
                    // } else {
                    //     // ถ้า cancerData1->topography != cancerData2->topography และมีการยืนยันให้ Update ข้อมูล
                    //     if (isset($input['confirm']) && $input['confirm'] == true) {
                    //         $type_id        = 3;
                    //         $val_cancer     = $this->storeDataCancer($request, $patient_id, $input, $mor_key);
                    //         unset($val_cancer['cancer_id']);
                    //         // $val_cancer['source_id'] = 5;
                    //         DB::table('data_cancer_summary')->where('patient_id', $patient_id)->where('topo_id', $topo_id)->update($val_cancer);
                    //         // ถ้า ยังไมมีการยืนยัน ให้ message กลับไปถาม เพื่อยืนยัน
                    //     } else {
                    //         return response()->json([
                    //             'status'    => false,
                    //             'confirm'   => true,
                    //             'type_id'   => 3,
                    //             'message'   => 'ตำแหน่งย่อยของ Topography ไม่ตรงกับที่เคยบันทึกไว้ เดิมคือ "' . $cancer_send->cancerData2->topography . '" ท่านต้องการเปลี่ยน ใช่หรือไม่'
                    //         ], 200);
                    //     }
                    // }
                }

                // กำหนดว่า Patient นี้ผ่านการอนุมัติแล้ว
                DB::table('data_patient')->where('id', $patient_id)->update(['checked_api' => 1]);

                // ถ้าตรวจสอบแล้ว valid = false (จะไม่เกิดขึ้น เพราะ ถ้า ICD-10 ไม่ผ่านก็ส่งมาไม่ได้)
            } else {
                // Log::info('*******************************************************');
                // Log::error($cancer_receive);
                // Log::error(json_encode($cancer_send->cancerData1));
                // Log::error(json_encode($cancer_send->cancerData2));
                // Log::info('*******************************************************');
                return response()->json([
                    'status'            => false,
                    'cancer_send'       => $cancer_send,
                    'cancer_receive'    => $cancer_receive,
                    'message'           => $cancer_receive['message'] ? $cancer_receive['message'] : 'ข้อมูลไม่ถูกต้อง กรุณาตรวจสอบอีกครั้ง'
                ], 422);
            }
        } catch (Exception $e) {
            Log::error($e);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        return response()->json([
            'status'            => true,
            'message'           => 'บันทึกข้อมูลสำเร็จ.',
            // 'type_id'           => $type_id,
            'id'                => $patient_id,
            'cancer_send'       => $cancer_send,
            'cancer_receive'    => $cancer_receive
        ], 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        if ($request->source_id == 'null' || $request->source_id == 5) {
            return CancerSummaryController::show($id);
        }

        $cancer = DB::table('data_cancer')->select([
            'data_cancer.id',
            'data_cancer.hospital_code',
            DB::raw("bd_hospital.name as hospital_name"),
            'data_cancer.patient_id',
            'data_cancer.entrance_date',
            'data_cancer.first_entrance_date',
            'data_cancer.finance_support_code',
            'data_cancer.finance_support_text',
            'data_cancer.recurrent',
            'data_cancer.recurrent_date',
            'data_cancer.diagnosis_code',
            'data_cancer.diagnosis_text',
            'data_cancer.diagnosis_out',
            'data_cancer.topo_code',
            'data_cancer.topo_text',
            'data_cancer.morphology_code',
            'data_cancer.morphology_text',
            'data_cancer.behaviour_code',
            'data_cancer.behaviour_text',
            'data_cancer.grade_code',
            'data_cancer.grade_text',
            'data_cancer.stage_code',
            'data_cancer.t_code',
            'data_cancer.n_code',
            'data_cancer.m_code',
            'data_cancer.tnm_date',
            'data_cancer.biopsy_sent_date',
            'data_cancer.extension_code',
            'data_cancer.metastasis',
            'data_cancer.metastasis_organ',
            'data_cancer.lifestatus_code',
            'data_cancer.last_seen',
            'data_cancer.death_date',
            'data_cancer.deathcause_code',
            'data_cancer.txt_clinical_sammary',
            'data_cancer.txt_patho',
            'data_cancer.txt_lab',
            'data_cancer.txt_xray',
            'data_cancer.create_by',
            'data_cancer.create_date',
            'data_cancer.last_update_by',
            'data_cancer.last_update_date',
            'data_cancer.diagnosis_date',
            'data_cancer.age',
            'data_cancer.excision_in_num',
            'data_cancer.excision_in_cut_date',
            'data_cancer.excision_in_read_date',
            'data_cancer.excision_out_num',
            'data_cancer.excision_out_cut_date',
            'data_cancer.excision_out_read_date',
            'data_cancer.recurent_code',
            'data_cancer.laterality_code',
            'data_cancer.contact_last_date',
            'data_cancer.refer_from_hos_code',
            'data_cancer.refer_from_date',
            'data_cancer.refer_from_hn',
            'data_cancer.refer_from_detail',
            'data_cancer.refer_to_hos_code',
            'data_cancer.refer_to_date',
            'data_cancer.refer_to_hn',
            'data_cancer.refer_to_detail',
            'data_cancer.icd10_code',
            'data_cancer.icd10_text',
            'data_cancer.clinical_summary',
            'data_cancer.met_1',
            'data_cancer.met_1_date',
            'data_cancer.met_2',
            'data_cancer.met_2_date',
            'data_cancer.met_3',
            'data_cancer.met_3_date',
            'data_cancer.met_4',
            'data_cancer.met_4_date',
            'data_cancer.met_5',
            'data_cancer.met_5_date',
            'data_cancer.met_6',
            'data_cancer.met_6_date',
            'data_cancer.met_7',
            'data_cancer.met_7_date',
            'data_cancer.met_7_other',
            'data_cancer.met_8',
            'data_cancer.user_cancer',
            DB::raw("null as prev_treatments"),
            DB::raw("null as treatments"),
            DB::raw("null as files"),
            DB::raw("null as metastasis")
        ])
            ->leftJoin('bd_hospital', 'data_cancer.hospital_code', '=', 'bd_hospital.code')
            ->where('id', $id)
            ->first();

        if (!$cancer) {
            return response()->json(['message' => 'ไม่พบข้อมูล'], 404);
        }

        $cancer->prev_treatments = [];
        $treatments = DB::table('data_treatment')
            ->where('cancer_id', $cancer->id)
            ->where(function ($query) {
                $query->where('is_prev', 1);
            })
            ->orderBy('treatment_date', 'asc')
            ->get();

        foreach ($treatments as $treat) {
            $treat->icd9_ids = json_decode($treat->icd9_ids);
            $treat->icd9_ids = $treat->icd9_ids ? $treat->icd9_ids : [];

            $cancer->prev_treatments[] = $treat;
        }

        $cancer->treatments = [];
        $treatments = DB::table('data_treatment')
            ->where('cancer_id', $cancer->id)
            ->where(function ($query) {
                $query->where('is_prev', 0)->orWhereNull('is_prev');
            })
            ->orderBy('treatment_date', 'asc')
            ->get();

        foreach ($treatments as $treat) {
            $treat->icd9_ids = json_decode($treat->icd9_ids);
            $treat->icd9_ids = $treat->icd9_ids ? $treat->icd9_ids : [];

            $cancer->treatments[] = $treat;
        }


        $cancer->files = [];
        $files = DB::table('data_file')->where('cancer_id', $cancer->id)->orderBy('id', 'asc')->get();
        foreach ($files as $file) {
            $cancer->files[] = $file;
        }

        return response()->json($cancer, 200, []);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $cancer = DB::table('data_cancer')->where('id', $id)->first();
        $cancer->treatments = [];
        $treatments = DB::table('data_treatment')->where('cancer_id', $cancer->id)->orderBy('treatment_date', 'asc')->get();
        foreach ($treatments as $treat) {
            $treat->icd9_ids = json_decode($treat->icd9_ids);
            $treat->icd9_ids = $treat->icd9_ids ? $treat->icd9_ids : [];

            $cancer->treatments[] = $treat;
        }

        $cancer->files = [];
        $files = DB::table('data_file')->where('cancer_id', $cancer->id)->orderBy('id', 'asc')->get();
        foreach ($files as $file) {
            $cancer->files[] = $file;
        }

        return response()->json($cancer, 200, []);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $input = $request->all();

        $source = $request->query('source', null);

        // Log::info($input);
        $patient_id     = $input['patient_id'];

        if (isset($input['morphology_text']) && $input['morphology_text'] != '') {
            $mor = DB::table('bd_mor')->where(DB::raw("concat(code, ' ', name)"), $input['morphology_text'])->first();
            if ($mor) {
                $mor_key = $mor->key;
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Morphology ไม่ถูกต้อง กรุณาเลือกจากตัวเลือกเท่านั้น'
                ], 422);
            }
        } else {
            $mor_key = null;
        }

        $old_cancer = DB::table('data_cancer')->select('topo_id')->where('id', $id)->first();

        $val_cancer = $this->getValCancer($patient_id, $input, $mor_key);
        unset($val_cancer['created_at']);
        unset($val_cancer['created_by']);
        $val_cancer['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
        $val_cancer['updated_by'] = $user->id;

        try {
            //if visit
            if ($source == 5) {
                // DB::table('data_cancer_summary')->where('id', $id)->update($val_cancer);

                // // Treatments
                // DB::table('data_cancer_summary_treatment')->where('cancer_id', $id)->delete();
                // if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {
                //     foreach ($input['curr_treatments'] as $val_treatment) {
                //         $val_treat = [
                //             'patient_id'            => $patient_id,
                //             'cancer_id'             => $id,
                //             'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                //             'treatment_code'        => $val_treatment['treatment_code'],
                //             'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                //             'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                //             'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                //             'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                //             'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                //             'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                //             'updated_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                //             'updated_by'            => $user->id,
                //             'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                //             'created_by'            => $user->id,
                //         ];
                //         DB::table('data_cancer_summary_treatment')->insert($val_treat);
                //     }
                // }
            } else {
                DB::table('data_cancer')->where('id', $id)->update($val_cancer);
                // Treatments
                DB::table('data_treatment')->where('cancer_id', $id)->delete();
                if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {
                    foreach ($input['curr_treatments'] as $val_treatment) {
                        $val_treat = [
                            'patient_id'            => $patient_id,
                            'cancer_id'             => $id,
                            'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                            'treatment_code'        => $val_treatment['treatment_code'],
                            'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                            'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                            'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                            'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                            'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                            'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                            'updated_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                            'updated_by'            => $user->id,
                            'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                            'created_by'            => $user->id,
                            'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                        ];
                        DB::table('data_treatment')->insert($val_treat);
                    }
                }

                // Files
                if (isset($input['curr_files']) && sizeof($input['curr_files']) > 0) {
                    $file_ids = [];
                    foreach ($input['curr_files'] as $idx => $val_file) {
                        if (isset($val_file['id'])) {
                            $file_ids[] = $val_file['id'];
                            DB::table('data_file')->where('id', $val_file['id'])->update([
                                'file_group_id'     => $val_file['file_group_id'],
                                'updated_at'        => Carbon::now()->format('Y-m-d H:i:s'),
                                'updated_by'        => $user->id
                            ]);
                        } else {
                            if ($request->hasFile('curr_files.' . $idx . '.file')) {
                                $file       = $request->file('curr_files.' . $idx . '.file');
                                $file_size  = $file->getSize();
                                $store_path = "upload/files/" . Carbon::now()->format('Ymd');
                                $name       = md5(uniqid(rand(), true)) . str_replace(' ', '-', $file->getClientOriginalName());
                                $file->move(public_path('/' . $store_path), $name);

                                $windows_path               = public_path(str_replace('/', '\\', $store_path) . '\\' . $name);
                                $val_file['file_path']      = $store_path . '/' . $name;
                                $val_file['file_name']      = $file->getClientOriginalName();
                                $val_file['file_size']      = $file_size;
                                $val_file['patient_id']     = $patient_id;
                                $val_file['cancer_id']      = $id;
                                $val_file['updated_at']     = Carbon::now()->format('Y-m-d H:i:s');
                                $val_file['updated_by']     = $user->id;
                                $val_file['created_at']     = Carbon::now()->format('Y-m-d H:i:s');
                                $val_file['created_by']     = $user->id;

                                unset($val_file['file']);
                                $file_ids[] = DB::table('data_file')->insertGetId($val_file);
                            } else {
                                Log::error('File not found.');
                            }
                        }
                    }

                    // if (sizeof($file_ids) > 0) {
                    //     DB::table('data_file')->where('cancer_id', $id)->whereNotIn('id', $file_ids)->delete();
                    // }
                } else {
                    // DB::table('data_file')->where('cancer_id', $id)->delete();
                }
            }
        } catch (Exception $e) {
            Log::error($e);
            return response()->json([
                'status'    => false,
                'message'   => $e->getMessage()
            ], 422);
        }

        DB::table('data_cancer')->where('id', $id)->update(['user_cancer' => 0]);

        if ($user->roleCancer == 'Y') {
            // check topo
            // TODO :  Revise again
            /////////////////////////////   ใช้ล้างข้อมูลในกรณีเปลี่ยน Topo /////////////////////////////
            if (isset($old_cancer->topo_id) && strlen($old_cancer->topo_id) == 2) {
                $count_topo = DB::table('data_cancer')
                    ->where('patient_id', $patient_id)
                    ->where('topo_id', $old_cancer->topo_id)
                    ->whereNot('id', $id)
                    ->count();

                Log::info('data cancer count => ' . $count_topo);

                if ($count_topo == 0) {
                    $count_topo_summary = DB::table('data_cancer_summary')
                        ->where('patient_id', $patient_id)
                        ->where('topo_id', $old_cancer->topo_id)
                        ->count();
                    Log::info('data cancer summary count => ' . $count_topo_summary);
                    if ($count_topo_summary <= 2) {
                        $cancer_summary = DB::table('data_cancer_summary')
                            ->where('patient_id', $patient_id)
                            ->where('topo_id', $old_cancer->topo_id)
                            ->get();

                        Log::info('data cancer summary data => ' . json_encode($cancer_summary));

                        foreach ($cancer_summary as $val) {
                            DB::table('data_cancer_summary_treatment')
                                ->where('cancer_id', $val->id)
                                ->delete();

                            DB::table('data_cancer_summary')
                                ->where('id', $val->id)
                                ->delete();
                        }
                    }
                }
            }

            // เตรียมข้อมูลส่งไปตรวจสอบกับ API
            $cancer_send = (object)[
                'cancerData1' => $this->getCancerFromInputSentToApi($patient_id, $input),
                'cancerData2' => $this->getCancerFromTableSendToApi([
                    'table_name'    => 'data_cancer_summary',
                    'patient_id'    => $patient_id,
                    'topo_id'       => $val_cancer['topo_id']
                ])
            ];

            // ถ้าไม่พบใบสรุปที่ตรงกับ ผู้ป่วย และ Topo ปัจจุบัน
            if ($cancer_send->cancerData2 == null) {
                unset($val_cancer['cancer_id']);
                unset($val_cancer['source_id']);

                $val_cancer['diagnosis_date'] = $this->determineDiagnosisDate($input);

                $cancer_summary_id = DB::table('data_cancer_summary')->insertGetId($val_cancer);

                DB::table('data_cancer')->where('id', $id)->update(['user_cancer' => 1]);

                // Treatments
                if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {

                    foreach ($input['curr_treatments'] as $val_treatment) {
                        $val_treat = [
                            'patient_id'            => $patient_id,
                            'cancer_id'             => $cancer_summary_id,
                            'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                            'treatment_code'        => $val_treatment['treatment_code'],
                            'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                            'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                            'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                            'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                            'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                            'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                            'last_update_date'      => Carbon::now()->format('Y-m-d H:i:s'),
                            'updated_by'            => $user->id,
                            'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                        ];
                        DB::table('data_cancer_summary_treatment')->insert($val_treat);
                    }
                }

                return response()->json([
                    'status'            => true,
                    'message'           => 'บันทึกข้อมูลสำเร็จ.',
                    'type_id'           => 4,
                    'id'                => $patient_id,
                    'cancer_send'       => $cancer_send,
                    'cancer_receive'    => null
                ], 200);
            }

            // ส่งข้อมูลไปตรวจสอบกับ API
            $cancer_receive = $this->checkCancerFromApi($cancer_send);

            // ถ้าตรวจสอบแล้ว valid = true
            if ($cancer_receive['valid'] && $cancer_receive['cancerData']) {
                // ถ้าตรวจสอบแล้ว multiple_primary = true ต้องสร้างข้อมูลสรุปโรคมะเร็งขึ้นมาใหม่
                if ($cancer_receive['multiple_primary']) {
                    unset($val_cancer['cancer_id']);
                    unset($val_cancer['source_id']);

                    $val_cancer['diagnosis_date'] = $this->determineDiagnosisDate($input);

                    $cancer_summary_id = DB::table('data_cancer_summary')->insertGetId($val_cancer);

                    DB::table('data_cancer')->where('id', $id)->update(['user_cancer' => 1]);

                    // Treatments
                    if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {

                        foreach ($input['curr_treatments'] as $val_treatment) {
                            $val_treat = [
                                'patient_id'            => $patient_id,
                                'cancer_id'             => $cancer_summary_id,
                                'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                                'treatment_code'        => $val_treatment['treatment_code'],
                                'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                                'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                                'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                                'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                                'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                                'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                                'last_update_date'      => Carbon::now()->format('Y-m-d H:i:s'),
                                'updated_by'            => $user->id,
                                'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                            ];
                            DB::table('data_cancer_summary_treatment')->insert($val_treat);
                        }
                    }

                    // ถ้าตรวจสอบแล้ว multiple_primary = false ให้ Update ข้อมูลโรคมะเร็งเดิม
                } else {
                    $bd_diag = DB::table('bd_diagnosis')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['basis'])->first();
                    $bd_topo = DB::table('bd_topo')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['topography'])->first();
                    $bd_morp = DB::table('bd_mor')->select([DB::raw("concat(code, ' ', name) as name"), 'key'])->where('code', $cancer_receive['cancerData']['morphology'])->first();
                    $bd_beha = DB::table('bd_behaviour')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['behaviour'])->first();
                    $bd_grad = DB::table('bd_grade')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['grade'])->first();

                    $cancer_receive['cancerData']['diagnosis_text']     = $bd_diag ? $bd_diag->name : null; // DB::table('bd_diagnosis')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['basis'])->first()->name;
                    $cancer_receive['cancerData']['topo_text']          = $bd_topo ? $bd_topo->name : null; // DB::table('bd_topo')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['topography'])->first()->name;
                    $cancer_receive['cancerData']['morphology_text']    = $bd_morp ? $bd_morp->name : null; // DB::table('bd_mor')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['morphology'])->first()->name;
                    $cancer_receive['cancerData']['behaviour_text']     = $bd_beha ? $bd_beha->name : null; // DB::table('bd_behaviour')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['behaviour'])->first()->name;
                    $cancer_receive['cancerData']['grade_text']         = $bd_grad ? $bd_grad->name : null; // DB::table('bd_grade')->select([DB::raw("concat(code, ' ', name) as name")])->where('code', $cancer_receive['cancerData']['grade'])->first()->name;

                    $cancer_summary_2 = DB::table('data_cancer_summary')
                        ->where('patient_id', $patient_id)
                        ->where('topo_id', $val_cancer['topo_id'])
                        ->first();

                    $cancer_summary_2->met_1 = $cancer_receive['cancerData']['metastasis_bone'];
                    $cancer_summary_2->met_1_date = $cancer_receive['cancerData']['metastasis_bone_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_bone_date'])
                        : null;

                    $cancer_summary_2->met_2 = $cancer_receive['cancerData']['metastasis_brain'];
                    $cancer_summary_2->met_2_date = $cancer_receive['cancerData']['metastasis_brain_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_brain_date'])
                        : null;

                    $cancer_summary_2->met_3 = $cancer_receive['cancerData']['metastasis_liver'];
                    $cancer_summary_2->met_3_date = $cancer_receive['cancerData']['metastasis_brain_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_brain_date'])
                        : null;

                    $cancer_summary_2->met_4 = $cancer_receive['cancerData']['metastasis_lung'];
                    $cancer_summary_2->met_4_date = $cancer_receive['cancerData']['metastasis_lung_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_lung_date'])
                        : null;

                    $cancer_summary_2->met_5 = $cancer_receive['cancerData']['metastasis_lymph_node'];
                    $cancer_summary_2->met_5_date = $cancer_receive['cancerData']['metastasis_lymph_node_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_lymph_node_date'])
                        : null;

                    $cancer_summary_2->met_6 = $cancer_receive['cancerData']['metastasis_peritoneum'];
                    $cancer_summary_2->met_6_date = $cancer_receive['cancerData']['metastasis_peritoneum_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_peritoneum_date'])
                        : null;
                    $cancer_summary_2->met_7 = $cancer_receive['cancerData']['metastasis_other'];
                    $cancer_summary_2->met_7_date = $cancer_receive['cancerData']['metastasis_other_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['metastasis_other_date'])
                        : null;
                    $cancer_summary_2->met_7_other = $cancer_receive['cancerData']['metastasis_other_orgen'];

                    $cancer_summary_2->diagnosis_date = $cancer_receive['cancerData']['diag_date'] != null
                        ? convertThaiDateToIso($cancer_receive['cancerData']['diag_date'])
                        : null;

                    $cancer_summary_2->behaviour_code = $cancer_receive['cancerData']['behaviour'];
                    $cancer_summary_2->behaviour_text = $cancer_receive['cancerData']['behaviour_text'];
                    $cancer_summary_2->morphology_code = $bd_morp ? $bd_morp->key : null;;
                    $cancer_summary_2->morphology_text = $cancer_receive['cancerData']['morphology_text'];
                    $cancer_summary_2->topo_id = substr($cancer_receive['cancerData']['topography'], 0, 2);
                    $cancer_summary_2->topo_code = $cancer_receive['cancerData']['topography'];
                    $cancer_summary_2->topo_text = $cancer_receive['cancerData']['topo_text'];
                    $cancer_summary_2->diagnosis_code = $cancer_receive['cancerData']['basis'];
                    $cancer_summary_2->diagnosis_text = $cancer_receive['cancerData']['diagnosis_text'];
                    $cancer_summary_2->grade_code = $cancer_receive['cancerData']['grade'];
                    $cancer_summary_2->grade_text = $cancer_receive['cancerData']['grade_text'];
                    $cancer_summary_2->stage_code = $cancer_receive['cancerData']['stage'];
                    $cancer_summary_2->extension_code = $cancer_receive['cancerData']['extend'];

                    //convert icd10
                    $suth_url       = 'https://caw.canceranywhere.com/convert_icdo';
                    $auth_username  = 'tcb_2022';
                    $auth_password  = 'tcb2022HAusdf7ew7YHyqw76jhjqwe';

                    $params = (object)[
                        'sex' => $cancer_receive['cancerData']['sex'],
                        'birth_date' => $cancer_receive['cancerData']['birth_date'],
                        'diag_date' => $cancer_receive['cancerData']['diag_date'],
                        'age' => $cancer_receive['cancerData']['age'],
                        'topography' => $cancer_receive['cancerData']['topography'],
                        'morphology' => $cancer_receive['cancerData']['morphology'],
                        'behaviour' => $cancer_receive['cancerData']['behaviour'],
                    ];

                    $body = null;
                    $cancer_summary_2->icd10_code = null;
                    $cancer_summary_2->icd10_text = null;

                    try {
                        $response = Http::withoutVerifying()
                            ->withBasicAuth($auth_username, $auth_password)
                            ->withBody(json_encode($params), 'application/json')
                            ->post($suth_url);

                        $body = $response->json();

                        // {
                        //     "valid": true,
                        //     "message": "",
                        //     "icd10": "C109"
                        // }

                        Log::info('CONVERT ICDO Response ' . json_encode($body));

                        if ($body['valid']) {
                            $icd10_text = static::getIcd10Text($body['icd10']);

                            $cancer_summary_2->icd10_code = $body['icd10'];
                            $cancer_summary_2->icd10_text = $icd10_text;
                            $cancer_summary_2->icd10_group_id = static::getIcd10group($body['icd10']);
                        }
                    } catch (\Exception $e) {
                        Log::info('CONVERT ICDO Error ' . $e);
                    }

                    // TODO: Revise again
                    $cancer_input                 = $this->getValCancer($patient_id, $input, $mor_key);

                    $cancer_summary_2->finance_support_text         = max($cancer_input['finance_support_text'] ?? null,  $cancer_summary_2->finance_support_text ?? null);
                    $cancer_summary_2->finance_support_code         = max($cancer_input['finance_support_code'] ?? null,  $cancer_summary_2->finance_support_code ?? null);
                    $cancer_summary_2->recurrent                    = $cancer_input['recurrent'] !=  $cancer_summary_2->recurrent ? null : $cancer_summary_2->recurrent;
                    $cancer_summary_2->recurrent_date               = min($cancer_input['recurrent_date'] ?? null,  $cancer_summary_2->recurrent_date ?? null);

                    $cancer_summary_2->t_code                       = $cancer_input['t_code'] ?? null;
                    $cancer_summary_2->n_code                       = $cancer_input['n_code'] ?? null;
                    $cancer_summary_2->m_code                       = $cancer_input['m_code'] ?? null;
                    $cancer_summary_2->tnm_date                     = $cancer_input['tnm_date'] ?? null;
                    $cancer_summary_2->last_update_by               = $user->id;
                    $cancer_summary_2->last_update_date             = Carbon::now()->format('Y-m-d H:i:s');
                    $cancer_summary_2->first_entrance_date          = $cancer_input['first_entrance_date'] ?? null;
                    $cancer_summary_2->excision_in_cut_date         = $cancer_input['excision_in_cut_date'] ?? null;
                    $cancer_summary_2->excision_in_read_date        = $cancer_input['excision_in_read_date'] ?? null;
                    $cancer_summary_2->entrance_date                = $cancer_input['entrance_date'] ?? null;
                    $cancer_summary_2->clinical_summary             = $cancer_input['clinical_summary'] . '\n--------------------------\n'  .  $cancer_summary_2->clinical_summary;
                    $cancer_summary_2->updated_at                   = Carbon::now()->format('Y-m-d H:i:s');
                    $cancer_summary_2->updated_by                   = $user->id;

                    //Sava to cancer summary
                    DB::table('data_cancer_summary')
                        ->where('id', $cancer_summary_2->id)
                        ->update(get_object_vars($cancer_summary_2));

                    DB::table('data_cancer')->where('id', $id)->update(['user_cancer' => 1]);

                    // Treatments
                    if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {

                        foreach ($input['curr_treatments'] as $val_treatment) {
                            $val_treat = [
                                'patient_id'            => $patient_id,
                                'cancer_id'             => $cancer_summary_2->id,
                                'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                                'treatment_code'        => $val_treatment['treatment_code'],
                                'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                                'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                                'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                                'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                                'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                                'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                                'last_update_date'      => Carbon::now()->format('Y-m-d H:i:s'),
                                'updated_by'            => $user->id,
                                'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                            ];
                            DB::table('data_cancer_summary_treatment')->insert($val_treat);
                        }
                    }
                }
                // ถ้าตรวจสอบแล้ว valid = false (จะไม่เกิดขึ้น เพราะ ถ้า ICD-10 ไม่ผ่านก็ส่งมาไม่ได้)
            } else {
                // Log::info('*******************************************************');
                // Log::error($cancer_receive);
                // Log::error($cancer_send->cancerData1);
                // Log::error($cancer_send->cancerData2);
                // Log::info('*******************************************************');
                // return response()->json([
                //     'status'            => false,
                //     'cancer_send'       => $cancer_send,
                //     'cancer_receive'    => $cancer_receive,
                //     'message'           => $cancer_receive['message'] ? $cancer_receive['message'] : 'ข้อมูลไม่ถูกต้อง กรุณาตรวจสอบอีกครั้ง'
                // ], 200);
                return response()->json([
                    'status'            => false,
                    'message'           => 'ข้อมูลไม่ถูกต้อง กรุณาตรวจสอบอีกครั้ง'
                ], 422);
            }
        }

        return response()->json([
            'status'            => true,
            'message'           => 'บันทึกข้อมูลสำเร็จ.'
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // ตรวจสอบว่ามี cancer อยู่หรือไม่
        // $old_cancer = DB::table('data_cancer')->where('id', $id)->first();
        $exists = DB::table('data_cancer')->where('id', $id)->exists();

        if (!$exists) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูลที่ต้องการลบ.'
            ], 404);
        }

        DB::table('data_file')->where('cancer_id', $id)->delete();
        DB::table('data_treatment')->where('cancer_id', $id)->delete();
        DB::table('data_cancer')->where('id', $id)->delete();

        return response()->json([
            'status' => true,
            'message' => 'ลบข้อมูลสำเร็จ.'
        ], 200);
    }

    private function getCancerByCid($cid)
    {
        $dataset = DB::table('data_cancer')
            ->select(['data_cancer.id', 'data_cancer.source_id', 'data_cancer.icd10_code', 'data_cancer.entrance_date', 'bd_hospital.name'])
            ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer.patient_id')
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer.hospital_code')
            ->where('data_patient.cid', $cid)
            ->orderBy('data_cancer.entrance_date', 'desc')
            ->get()
            ->toArray();

        return $dataset;
    }

    public function getCancerByPatientId(Request $request)
    {
        $patient_id = $request->query('patient_id');

        $patient = DB::table('data_patient')->where('id', $patient_id)->first();

        if (!$patient) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูลผู้ป่วย'
            ], 404);
        }

        if ($patient->cid == '0-0000-00000-00-0' || $patient->cid == '9-9999-99999-99-9') {
            $datasum = DB::table('data_cancer_summary')
                ->select([
                    'data_cancer_summary.id',
                    'data_cancer_summary.source_id',
                    'data_cancer_summary.icd10_code',
                    'data_cancer_summary.icd10_text',
                    'data_cancer_summary.entrance_date',
                    'bd_hospital.name',
                    DB::raw("'summary' as flag"),
                    'user_cancer'
                ])
                ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer_summary.hospital_code')
                ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                ->where('data_patient.id', $patient->id)
                ->orderBy('data_cancer_summary.created_at', 'desc')
                ->get()
                ->toArray();

            $dataset = DB::table('data_cancer')
                ->select([
                    'data_cancer.id',
                    'data_cancer.source_id',
                    'data_cancer.icd10_code',
                    'data_cancer.icd10_text',
                    'data_cancer.entrance_date',
                    'bd_hospital.name',
                    DB::raw("'cancer' as flag"),
                    'user_cancer'
                ])
                ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer.hospital_code')
                ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer.patient_id')
                ->where('data_patient.id', $patient->id)
                ->where('data_cancer.hospital_code', $patient->hospital_code)
                ->orderBy('data_cancer.entrance_date', 'desc')
                ->get()
                ->toArray();

            return array_merge($datasum, $dataset);
        }

        $datasum = DB::table('data_cancer_summary')
            ->select([
                'data_cancer_summary.id',
                'data_cancer_summary.source_id',
                'data_cancer_summary.icd10_code',
                'data_cancer_summary.icd10_text',
                'data_cancer_summary.entrance_date',
                'bd_hospital.name',
                DB::raw("'summary' as flag"),
                'user_cancer'
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer_summary.hospital_code')
            ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
            ->where('data_patient.cid', $patient->cid)
            ->orderBy('data_cancer_summary.created_at', 'desc')
            ->get()
            ->toArray();

        $dataset = DB::table('data_cancer')
            ->select([
                'data_cancer.id',
                'data_cancer.source_id',
                'data_cancer.icd10_code',
                'data_cancer.icd10_text',
                'data_cancer.entrance_date',
                'bd_hospital.name',
                DB::raw("'cancer' as flag"),
                'user_cancer'
            ])
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer.hospital_code')
            ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer.patient_id')
            ->where('data_patient.cid', $patient->cid)
            ->where('data_cancer.hospital_code', $patient->hospital_code)
            ->orderBy('data_cancer.entrance_date', 'desc')
            ->get()
            ->toArray();

        return array_merge($datasum, $dataset);
    }

    private function getPatientCancerHistory($request)
    {
        $patient = $this->getPatient($request->get('patient_id'));

        if ($patient->cid == '0-0000-00000-00-0') {
            $cancers = DB::table('data_cancer')->where('patient_id', $request->get('patient_id'))->orderBy('entrance_date', 'desc')->get();
        } else {
            $patient_ids =  DB::table('data_patient')->where('cid', $patient->cid)->pluck('id');
            $cancers = DB::table('data_cancer')->whereIn('patient_id', $patient_ids)->orderBy('entrance_date', 'desc')->get();
        }

        foreach ($cancers as $idx => $cancer) {
            $hospital = DB::table('bd_hospital')->where('code', $cancer->hospital_code)->first();
            $cancers[$idx]->hospital_name = $hospital->name;

            $treatments = DB::table('data_treatment')->where('cancer_id', $cancer->id)->where(function ($query) {
                $query->orWhere('is_prev', null)
                    ->orWhere('is_prev', '!=', 1);
            })->orderBy('treatment_date', 'asc')->get();

            $cancers[$idx]->treatments = [];
            foreach ($treatments as $treat) {
                $cancers[$idx]->treatments[] = $treat;
            }
            $files = DB::table('data_file')->where('cancer_id', $cancer->id)->get();
            $cancers[$idx]->files = [];
            foreach ($files as $file) {
                $cancers[$idx]->files[] = $file;
            }
        }

        $files = [];

        if ($patient->cid == '0-0000-00000-00-0' || $patient->cid == '9-9999-99999-99-9') {
            // $files = DB::table('data_file')
            //     ->select('data_file.*')
            //     ->join('data_patient', 'data_patient.id', '=', 'data_file.patient_id')
            //     ->where('data_patient.cid', $patient->cid)
            //     ->where('data_patient.hn_no', $patient->hn_no)
            //     ->orderBy('created_at', 'desc')
            //     ->get();
        } else {
            $files1 = DB::table('data_file')
                ->select('data_file.*')
                ->join('data_patient', 'data_patient.id', '=', 'data_file.patient_id')
                ->where('data_patient.cid', $patient->cid)
                ->orderBy('created_at', 'desc')
                ->get();

            $files2 = DB::table('data_refer_cancer_file')
                ->select('data_refer_cancer_file.*')
                ->join('data_refer_patient', 'data_refer_patient.id', '=', 'data_refer_cancer_file.patient_id')
                ->where('data_refer_patient.cid', $patient->cid)
                ->orderBy('created_at', 'desc')
                ->get();

            $files = array_merge($files1->toArray(), $files2->toArray());
        }

        return [
            'patient' => $patient,
            'cancers' => $cancers,
            'files' => $files,
        ];
    }

    private function getPatient($patient_id)
    {
        $patient = DB::table('data_patient')
            ->select([
                'data_patient.id',
                DB::raw('bd_hospital.name as hospital_name'),
                'data_patient.hn_no',
                DB::raw('bd_title.name as title_name'),
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.cid',
                'data_patient.birth_date',
                DB::raw('bd_sex.name as sex_name'),
                'data_patient.address_no',
                'data_patient.address_moo',
                'ref_sub_districts.sub_district_name_th',
                'ref_districts.district_name_th',
                'ref_provinces.province_name_th',
                'data_patient.address_zipcode',
                'data_patient.email',
                'data_patient.telephone_1',
                'data_patient.telephone_2'
            ])
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('ref_provinces', 'data_patient.address_province_id', '=', 'ref_provinces.id')
            ->leftJoin('ref_districts', 'data_patient.address_district_id', '=', 'ref_districts.id')
            ->leftJoin('ref_sub_districts', 'data_patient.address_sub_district_id', '=', 'ref_sub_districts.id')
            ->where('data_patient.id', $patient_id)
            ->first();

        return $patient;
    }

    public function getPatientCancerApprove(Request $request)
    {
        $patient_id = $request->get('patient_id');
        $icd10_code = $request->get('icd10_code');

        // ข้อมูลโรคมะเร็งที่ยังไม่ Approve *******************************************************
        $cancers = DB::table('data_cancer')
            ->select('data_cancer.*', DB::raw('bd_stage.name as stage_name'), DB::raw('bd_extend.name as extension_name'))
            ->leftJoin('bd_stage', 'bd_stage.code', '=', 'data_cancer.stage_code')
            ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer.extension_code')
            ->where('patient_id', $patient_id)
            ->where('user_cancer', 0)
            ->where('icd10_code', $icd10_code)
            ->orderBy('entrance_date', 'desc')
            ->get()
            ->toArray();

        foreach ($cancers as $idx => $cancer) {
            $treatments = DB::table('data_treatment')
                ->where('cancer_id', $cancer->id)
                ->where(function ($query) {
                    $query->orWhere('is_prev', null)
                        ->orWhere('is_prev', '!=', 1);
                })
                ->orderBy('treatment_date', 'asc')
                ->get();

            $cancers[$idx]->treatments = [];
            foreach ($treatments as $treat) {
                $cancers[$idx]->treatments[] = $treat;
            }
            $files = DB::table('data_file')->where('cancer_id', $cancer->id)->get();
            $cancers[$idx]->files = [];
            foreach ($files as $file) {
                $cancers[$idx]->files[] = $file;
            }
        }
        // ****************************************************************************************


        // ข้อมูลสรุป ********************************************************************************
        $old = $this->getDefaultNewCancer($request);

        // ****************************************************************************************
        if ($old) {
            $new = $old;
        } else {
            if (sizeof($cancers) >= 1) {
                $new = clone $cancers[0];

                $new = $this->autoApprove($cancers, $new);
            } else {
                $new = null;
            }
        }

        if ($new) {
            $new->treatmentsn = [];

            foreach ($cancers as $cancer) {
                foreach ($cancer->treatments as $treat) {
                    $new->treatmentsn[] = $treat;
                }
            }
        }

        $patient            = DB::table('data_patient')->where('id', $patient_id)->first();
        $birth_date         = Carbon::createFromFormat('Y-m-d', $patient->birth_date);      // ค.ศ.
        $diagnosis_date     = $new->diagnosis_date ? Carbon::createFromFormat('Y-m-d', $new->diagnosis_date) : Carbon::now();      // ค.ศ.

        return [
            'curr_patient'      => [
                'id'                => $patient_id,
                'age'               => intval($birth_date->diff($diagnosis_date)->format('%y')),
                'birth_date'        => $birth_date->addYears(543)->format('d/m/Y'),
                'sex_code'          => $patient->sex_code,
            ],
            'cancers'           => $cancers,
            'old_cancer'        => $old,
            'curr_cancer'       => $new
        ];
    }

    private function getDefaultNewCancer($request)
    {
        $cancer = DB::table('data_cancer_summary')
            ->where('patient_id', $request->get('patient_id'))
            // ->where(function ($query) use ($request) {
            //     if ($request->has('topo_id')) {
            //         $query->where('topo_id', $request->get('topo_id'));
            //     }
            // })
            // ->when($request->has('icd10_code'), function ($query) use ($request) {
            //     $query->where('icd10_code', 'LIKE', '%' . substr($request->get('icd10_code'), 0, 3) . '%');
            // })
            ->where('icd10_code', substr($request->get('icd10_code'), 0, 3))
            ->orderBy('updated_at', 'desc')
            ->first();

        if ($cancer) {
            $treatments = DB::table('data_cancer_summary_treatment')
                // ->where('patient_id', $request->get('patient_id'))
                ->where('cancer_id', $cancer->id)
                // ->orderBy('treatment_date', 'asc')
                ->get();

            $cancer->treatments = [];
            foreach ($treatments as $treat) {
                $cancer->treatments[] = $treat;
            }
        }

        return $cancer;
    }

    public function getExistApproveTable(Request $request)
    {
        $user = Auth::user();

        $input = $request->all();
        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $searchable = [
            'data_patient.hn_no',
            'data_patient.cid',
            DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name)")
        ];

        // ถ้า User เป็น "ทะเบียนมะเร็ง" ให้ทำการสร้างใบสรุปทันที
        // if ($user->roleCancer == 'Y') {
        $dataset = DB::table('data_cancer')->select([
            'data_patient.id',
            'data_patient.hn_no',
            DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name) as full_name"),
            'bd_sex.name',
            'data_patient.cid',
            DB::raw('max(data_cancer.entrance_date) as entrance_date'),
            DB::raw('count(data_cancer.id) as cancer_count'),
            DB::raw('max(data_cancer.created_at) as created_at'),
            // DB::raw('count(data_cancer.id) as cancer_count')
        ])
            ->leftJoin('data_patient', 'data_cancer.patient_id', '=', 'data_patient.id')
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('data_cancer_summary', 'data_cancer.patient_id', '=', 'data_cancer_summary.patient_id')
            ->whereNotNull('data_cancer_summary.id')
            // ->whereNotNull('data_cancer.diagnosis_date')
            ->where('data_cancer.hospital_code', $user->hosCode)
            ->where('data_patient.hospital_code', $user->hosCode)
            ->where('data_cancer.user_cancer', 0)
            ->where(function ($query) use ($input, $search, $searchable) {
                if (isset($search['value']) && !empty($search['value'])) {
                    foreach ($searchable as $s) {
                        if ($s === 'data_patient.cid') {
                            $searchValue = str_replace('-', '', $search['value']); // ลบ - ออกจากค่าที่ใช้ค้นหา
                            $query->orWhere(DB::raw("REPLACE(data_patient.cid, '-', '')"), 'LIKE', '%' . $searchValue . '%');
                        } else {
                            $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                        }
                    }
                }
            })
            // ->groupBy( 'data_patient.id', 'data_patient.hn_no', DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name)"), 'data_patient.birth_date', 'data_patient.telephone', 'data_patient.cid' )
            ->groupBy('data_patient.id', 'data_patient.hn_no', 'full_name', 'bd_sex.name', 'data_patient.cid')
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1)
            ->toArray();


        if (isset($input['searchTerm'])) {
            $dataset['searchTerm'] = $input['searchTerm'] ?: '';
        } else {
            $dataset['searchTerm'] = '';
        }

        return $dataset;
        // } else {
        //     return null;
        // }
    }

    public function getFirstApproveTable(Request $request)
    {
        $user = Auth::user();

        $input = $request->all();
        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $searchable = [
            'data_patient.hn_no',
            'data_patient.cid',
            DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name)")
        ];

        $dataset = DB::table('data_cancer')->select([
            'data_patient.id',
            'data_patient.hn_no',
            DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name) as full_name"),
            'bd_sex.name',
            'data_patient.cid',
            DB::raw('max(data_cancer.entrance_date) as entrance_date'),
            DB::raw('count(data_cancer.id) as cancer_count'),
            DB::raw('max(data_cancer.created_at) as created_at')
        ])
            ->leftJoin('data_patient', 'data_cancer.patient_id', '=', 'data_patient.id')
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('data_cancer_summary', 'data_cancer.patient_id', '=', 'data_cancer_summary.patient_id')
            ->whereNull('data_cancer_summary.id')
            // ->whereNotNull('data_cancer.diagnosis_date')
            ->where('data_cancer.hospital_code', $user->hosCode)
            ->where('data_patient.hospital_code', $user->hosCode)
            ->where('data_cancer.user_cancer', 0)
            ->where(function ($query) use ($search, $searchable) {
                if (isset($search['value']) && !empty($search['value'])) {

                    foreach ($searchable as $s) {
                        if ($s === 'data_patient.cid') {
                            $searchValue = str_replace('-', '', $search['value']); // ลบ - ออกจากค่าที่ใช้ค้นหา
                            $query->orWhere(DB::raw("REPLACE(data_patient.cid, '-', '')"), 'LIKE', '%' . $searchValue . '%');
                        } else {
                            $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                        }
                    }
                }
            })
            ->groupBy('data_patient.id', 'data_patient.hn_no', 'full_name', 'bd_sex.name', 'data_patient.cid')
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1)
            ->toArray();

        if (isset($input['searchTerm'])) {
            $dataset['searchTerm'] = $input['searchTerm'] ?: '';
        } else {
            $dataset['searchTerm'] = '';
        }

        return $dataset;
    }

    private function storeDataCancer($request, $patient_id, $input, $mor_key)
    {
        $user = Auth::user();

        DB::beginTransaction();

        try {
            // ข้อมูลโรคมะเร็งใหม่
            $val_cancer                 = $this->getValCancer($patient_id, $input, $mor_key);
            $val_cancer['source_id']    = $user->roleCancer == 'Y' ? 1 : 2;
            $val_cancer['updated_at']   = Carbon::now()->format('Y-m-d H:i:s');
            $val_cancer['updated_by']   = $user->id;
            $val_cancer['created_at']   = Carbon::now()->format('Y-m-d H:i:s');
            $val_cancer['created_by']   = $user->id;
            $cancer_id                  = DB::table('data_cancer')->insertGetId($val_cancer);

            $val_cancer['cancer_id']    = $cancer_id;

            $val_patient = PatientController::getPatientDetail($patient_id);

            if ($cancer_id) {
                $hospital = DB::table('bd_hospital')->where('code', $val_cancer['hospital_code'])->first();
                if ($hospital) {
                    DB::table('state_patient')->updateOrInsert(
                        [
                            'curr_date'         => Carbon::now()->format('Y-m-d'),
                            'hospital_code'     => $val_cancer['hospital_code']
                        ],
                        [
                            'curr_date'         => Carbon::now()->format('Y-m-d'),
                            'health_region_id'  => $hospital->health_region_id,
                            'province_id'       => $hospital->province_id,
                            'district_id'       => $hospital->district_id,
                            'sub_district_id'   => $hospital->sub_district_id,
                            'hospital_code'     => $val_cancer['hospital_code'],
                            'cancers'           => DB::raw('ifnull(cancers, 0) + 1')
                        ]
                    );
                }
            }
            // Treatments
            if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {
                foreach ($input['curr_treatments'] as $val_treatment) {
                    $val_treat = [
                        'patient_id'            => $patient_id,
                        'cancer_id'             => $cancer_id,
                        'treatment_type_id'     => static::getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                        'treatment_code'        => $val_treatment['treatment_code'],
                        'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                        'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                        'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                        'none_protocol'         => isset($val_treatment['none_protocol']) && $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                        'none_protocol_note'    => isset($val_treatment['none_protocol_note']) && $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                        'note'                  => isset($val_treatment['note']) && $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                        'updated_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                        'updated_by'            => $user->id,
                        'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                        'created_by'            => $user->id,
                        'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                    ];
                    DB::table('data_treatment')->insert($val_treat);

                    //create 4 6 6
                    // $key_ = $val_cancer['hospital_code'] . $val_patient['cid'] . $val_cancer['icd10_code'];
                    // $val_can_treat = [
                    //     'key_'              => $key_,
                    //     'hospital_code'     => $val_cancer['hospital_code'],
                    //     'hn_no'             => $val_patient['hn_no'],
                    //     'cid'               => $val_patient['cid'],
                    //     'name'              => $val_patient['name'],
                    //     'last_name'         => $val_patient['last_name'],
                    //     'sex_code'          => $val_patient['sex_code'],
                    //     'birth_date'        => date('d/m/Y', strtotime($val_patient['birth_date'])),
                    //     'icd10'             => $val_cancer['icd10_code'],
                    //     'treatment_code'    => $val_treatment['treatment_code'],
                    //     'trt_date'          => date('d/m/Y', strtotime($val_treat['treatment_date'])),
                    //     'trt_date_dt'       => $val_treat['treatment_date']
                    // ];

                    // DB::table('data_treatment_group')->insert($val_can_treat);
                }
            }

            // Files
            if (isset($input['curr_files']) && sizeof($input['curr_files']) > 0) {
                foreach ($input['curr_files'] as $idx => $val_file) {
                    if ($request->hasFile('curr_files.' . $idx . '.file')) {
                        $file = $request->file('curr_files.' . $idx . '.file');
                        $file_size = $file->getSize();
                        $store_path = "upload/files/" . Carbon::now()->format('Ymd');
                        $name = md5(uniqid(rand(), true)) . str_replace(' ', '-', $file->getClientOriginalName());
                        $file->move(public_path('/' . $store_path), $name);

                        $windows_path               = public_path(str_replace('/', '\\', $store_path) . '\\' . $name);
                        $val_file['file_path']      = $store_path . '/' . $name;
                        $val_file['file_name']      = $file->getClientOriginalName();
                        $val_file['file_size']      = $file_size;
                        $val_file['patient_id']     = $patient_id;
                        $val_file['cancer_id']      = $cancer_id;
                        $val_file['updated_at']     = Carbon::now()->format('Y-m-d H:i:s');
                        $val_file['updated_by']     = $user->id;
                        $val_file['created_at']     = Carbon::now()->format('Y-m-d H:i:s');
                        $val_file['created_by']     = $user->id;

                        unset($val_file['file']);
                        DB::table('data_file')->insert($val_file);
                    }
                }
            }

            DB::commit();

            return $val_cancer;
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    private function getCancerFromInputSentToApi($patient_id, $input)
    {
        $patient            = DB::table('data_patient')->where('id', $patient_id)->first();

        $birth_date         = Carbon::createFromFormat('Y-m-d', $patient->birth_date);                     // ค.ศ.
        $diagnosis_date     = Carbon::createFromFormat('d/m/Y', $input['diagnosis_date'])->subYears(543);   // ค.ศ.
        $topo_code          = static::getCodeFromText($input, 'topo_text', 'bd_topo');
        $morphology_code    = static::getCodeFromText($input, 'morphology_text', 'bd_mor');
        $behaviour_code     = static::getCodeFromText($input, 'behaviour_text', 'bd_behaviour');
        $grade_code         = static::getCodeFromText($input, 'grade_text', 'bd_grade');

        $result = (object)[
            "sex"                           => strval($patient->sex_code),
            "birth_date"                    => $birth_date->addYears(543)->format('Ymd'),
            "diag_date"                     => $diagnosis_date->addYears(543)->format('Ymd'),               // 26
            "age"                           => intval($birth_date->diff($diagnosis_date)->format('%y')),
            "topography"                    => $topo_code ? strval($topo_code) : null,                      // 30
            "morphology"                    => $morphology_code ? strval($morphology_code) : null,          // 33
            "behaviour"                     => $behaviour_code,                                                                             // 34
            "basis"                         => strval(substr($input['diagnosis_text'], 0, 1)),
            "grade"                         => $grade_code ? strval($grade_code) : null,                                                                                 // 35
            "stage"                         => isset($input['stage_code']) ? strval($input['stage_code']) : null,           // 38
            "extend"                        => isset($input['extension_code']) ? strval($input['extension_code']) : null,   // 39
            "metastasis_bone"               => isset($input['met_1']) && $input['met_1'] == 1 ? 'Y' : 'N',
            "metastasis_bone_date"          => isset($input['met_1_date']) && $input['met_1_date'] != '' ? Carbon::createFromFormat('d/m/Y', $input['met_1_date'])->format('Ymd') : "",
            "metastasis_brain"              => isset($input['met_2']) && $input['met_2'] == 1 ? 'Y' : 'N',
            "metastasis_brain_date"         => isset($input['met_2_date']) && $input['met_2_date'] != '' ? Carbon::createFromFormat('d/m/Y', $input['met_2_date'])->format('Ymd') : "",
            "metastasis_liver"              => isset($input['met_3']) && $input['met_3'] == 1 ? 'Y' : 'N',
            "metastasis_liver_date"         => isset($input['met_3_date']) && $input['met_3_date'] != '' ? Carbon::createFromFormat('d/m/Y', $input['met_3_date'])->format('Ymd') : "",
            "metastasis_lung"               => isset($input['met_4']) && $input['met_4'] == 1 ? 'Y' : 'N',
            "metastasis_lung_date"          => isset($input['met_4_date']) && $input['met_4_date'] != '' ? Carbon::createFromFormat('d/m/Y', $input['met_4_date'])->format('Ymd') : "",
            "metastasis_lymph_node"         => isset($input['met_5']) && $input['met_5'] == 1 ? 'Y' : 'N',
            "metastasis_lymph_node_date"    => isset($input['met_5_date']) && $input['met_5_date'] != '' ? Carbon::createFromFormat('d/m/Y', $input['met_5_date'])->format('Ymd') : "",
            "metastasis_peritoneum"         => isset($input['met_6']) && $input['met_6'] == 1 ? 'Y' : 'N',
            "metastasis_peritoneum_date"    => isset($input['met_6_date']) && $input['met_6_date'] != '' ? Carbon::createFromFormat('d/m/Y', $input['met_6_date'])->format('Ymd') : "",
            "metastasis_other"              => isset($input['met_7']) && $input['met_7'] == 1 ? 'Y' : 'N',
            "metastasis_other_date"         => isset($input['met_7_date']) && $input['met_7_date'] != '' ? Carbon::createFromFormat('d/m/Y', $input['met_7_date'])->format('Ymd') : "",
            "metastasis_other_orgen"        => isset($input['met_7_other']) && $input['met_7_other'] != '' ? $input['met_7_other'] : "",
            'clinical_summary'              => isset($input['clinical_summary']) && $input['clinical_summary'] != '' ? $input['clinical_summary'] : "",
        ];

        return $result;
    }

    public function patientCancerHistory(Request $request)
    {
        return response()->json($this->getPatientCancerHistory($request), 200, []);
    }

    private function getCancerFromTableSendToApi($param)
    {
        $cancer     = DB::table($param['table_name'])
            ->where('patient_id', $param['patient_id'])
            ->where('topo_id', $param['topo_id'])
            ->first();

        // ->where(function ($query) use ($param) {
        //     foreach ($param['condition'] as $value) {
        //         $query->where($value['field_name'], $value['field_value']);
        //     }
        // })
        //     ->first();

        // ถ้าไม่พบใบสรุปที่ตรงกับ คนไข้ และ Topo ปัจจุบัน
        if (!$cancer) {
            return null;
        }

        $patient            = DB::table('data_patient')->where('id', $cancer->patient_id)->first();
        $birth_date         = Carbon::createFromFormat('Y-m-d', $patient->birth_date);
        $diagnosis_date     = Carbon::createFromFormat('Y-m-d', $cancer->diagnosis_date);

        $result = (object)[
            "sex"                           => strval($patient->sex_code),
            "birth_date"                    => $birth_date->addYears(543)->format('Ymd'),
            "diag_date"                     => $diagnosis_date->addYears(543)->format('Ymd'),                                   // 26
            "age"                           => intval($birth_date->diff($diagnosis_date)->format('%y')),
            "topography"                    => strval($cancer->topo_code),                                                      // 30
            "morphology"                    => substr($cancer->morphology_text, 0, strpos($cancer->morphology_text, ' ')),      // 33
            "behaviour"                     => strval($cancer->behaviour_code),                                                 // 34
            "basis"                         => strval($cancer->diagnosis_code),
            "grade"                         => strval(substr($cancer->grade_text, 0, strpos($cancer->grade_text, ' '))),        // 35
            "stage"                         => strval($cancer->stage_code),                                                     // 38
            "extend"                        => strval($cancer->extension_code),                                                 // 39
            "metastasis_bone"               => $cancer->met_1 == 0 ? 'N' : 'Y',
            "metastasis_bone_date"          => $cancer->met_1_date ? Carbon::createFromFormat('Y-m-d', $cancer->met_1_date)->addYears(543)->format('Ymd') : "",
            "metastasis_brain"              => $cancer->met_2 == 0 ? 'N' : 'Y',
            "metastasis_brain_date"         => $cancer->met_2_date ? Carbon::createFromFormat('Y-m-d', $cancer->met_2_date)->addYears(543)->format('Ymd') : "",
            "metastasis_liver"              => $cancer->met_3 == 0 ? 'N' : 'Y',
            "metastasis_liver_date"         => $cancer->met_3_date ? Carbon::createFromFormat('Y-m-d', $cancer->met_3_date)->addYears(543)->format('Ymd') : "",
            "metastasis_lung"               => $cancer->met_4 == 0 ? 'N' : 'Y',
            "metastasis_lung_date"          => $cancer->met_4_date ? Carbon::createFromFormat('Y-m-d', $cancer->met_4_date)->addYears(543)->format('Ymd') : "",
            "metastasis_lymph_node"         => $cancer->met_5 == 0 ? 'N' : 'Y',
            "metastasis_lymph_node_date"    => $cancer->met_5_date ? Carbon::createFromFormat('Y-m-d', $cancer->met_5_date)->addYears(543)->format('Ymd') : "",
            "metastasis_peritoneum"         => $cancer->met_6 == 0 ? 'N' : 'Y',
            "metastasis_peritoneum_date"    => $cancer->met_6_date ? Carbon::createFromFormat('Y-m-d', $cancer->met_6_date)->addYears(543)->format('Ymd') : "",
            "metastasis_other"              => $cancer->met_7 == 0 ? 'N' : 'Y',
            "metastasis_other_date"         => $cancer->met_7_date ? Carbon::createFromFormat('Y-m-d', $cancer->met_7_date)->addYears(543)->format('Ymd') : "",
            "metastasis_other_orgen"        => $cancer->met_7_other ? $cancer->met_7_other : "",
        ];

        return $result;
    }

    private function checkCancerFromApi($params)
    {
        $suth_url       = 'https://caw.canceranywhere.com/check_mp';
        $auth_username  = 'tcb_2022';
        $auth_password  = 'tcb2022HAusdf7ew7YHyqw76jhjqwe';
        $response = Http::withoutVerifying()
            ->withBasicAuth($auth_username, $auth_password)
            ->withBody(json_encode($params), 'application/json')
            ->post($suth_url);

        Log::info('Cancer    : checkCancerFromApi: ' . json_encode($params));

        $body = $response->json();

        Log::info('Cancer    : checkCancerFromApi: ' . json_encode($body));

        return $body;
    }

    public static function getValCancer($patient_id, $input, $mor_key)
    {
        $user               = Auth::user();
        $patient            = DB::table('data_patient')->where('id', $patient_id)->first();

        if (isset($input['topo_text'])) {
            Log::info('Cancer    : topo_text: ' . $input['topo_text']);
        }

        $topo_code  = static::getCodeFromText($input, 'topo_text', 'bd_topo');
        $topo_id    = $topo_code ? substr($topo_code, 0, 2) : null;
        $icd10_code = isset($input['icd10_code']) ? str_replace('.', '', strtoupper($input['icd10_code'])) : null;
        $icd10_text = $icd10_code ? static::getIcd10Text($icd10_code) : null;
        $icd10_group_id = $icd10_code ? static::getIcd10group($icd10_code) : null;

        $val = [
            'hospital_code'             => $patient->hospital_code,
            'patient_id'                => $patient_id,
            'entrance_date'             => $input['entrance_date'] != '' ? getDateTimeTHtoEN($input['entrance_date']) : null,
            'first_entrance_date'       => $input['first_entrance_date'] != '' ? getDateTimeTHtoEN($input['first_entrance_date']) : null,
            'finance_support_code'      => static::getCodeFromText($input, 'finance_support_text', 'bd_finance_support'),        // isset($input['finance_support_text']) && trim($input['finance_support_text']) != '' ? substr($input['finance_support_text'], 0, strpos($input['finance_support_text'], ' ')) : null,
            'finance_support_text'      => isset($input['finance_support_text']) ? $input['finance_support_text'] : null,
            'diagnosis_date'            => $input['diagnosis_date'] != '' ? getDateTimeTHtoEN($input['diagnosis_date']) : null,
            'age'                       => isset($input['diagnosis_date']) ? calculateAge($patient->birth_date, getDateTimeTHtoEN($input['diagnosis_date'])) : null,
            'diagnosis_code'            => isset($input['diagnosis_text']) ? substr($input['diagnosis_text'], 0, 1) : null,
            'diagnosis_text'            => isset($input['diagnosis_text']) ? $input['diagnosis_text'] : null,
            'diagnosis_out'             => isset($input['diagnosis_out']) ? $input['diagnosis_out'] : 0,
            'excision_in_cut_date'      => isset($input['excision_in_cut_date']) && $input['excision_in_cut_date'] != '' ? getDateTimeTHtoEN($input['excision_in_cut_date']) : null,
            'excision_in_read_date'     => isset($input['excision_in_read_date']) && $input['excision_in_read_date'] != '' ? getDateTimeTHtoEN($input['excision_in_read_date']) : null,
            'topo_id'                   => $topo_id,
            'topo_code'                 => $topo_code,
            'topo_text'                 => isset($input['topo_text']) ? $input['topo_text'] : null,
            'recurrent'                 => isset($input['recurrent']) ? $input['recurrent'] : null,
            'recurrent_date'            => isset($input['recurrent_date']) && $input['recurrent_date'] != '' ? getDateTimeTHtoEN($input['recurrent_date']) : null,
            'morphology_code'           => $mor_key,
            'morphology_text'           => isset($input['morphology_text']) ? $input['morphology_text'] : null,
            'behaviour_code'            => static::getCodeFromText($input, 'behaviour_text', 'bd_behaviour'),
            'behaviour_text'            => isset($input['behaviour_text']) ? $input['behaviour_text'] : null,
            'grade_code'                => static::getCodeFromText($input, 'grade_text', 'bd_grade'),  // isset($input['grade_text']) ? substr($input['grade_text'], 0, strpos($input['grade_text'], ' ')) : null,
            'grade_text'                => isset($input['grade_text']) ? $input['grade_text'] : null,
            'm_code'                    => isset($input['m_code']) ? intval($input['m_code']) : null,
            'n_code'                    => isset($input['n_code']) ? intval($input['n_code']) : null,
            't_code'                    => isset($input['t_code']) ? intval($input['t_code']) : null,
            'tnm_date'                  => isset($input['tnm_date']) && $input['tnm_date'] != '' ? getDateTimeTHtoEN($input['tnm_date']) : null,
            'stage_code'                => isset($input['stage_code']) ? intval($input['stage_code']) : null,
            'extension_code'            => isset($input['extension_code']) ? intval($input['extension_code']) : null,
            'icd10_code'                => $icd10_code,
            'icd10_text'                => $icd10_text,
            'icd10_group_id'            => $icd10_group_id,
            'met_1'                     => isset($input['met_1']) ? $input['met_1'] : 0,
            'met_1_date'                => isset($input['met_1_date']) && $input['met_1_date'] != '' ? getDateTimeTHtoEN($input['met_1_date']) : null,
            'met_2'                     => isset($input['met_2']) ? $input['met_2'] : 0,
            'met_2_date'                => isset($input['met_2_date']) && $input['met_2_date'] != '' ? getDateTimeTHtoEN($input['met_2_date']) : null,
            'met_3'                     => isset($input['met_3']) ? $input['met_3'] : 0,
            'met_3_date'                => isset($input['met_3_date']) && $input['met_3_date'] != '' ? getDateTimeTHtoEN($input['met_3_date']) : null,
            'met_4'                     => isset($input['met_4']) ? $input['met_4'] : 0,
            'met_4_date'                => isset($input['met_4_date']) && $input['met_4_date'] != '' ? getDateTimeTHtoEN($input['met_4_date']) : null,
            'met_5'                     => isset($input['met_5']) ? $input['met_5'] : 0,
            'met_5_date'                => isset($input['met_5_date']) && $input['met_5_date'] != '' ? getDateTimeTHtoEN($input['met_5_date']) : null,
            'met_6'                     => isset($input['met_6']) ? $input['met_6'] : 0,
            'met_6_date'                => isset($input['met_6_date']) && $input['met_6_date'] != '' ? getDateTimeTHtoEN($input['met_6_date']) : null,
            'met_7'                     => isset($input['met_7']) ? $input['met_7'] : 0,
            'met_7_date'                => isset($input['met_7_date']) && $input['met_7_date'] != '' ? getDateTimeTHtoEN($input['met_7_date']) : null,
            'met_7_other'               => isset($input['met_7_other']) ? $input['met_7_other'] : null,
            'clinical_summary'          => isset($input['clinical_summary']) ? $input['clinical_summary'] : null,
            'created_by'                => $user->id,
            'created_at'                => Carbon::now()->format('Y-m-d H:i:s'),
            'user_cancer'               => $user->roleCancer == 'Y' ? 1 : 0,
        ];

        return $val;
    }

    public static function getIcd10Text($icd10_code)
    {
        $icd10 = DB::table('bd_icd10')->where('ICD10', strtoupper($icd10_code))->first();
        if ($icd10) {
            return $icd10->ICD10_TEXT;
        } else {
            return null;
        }
    }

    public static function getIcd10group($icd10_code)
    {
        $group = DB::table('bd_sitegroup')->where('icd10_list', 'like', '%' . substr($icd10_code, 0, 3) . '%')->first();

        if (!$group) {
            return null;
        }

        return $group->group_id;
    }

    public static function getCodeFromText($input, $field_name, $table_name)
    {
        if (isset($input[$field_name]) && strlen($input[$field_name]) > 0) {
            $vals = explode(' ', trim($input[$field_name]));
            if (sizeof($vals) > 0) {
                $rec = DB::table($table_name)->where('code', $vals[0])->first();
                if ($rec) {
                    return $vals[0];
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    // private function getReference()
    // {
    //     $finance_support_list   = DB::table('bd_finance_support')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->toArray();
    //     $diagnosis_list         = DB::table('bd_diagnosis')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->toArray();
    //     $topo_list              = DB::table('bd_topo')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->toArray();
    //     $morphology_list        = DB::table('bd_mor')->pluck(DB::raw("concat(code, ' ', name) as name"), 'key');
    //     $behaviour_list         = DB::table('bd_behaviour')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->toArray();
    //     $grade_list             = DB::table('bd_grade')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->toArray();
    //     $t_list                 = DB::table('bd_t')->pluck('name', 'code')->toArray();
    //     $n_list                 = DB::table('bd_n')->pluck('name', 'code')->toArray();
    //     $m_list                 = DB::table('bd_m')->pluck('name', 'code')->toArray();
    //     $stage_list             = DB::table('bd_stage')->pluck('name', 'code')->toArray();
    //     $extension_list         = DB::table('bd_extend')->pluck('name', 'code')->toArray();
    //     $icd10_list             = DB::table('bd_icd10')->pluck(DB::raw("ICD10 as name"), DB::raw('ICD10_KEY as code'))->toArray();
    //     $icd10_list_arr         = DB::table('bd_icd10')->pluck(DB::raw("ICD10_TEXT as name"), DB::raw('ICD10 as code'))->toArray();
    //     $treatment_list         = DB::table('bd_treatment')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->toArray();

    //     return [
    //         'finance_support_list'  => $finance_support_list,
    //         'diagnosis_list'        => $diagnosis_list,
    //         'topo_list'             => $topo_list,
    //         'morphology_list'       => $morphology_list,
    //         'behaviour_list'        => $behaviour_list,
    //         'grade_list'            => $grade_list,
    //         't_list'                => $t_list,
    //         'n_list'                => $n_list,
    //         'm_list'                => $m_list,
    //         'stage_list'            => $stage_list,
    //         'extension_list'        => $extension_list,
    //         'icd10_list'            => $icd10_list,
    //         'icd10_list_arr'        => $icd10_list_arr,
    //         'treatment_list'        => $treatment_list,
    //     ];
    // }

    public static function getCancer($id)
    {
        $cancer = DB::table('data_cancer')->where('id', $id)->first();

        return $cancer;
    }

    public static function getCancersByPatientId($patient_id)
    {
        $cancers = DB::table('data_cancer')
            ->select(
                DB::raw('bd_hospital.name as hospital_name'),
                'data_cancer.*'
            )
            ->join('bd_hospital', 'bd_hospital.code', '=', 'data_cancer.hospital_code')
            ->where('patient_id', $patient_id)
            ->get();

        return $cancers;
    }

    public static function getCancersAndSumByPatientId($patient_id)
    {
        $datasum = DB::table('data_cancer_summary')
            ->select(
                'data_cancer_summary.*',
                DB::raw('bd_hospital.name as hospital_name'),
                DB::raw("'summary' as flag")
            )
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer_summary.hospital_code')
            ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
            ->where('data_patient.id', $patient_id)
            ->orderBy('data_cancer_summary.created_at', 'desc')
            ->get()
            ->toArray();

        $dataset = DB::table('data_cancer')
            ->select(
                'data_cancer.*',
                DB::raw('bd_hospital.name as hospital_name'),
                DB::raw("'cancer' as flag")
            )
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer.hospital_code')
            ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer.patient_id')
            ->where('data_patient.id', $patient_id)
            ->orderBy('data_cancer.entrance_date', 'desc')
            ->get()
            ->toArray();

        return array_merge($datasum, $dataset);
    }

    public static function getCancersAndSumByCid($cid)
    {
        $datasum = DB::table('data_cancer_summary')
            ->select(
                'data_cancer_summary.*',
                DB::raw('bd_hospital.name as hospital_name'),
                DB::raw("'summary' as flag")
            )
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer_summary.hospital_code')
            ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
            ->where('data_patient.cid', $cid)
            ->orderBy('data_cancer_summary.created_at', 'desc')
            ->get()
            ->toArray();

        $dataset = DB::table('data_cancer')
            ->select(
                'data_cancer.*',
                DB::raw('bd_hospital.name as hospital_name'),
                DB::raw("'cancer' as flag")
            )
            ->leftJoin('bd_hospital', 'bd_hospital.code', '=', 'data_cancer.hospital_code')
            ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer.patient_id')
            ->where('data_patient.cid', $cid)
            ->orderBy('data_cancer.entrance_date', 'desc')
            ->get()
            ->toArray();

        return array_merge($datasum, $dataset);
    }

    public static function getFilesByPatientId($patient_id)
    {
        $files = DB::table('data_file')->where('patient_id', $patient_id)->orderBy('created_at', 'desc')->get();

        return $files;
    }

    public static function getFilesByCid($cid)
    {
        $files1 = DB::table('data_file as F')
            ->join('data_patient as P', 'F.patient_id', '=', 'P.id')
            ->where('P.cid', $cid)
            ->get()
            ->toArray();

        $files2 = DB::table('data_refer_cancer_file as CF')
            ->join('data_refer_patient as RP', 'RP.id', '=', 'CF.patient_id')
            ->select('RP.*', 'CF.*')
            ->where('RP.cid', $cid)
            ->get()
            ->toArray();

        return array_merge($files1, $files2);
    }

    public function getcancerStage(Request $request)
    {
        $T = $request->input('T');
        $M = $request->input('M');
        $N = $request->input('N');
        $topo_code = $request->input('topo_code');

        // $data = DB::table('bd_tnm_stage')->select('STAGE_CODE','SITE_NAME')->where('T', $T)
        // ->where('N', $N)
        // ->where('M', $M)
        // ->where('ICD_O_LIST', 'LIKE', '%'.$topo_code.'%')
        // ->get();

        $stage_list = DB::table('bd_tnm_stage')
            ->join('bd_stage', 'bd_tnm_stage.STAGE_CODE', '=', 'bd_stage.code')
            ->select(
                'bd_stage.code as code',
                DB::raw('CONCAT(bd_stage.stage_group_id, " ", bd_tnm_stage.STAGE) as name')
            )
            ->where('ICD_O_LIST', 'LIKE', '%' . $topo_code . '%')
            ->distinct()
            ->get();

        return response()->json([
            'stage_list' => $stage_list
        ]);
    }

    public function getCancerTNM(Request $request)
    {

        $topo_code = $request->input('topo_code');

        $t_list = DB::table('bd_tnm_stage')
            ->distinct()
            ->join('bd_t', 'bd_tnm_stage.T', '=', 'bd_t.name')
            ->where('ICD_O_LIST', 'like', '%' . $topo_code . '%')
            ->select('bd_t.code as code', 'bd_tnm_stage.T as name')
            ->get();

        $n_list = DB::table('bd_tnm_stage')
            ->distinct()
            ->where('ICD_O_LIST', 'like', '%' . $topo_code . '%')
            ->select('N as code', 'N as name')
            ->get();

        $m_list = DB::table('bd_tnm_stage')
            ->distinct()
            ->where('ICD_O_LIST', 'like', '%' . $topo_code . '%')
            ->select('M as code', 'M as name')
            ->get();

        return response()->json([
            't_list' => $t_list,
            'n_list' => $n_list,
            'm_list' => $m_list
        ]);
    }
    public function app_cancer(Request $request)
    {
        $cid = $request->query('cid');

        $cancers = DB::table('data_cancer_summary')
            ->join('data_patient', function (JoinClause $join) {
                $join->on('data_cancer_summary.hospital_code', '=', 'data_patient.hospital_code')
                    ->on('data_cancer_summary.patient_id', '=', 'data_patient.id');
            })
            ->join('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
            ->select(
                'data_cancer_summary.patient_id',
                'data_cancer_summary.hospital_code',
                'bd_hospital.name as hospital_name',
                'data_cancer_summary.diagnosis_date',
                'data_cancer_summary.icd10_code',
                'data_cancer_summary.last_entrance_date',
                'data_patient.cid'
            )
            ->where('data_patient.cid', $cid)
            ->get();

        foreach ($cancers as $cancer) {
            $cancer->treatments = [];
            $treatments = DB::table('data_cancer_summary_treatment')
                ->distinct()
                ->select(['id', 'icd10_code', 'treatment_code', 'treatment_date', 'treatment_date_end'])
                ->where('patient_id', $cancer->patient_id)
                ->orderBy('treatment_date', 'asc')
                ->get();

            $temp = [];
            $currentTreatment = null;

            foreach ($treatments as $treatment) {
                $type = $treatment->treatment_code;
                $date = Carbon::parse($treatment->treatment_date);

                if (!$currentTreatment || $currentTreatment['treatment_code'] !== $type || ($type === '1 Surgery' && $currentTreatment['treatment_date_end'] !== $date->format('Y-m-d'))) {
                    if ($currentTreatment) {
                        $temp[] = $currentTreatment; // Add the last treatment to results
                    }
                    $currentTreatment = [
                        'treatment_code' => $type,
                        'treatment_date' => $date->format('Y-m-d'),
                        'treatment_date_end' => $type === '1 Surgery'
                            ? $date->format('Y-m-d')
                            : $date->format('Y-m-d'),
                    ];
                } else {
                    $currentTreatment['treatment_date_end'] = $type === '1 Surgery'
                        ? $date->format('Y-m-d')
                        : $date->format('Y-m-d');
                }
            }

            if ($currentTreatment) {
                $temp[] = $currentTreatment; // Add the last group
            }

            $cancer->treatments = $temp;
        }

        return response()->json([
            'data' => $cancers
        ], 200);
    }

    public function autoApprove($cancers, $new)
    {
        // Simplified date processing
        $new->entrance_date = collect($cancers)
            ->pluck('entrance_date')
            ->filter()
            ->map(fn($date) => Carbon::parse($date)->toDateString())
            ->sort()
            ->first();

        $finance_support_text = collect($cancers)
            ->pluck('finance_support_text')
            ->unique()
            ->reject(fn($value) => $value === '9 อื่นๆ')
            ->sortDesc()
            ->first();

        $new->finance_support_text = $finance_support_text;
        $new->finance_support_code = substr($finance_support_text, 0, 1);

        $new->diagnosis_date = collect($cancers)
            ->pluck('diagnosis_date')
            ->filter()
            ->map(fn($date) => Carbon::parse($date)->toDateString())
            ->sort()
            ->first();

        $diagnosis_text = collect($cancers)
            ->pluck('diagnosis_text')
            ->unique()
            ->sortDesc()
            ->first();

        $new->diagnosis_text = $diagnosis_text;
        $new->diagnosis_code = substr($diagnosis_text, 0, 1);

        return $new;
    }

    public function storeCancerFromApi(Request $request)
    {
        //check authorization
        $username = $request->getUser();
        $password = $request->getPassword();

        $response = $this->authService($username, $password);
        if (!$response) {
            return response()->json([
                'status' => false
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'finance_support_code'              => 'required',
            'behaviour_code'                    => 'required',
            'icd10_code'                        => 'required',
            'visit_date'                        => 'required|date_format:Ymd',
            'cid'                               => 'required|regex:/^\d-\d{4}-\d{5}-\d{2}-\d$/',
            'first_entrance_date'               => 'date_format:Ymd|nullable',
        ], [
            'finance_support_code.required'     => 'Missing or not in list, please see reference',
            'behaviour_code.required'           => 'Missing or not in list, please see reference',
            'icd10_code.required'               => 'Missing or not in list, please see reference',
            'visit_date.required'               => "Missing or doesn't match the format or invalid date",
            'visit_date.date_format'            => "Format YYYYMMDD",
            'cid.required'                      => "Missing or doesn't match the format",
            'cid.regex'                         => "Format X-XXXX-XXXXX-XX-X",
        ]);

        if ($validator->fails()) {
            $errors = Arr::map($validator->errors()->toArray(), fn($messages) => $messages[0]);

            return response()->json(
                $errors,
                400
            );
        }

        // ค้นหาผู้ป่วยจาก cid และ hospital_code
        $patient = null;
        $input = $request->all();
        $input['hospital_code'] = $username;

        if ($input['cid'] == '0-0000-00000-00-0' || $input['cid'] == '9-9999-99999-99-9') {
            $patient = null;
            // $patient = DB::table('data_patient')
            //     ->where('hospital_code', $input['hospital_code'])
            //     ->where('cid', $input['cid'])
            //     ->where('hn_no', $input['hn'])
            //     ->first();
        } else {
            $patient = DB::table('data_patient')
                ->where('hospital_code', $input['hospital_code'])
                ->where('cid', $input['cid'])
                ->first();
        }

        if (!$patient) {
            return response()->json([
                'status' => false,
                'message' => 'Patient not found'
            ], 404);
        }

        $patient_id = $patient->id;

        $entrance_date = isset($input['visit_date']) ? getDateTimeFormStr($input['visit_date']) : null;
        $first_entrance_date = isset($input['first_entrance_date']) ? getDateTimeFormStr($input['first_entrance_date']) : null;

        DB::beginTransaction();

        try {
            $icd10_code = isset($input['icd10_code']) ? str_replace('.', '', strtoupper($input['icd10_code'])) : null;
            $icd10_text = $icd10_code ? static::getIcd10Text($icd10_code) : null;

            // ตรวจสอบว่ามีข้อมูลซ้ำหรือไม่
            $existing_record = DB::table('data_cancer')
                ->where('patient_id', $patient_id)
                ->where('entrance_date', $entrance_date)
                ->where('icd10_code', $icd10_code)
                ->where('source_id', 3)
                ->first();

            $finance_support = isset($input['finance_support_code']) ? DB::table('bd_finance_support')->where('code', $input['finance_support_code'])->first() : null;
            $bd_diagnosis = isset($input['diagnosis_code']) ? DB::table('bd_diagnosis')->where('code', $input['diagnosis_code'])->first() : null;
            $bd_behaviour = isset($input['behaviour_code']) ? DB::table('bd_behaviour')->where('code', $input['behaviour_code'])->first() : null;
            $bd_grade = isset($input['grade_code']) ? DB::table('bd_grade')->where('code', $input['grade_code'])->first() : null;
            $bd_topo = isset($input['topo_code']) ? DB::table('bd_topo')->where('code', $input['topo_code'])->first() : null;

            //ค้นหา TNM
            $bd_t = isset($input['t']) ? DB::table('bd_t')->where('name', 'LIKE', '%' . $input['t'] . '%')->first() : null;
            $bd_n = isset($input['n']) ? DB::table('bd_n')->where('name', 'LIKE', '%' . $input['n'] . '%')->first() : null;
            $bd_m = isset($input['m']) ? DB::table('bd_m')->where('name', 'LIKE', '%' . $input['m'] . '%')->first() : null;

            $val = [
                'hospital_code'        => $input['hospital_code'],
                'patient_id'           => $patient_id,
                'entrance_date'        => $entrance_date,
                'first_entrance_date'  => $first_entrance_date,
                'finance_support_code' => $input['finance_support_code'] ?? null,
                'finance_support_text' => isset($input['finance_support_code']) ? $input['finance_support_code'] . ' ' . $finance_support->name : null,
                'diagnosis_code'       => $input['diagnosis_code'] ?? null,
                'diagnosis_text'       => isset($input['diagnosis_code']) ? $input['diagnosis_code'] . ' ' . $bd_diagnosis->name : null,
                'recurrent'            => $input['recurrent'] ?? null,
                'recurrent_date'       => isset($input['recurrent_date']) ? getDateTimeFormStr($input['recurrent_date']) : null,
                'morphology_text'      => $input['morphology'] ?? null,
                'behaviour_code'       => $input['behaviour_code'] ?? null,
                'behaviour_text'       => $bd_behaviour->name ?? null,
                'grade_code'           => $input['grade_code'] ?? null,
                'grade_text'           => $bd_grade->name ?? null,
                'topo_id'              => $bd_topo ? substr($bd_topo->code, 2) : null,
                'topo_code'            => $bd_topo ? $bd_topo->code : null,
                'topo_text'            => $bd_topo ? $bd_topo->code . ' ' . $bd_topo->name : null,
                'm_code'               => isset($input['m']) ? $bd_m->code : null,
                'n_code'               => isset($input['n']) ? $bd_n->code : null,
                't_code'               => isset($input['t']) ? $bd_t->code : null,
                'tnm_date'             => isset($input['tnm_date']) ? getDateTimeFormStr($input['tnm_date']) : null,
                'stage_code'           => isset($input['stage_code']) ? intval($input['stage_code']) : null,
                'extension_code'       => isset($input['extension_code']) ? intval($input['extension_code']) : null,
                'icd10_code'           => $icd10_code,
                'icd10_text'           => $icd10_text,
                'clinical_summary'     => $input['clinical_summary'] ?? null,
                'updated_at'           => Carbon::now()->format('Y-m-d H:i:s'),
                'user_cancer'          => 0,
                'source_id'            => 3,
            ];

            $log = [
                'log_date_time' => Carbon::now()->format('Y-m-d H:i:s'),
                'log_type' => null,
                'hospital_code' => $request->getUser(),
                'ref' => $input['cid'] . '|' . $input['visit_date'] . '|' . $input['icd10_code'],
                'data' => json_encode($request->all())
            ];

            if ($existing_record) {
                // ถ้ามีข้อมูลอยู่แล้ว -> ทำการอัปเดต
                // DB::table('data_cancer')
                //     ->where('id', $existing_record->id)
                //     ->update($val);

                // $cancer_id = $existing_record->id;

                // $message = 'Update Success';

                // $log['log_type'] = 'API_CANCER_UPDATE';

                // DB::table('log_api')->insert($log);

                return response()->json([
                    'status'  => true,
                    'message' => 'Cancer already exists.',
                    'id'      => null,
                ], 200);
            } else {
                // ถ้าไม่มี -> เพิ่มข้อมูลใหม่
                $val['created_at'] = Carbon::now()->format('Y-m-d H:i:s');

                $cancer_id = DB::table('data_cancer')->insertGetId($val);

                $message = 'Success';

                $log['log_type'] = 'API_CANCER_NEW';

                DB::table('log_api')->insert($log);
            }

            DB::commit();

            return response()->json([
                'status'  => true,
                'message' => $message,
                'id'      => $cancer_id
            ], 200);
        } catch (Exception $e) {
            DB::rollback();

            Log::error($e);

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    public function storeTreatmentFromApi(Request $request)
    {
        //check authorization
        $username = $request->getUser();
        $password = $request->getPassword();

        $response = $this->authService($username, $password);
        if (!$response) {
            return response()->json([
                'status' => false
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'treatment_start_date' => 'required|date_format:Ymd',
            'treatment_end_date'   => 'date_format:Ymd|nullable',
            'treatment_code'       => 'required',
            'visit_date'           => 'required|date_format:Ymd',
            'cid'                  => 'required|regex:/^\d-\d{4}-\d{5}-\d{2}-\d$/',
            'icd10_code'           => 'required',
        ], [
            'treatment_start_date.required'    => "Missing or doesn't match the format or invalid date",
            'treatment_start_date.date_format' => "Format YYYYMMDD",
            'treatment_end_date.date_format' => "Format YYYYMMDD",
            'treatment_code.required'          => 'Missing or not in list, please see reference',
            'visit_date.required'              => "Missing or doesn't match the format or invalid date",
            'visit_date.required'              => "Format YYYYMMDD",
            'cid.required'                     => "Missing or doesn't match the format",
            'cid.regex'                        => "Format X-XXXX-XXXXX-XX-X",
            'icd10_code.required'              => "Missing or not in list, please see reference"
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors()->toArray(), 400);
        }

        $input = $request->all();
        $entrance_date = getDateTimeFormStr($input['visit_date']);

        // ค้นหาผู้ป่วยจาก CID และ hospital_code
        $cancer = DB::table('data_patient')
            ->select('data_cancer.id as cancer_id', 'data_patient.id as patient_id')
            ->join('data_cancer', 'data_patient.id', '=', 'data_cancer.patient_id')
            ->where('data_patient.cid', $request->input('cid'))
            ->where('data_patient.hospital_code', $request->getUser())
            ->where('data_cancer.entrance_date', $entrance_date)
            ->where('data_cancer.icd10_code', $input['icd10_code'])
            ->first();

        if (!$cancer) {
            return response()->json([
                'status' => false,
                'message' => 'Patient not found or no cancer data'
            ], 404);
        }

        $patient_id = $cancer->patient_id;
        $cancer_id = $cancer->cancer_id;

        $treatment_date = getDateTimeFormStr($input['treatment_start_date']);
        $treatment_date_end = isset($input['treatment_end_date']) ? getDateTimeFormStr($input['treatment_end_date']) : null;

        $treatment = DB::table('bd_treatment')
            ->where('code', $input['treatment_code'])
            ->first();

        DB::beginTransaction();

        $log = [
            'log_date_time' => Carbon::now()->format('Y-m-d H:i:s'),
            'log_type' => null,
            'hospital_code' => $request->getUser(),
            'ref' => $input['cid'] . '|' . $input['visit_date'] . '|' . $input['icd10_code'] . '|' . $input['treatment_code'],
            'data' => json_encode($request->all())
        ];

        try {
            $val_treat = [
                'patient_id'         => $patient_id,
                'cancer_id'          => $cancer_id,
                'treatment_type_id'  => $treatment->code,
                'treatment_code'     => $treatment->name,
                'treatment_date'     => $treatment_date,
                'treatment_date_end' => $treatment_date_end,
                'note'               => $input['note'] ?? null,
                'updated_at'         => Carbon::now()->format('Y-m-d H:i:s'),
                'created_at'         => Carbon::now()->format('Y-m-d H:i:s'),
            ];

            DB::table('data_treatment')->insert($val_treat);

            $log['log_type'] = 'API_TREATMENT_NEW';

            DB::table('log_api')->insert($log);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            Log::error($e);

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        return response()->json([
            'status' => true,
            'message' => 'Success',
        ], 200);
    }

    public function ocrSave(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'files' => 'required|file',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        $files = $request->file('files');

        // ส่งภาพไปยัง third-party OCR API
        $response = Http::attach(
            'files',                // ชื่อ field ของไฟล์ใน API ปลายทาง
            file_get_contents($files),
            $files->getClientOriginalName()
        )->post('https://asha-tech.co.th/ocr.php', [
            // เพิ่มพารามิเตอร์อื่น ๆ ถ้าต้องการ
        ]);

        if ($response->successful()) {
            return response()->json($response->json());
        } else {
            return response()->json([
                'message' => 'OCR failed',
                'error' => $response->body(),
            ], $response->status());
        }
    }

    public function ocrGet(Request $request)
    {
        $data = DB::table('ocr')->orderByDesc('id')->first();

        DB::table('ocr')->delete();

        return response()->json([
            'status' => true,
            'data' => $data,
        ], 200);
    }

    public function determineDiagnosisDateRequest(Request $request)
    {
        $input = $request->all();

        $diagnosis_date = $this->determineDiagnosisDate($input);

        return response()->json([
            'status' => true,
            'data' => $diagnosis_date,
        ], 200);
    }

    static public function determineDiagnosisDate(array $input): ?string
    {
        $temp = $input;

        $temp['excision_in_cut_date'] = getDateTimeTHtoEN($temp['excision_in_cut_date']);
        $temp['excision_in_read_date'] = getDateTimeTHtoEN($temp['excision_in_read_date']);
        $temp['diagnosis_date'] = getDateTimeTHtoEN($temp['diagnosis_date']);
        $temp['first_entrance_date'] = getDateTimeTHtoEN($temp['first_entrance_date']);
        $temp['entrance_date'] = getDateTimeTHtoEN($temp['entrance_date']);

        if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {
            $date_for_compare = [
                'excision_in_cut_date' => $temp['excision_in_cut_date'],
                'excision_in_read_date' => $temp['excision_in_read_date'],
                'diagnosis_date' => $temp['diagnosis_date'],
                'first_entrance_date' => $temp['first_entrance_date'],
                'entrance_date' => $temp['entrance_date'],
            ];

            //หาวันที่น้อยสุดในการรักษา
            $date_treatment = collect($temp['curr_treatments'])->map(function ($item) {
                return getDateTimeTHtoEN($item['treatment_date']);
            })->sort()->shift();

            foreach ($date_for_compare as $key => $value) {
                if ($date_for_compare[$key] > $date_treatment) {
                    $date_for_compare[$key] = null;
                }
            }

            //ตรวจสอบวันที่
            $diagnosis_date = null;

            if ($temp['diagnosis_out'] === '1') {
                $dates = [];

                $dates = array_filter([
                    $temp['first_entrance_date'] ?? null,
                    $temp['entrance_date'] ?? null,
                ]);

                // ตรวจสอบว่ามีค่าวันที่ให้เปรียบเทียบ
                if (!empty($dates)) {
                    $diagnosis_date = min($dates);
                }
            } else {
                // 31 32 29 26 27
                if ($temp['excision_in_cut_date'] != null) {
                    $diagnosis_date = $temp['excision_in_cut_date'];
                } else if ($temp['excision_in_read_date'] != null) {
                    $diagnosis_date = $temp['excision_in_read_date'];
                } else if ($temp['diagnosis_date'] != null) {
                    $diagnosis_date = $temp['diagnosis_date'];
                } else if ($temp['first_entrance_date'] != null) {
                    $diagnosis_date = $temp['first_entrance_date'];
                } else if ($temp['entrance_date'] != null) {
                    $diagnosis_date = $temp['entrance_date'];
                }
            }
        } else {
            //ตรวจสอบวันที่
            $diagnosis_date = null;

            if ($temp['diagnosis_out'] === '1') {
                $dates = [];

                $dates = array_filter([
                    $temp['first_entrance_date'] ?? null,
                    $temp['entrance_date'] ?? null,
                ]);

                // ตรวจสอบว่ามีค่าวันที่ให้เปรียบเทียบ
                if (!empty($dates)) {
                    $diagnosis_date = min($dates);
                }
            } else {
                // 31 32 29 26 27
                if ($temp['excision_in_cut_date'] != null) {
                    $diagnosis_date = $temp['excision_in_cut_date'];
                } else if ($temp['excision_in_read_date'] != null) {
                    $diagnosis_date = $temp['excision_in_read_date'];
                } else if ($temp['diagnosis_date'] != null) {
                    $diagnosis_date = $temp['diagnosis_date'];
                } else if ($temp['first_entrance_date'] != null) {
                    $diagnosis_date = $temp['first_entrance_date'];
                } else if ($temp['entrance_date'] != null) {
                    $diagnosis_date = $temp['entrance_date'];
                }
            }
        }

        return $diagnosis_date;
    }

    static public function determineDiagnosisDate1(array $input): ?string
    {
        $temp = $input;

        $temp['excision_in_cut_date'] = ($temp['excision_in_cut_date']);
        $temp['excision_in_read_date'] = ($temp['excision_in_read_date']);
        $temp['diagnosis_date'] = ($temp['diagnosis_date']);
        $temp['first_entrance_date'] = ($temp['first_entrance_date']);
        $temp['entrance_date'] = ($temp['entrance_date']);

        if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {
            $date_for_compare = [
                'excision_in_cut_date' => $temp['excision_in_cut_date'],
                'excision_in_read_date' => $temp['excision_in_read_date'],
                'diagnosis_date' => $temp['diagnosis_date'],
                'first_entrance_date' => $temp['first_entrance_date'],
                'entrance_date' => $temp['entrance_date'],
            ];

            //หาวันที่น้อยสุดในการรักษา
            $date_treatment = collect($temp['curr_treatments'])->map(function ($item) {
                return ($item['treatment_date']);
            })->sort()->shift();

            foreach ($date_for_compare as $key => $value) {
                if ($date_for_compare[$key] > $date_treatment) {
                    $date_for_compare[$key] = null;
                }
            }

            //ตรวจสอบวันที่
            $diagnosis_date = null;

            if ($temp['diagnosis_out'] === '1') {
                $dates = [];

                $dates = array_filter([
                    $temp['first_entrance_date'] ?? null,
                    $temp['entrance_date'] ?? null,
                ]);

                // ตรวจสอบว่ามีค่าวันที่ให้เปรียบเทียบ
                if (!empty($dates)) {
                    $diagnosis_date = min($dates);
                }
            } else {
                // 31 32 29 26 27
                if ($temp['excision_in_cut_date'] != null) {
                    $diagnosis_date = $temp['excision_in_cut_date'];
                } else if ($temp['excision_in_read_date'] != null) {
                    $diagnosis_date = $temp['excision_in_read_date'];
                } else if ($temp['diagnosis_date'] != null) {
                    $diagnosis_date = $temp['diagnosis_date'];
                } else if ($temp['first_entrance_date'] != null) {
                    $diagnosis_date = $temp['first_entrance_date'];
                } else if ($temp['entrance_date'] != null) {
                    $diagnosis_date = $temp['entrance_date'];
                }
            }
        } else {
            //ตรวจสอบวันที่
            $diagnosis_date = null;

            if ($temp['diagnosis_out'] === '1') {
                $dates = [];

                $dates = array_filter([
                    $temp['first_entrance_date'] ?? null,
                    $temp['entrance_date'] ?? null,
                ]);

                // ตรวจสอบว่ามีค่าวันที่ให้เปรียบเทียบ
                if (!empty($dates)) {
                    $diagnosis_date = min($dates);
                }
            } else {
                // 31 32 29 26 27
                if ($temp['excision_in_cut_date'] != null) {
                    $diagnosis_date = $temp['excision_in_cut_date'];
                } else if ($temp['excision_in_read_date'] != null) {
                    $diagnosis_date = $temp['excision_in_read_date'];
                } else if ($temp['diagnosis_date'] != null) {
                    $diagnosis_date = $temp['diagnosis_date'];
                } else if ($temp['first_entrance_date'] != null) {
                    $diagnosis_date = $temp['first_entrance_date'];
                } else if ($temp['entrance_date'] != null) {
                    $diagnosis_date = $temp['entrance_date'];
                }
            }
        }

        return $diagnosis_date;
    }

    public function getIcd10WaitApprove(Request $request)
    {
        $user = Auth::user();

        $patient_id = $request->get('patient_id');

        $cancers = DB::table('data_cancer')
            ->select('data_cancer.hospital_code', 'data_cancer.patient_id', 'data_cancer.icd10_code')
            ->where('data_cancer.hospital_code', $user->hosCode)
            ->where('data_cancer.patient_id', $patient_id)
            ->where('data_cancer.user_cancer', 0)
            ->groupBy('data_cancer.icd10_code')
            ->get();

        return response()->json([
            'status' => true,
            'data' => $cancers
        ], 200);
    }
}
