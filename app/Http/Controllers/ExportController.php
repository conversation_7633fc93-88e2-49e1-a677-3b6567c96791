<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Exports\DiagnosisExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExtensionExport;
use App\Exports\BrowseDataExport;
use App\Exports\BrowseDataVisitExport;
use App\Exports\HospitalData;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportController extends Controller
{
    public function diagnosis_data(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
                    ->leftJoin('bd_stage', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    // ->leftJoin('data_cancer_summary_treatment', 'data_cancer_summary_treatment.cancer_summary_id', '=', 'data_cancer_summary.id')
                    ->select(
                        'bd_diagnosis.code',
                        'bd_diagnosis.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count')
                    )
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    // ->when($area_id, function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', $area_id);
                    // })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(!isset($behaviour_code_start) && !isset($behaviour_code_end), function ($query) {
                        return $query->where('data_cancer_summary.behaviour_code', 3);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    // ->when($treatment_code, function ($query) use ($treatment_code) {
                    //     return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    // })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        return $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        return $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_diagnosis.code', 'bd_diagnosis.name')
                    ->orderBy('bd_diagnosis.code')
                    ->get();

                break;
            case 2:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->leftJoin('bd_stage', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    // ->leftJoin('data_cancer_summary_treatment', 'data_cancer_summary_treatment.cancer_summary_id', '=', 'data_cancer_summary.id')
                    ->select(
                        'bd_diagnosis.code',
                        'bd_diagnosis.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count')
                    )
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', $area_id);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(!isset($behaviour_code_start) && !isset($behaviour_code_end), function ($query) {
                        return $query->where('data_cancer_summary.behaviour_code', 3);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    // ->when($treatment_code, function ($query) use ($treatment_code) {
                    //     return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    // })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        return $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        return $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_diagnosis.code', 'bd_diagnosis.name')
                    ->orderBy('bd_diagnosis.code')
                    ->get();

                break;
            case 3:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->leftJoin('bd_stage', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    // ->leftJoin('data_cancer_summary_treatment', 'data_cancer_summary_treatment.cancer_summary_id', '=', 'data_cancer_summary.id')
                    ->select(
                        'bd_diagnosis.code',
                        'bd_diagnosis.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count')
                    )
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('hos.province_id', $area_id);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(!isset($behaviour_code_start) && !isset($behaviour_code_end), function ($query) {
                        return $query->where('data_cancer_summary.behaviour_code', 3);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    // ->when($treatment_code, function ($query) use ($treatment_code) {
                    //     return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    // })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        return $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        return $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_diagnosis.code', 'bd_diagnosis.name')
                    ->orderBy('bd_diagnosis.code')
                    ->get();

                break;
            case 4:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
                    ->leftJoin('bd_stage', 'bd_stage.code', '=', 'data_cancer_summary.stage_code')
                    ->select(
                        'bd_diagnosis.code',
                        'bd_diagnosis.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count')
                    )
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('data_cancer_summary.hospital_code', $area_id);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(!isset($behaviour_code_start) && !isset($behaviour_code_end), function ($query) {
                        return $query->where('data_cancer_summary.behaviour_code', 3);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('bd_stage.stage_group_id', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    // ->when($treatment_code, function ($query) use ($treatment_code) {
                    //     return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    // })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        return $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        return $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_diagnosis.code', 'bd_diagnosis.name')
                    ->orderBy('bd_diagnosis.code')
                    ->get();

                break;

            default:
                return response()->json(['error' => 'Invalid area_level_id'], 400);
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json(['data' => $results], 200);
    }

    public function diagnosis_dataPop(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurrent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    // ->leftJoin('data_cancer_summary_treatment', 'data_cancer_summary_treatment.cancer_summary_id', '=', 'data_cancer_summary.id')
                    ->select(
                        'bd_diagnosis.code',
                        'bd_diagnosis.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3)
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    // ->when($area_id, function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', $area_id);
                    // })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    // ->when($treatment_code, function ($query) use ($treatment_code) {
                    //     return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    // })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        return $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        return $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_diagnosis.code', 'bd_diagnosis.name')
                    ->orderBy('bd_diagnosis.code')
                    ->get();

                break;
            case 2:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->join('ref_provinces as v', 'v.id', '=', 'data_patient.address_province_id')
                    // ->leftJoin('data_cancer_summary_treatment', 'data_cancer_summary_treatment.cancer_summary_id', '=', 'data_cancer_summary.id')
                    ->select(
                        'bd_diagnosis.code',
                        'bd_diagnosis.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3)
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('v.health_region_id', $area_id);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    // ->when($treatment_code, function ($query) use ($treatment_code) {
                    //     return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    // })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        return $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        return $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_diagnosis.code', 'bd_diagnosis.name')
                    ->orderBy('bd_diagnosis.code')
                    ->get();

                break;
            case 3:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    // ->leftJoin('data_cancer_summary_treatment', 'data_cancer_summary_treatment.cancer_summary_id', '=', 'data_cancer_summary.id')
                    ->select(
                        'bd_diagnosis.code',
                        'bd_diagnosis.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3)
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('data_patient.address_province_id', $area_id);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    // ->when($treatment_code, function ($query) use ($treatment_code) {
                    //     return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    // })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        return $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        return $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_diagnosis.code', 'bd_diagnosis.name')
                    ->orderBy('bd_diagnosis.code')
                    ->get();

                break;
            case 4:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->leftJoin('bd_diagnosis', 'bd_diagnosis.code', '=', 'data_cancer_summary.diagnosis_code')
                    // ->leftJoin('data_cancer_summary_treatment', 'data_cancer_summary_treatment.cancer_summary_id', '=', 'data_cancer_summary.id')
                    ->select(
                        'bd_diagnosis.code',
                        'bd_diagnosis.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "1" THEN data_patient.id ELSE NULL END) as male_count'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = "2" THEN data_patient.id ELSE NULL END) as female_count'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3)
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        // return $query->whereRaw(
                        //     'GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?',
                        //     [$start_date, $end_date]
                        // );

                        return $query->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('data_cancer_summary.hospital_code', $area_id);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    // ->when($treatment_code, function ($query) use ($treatment_code) {
                    //     return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    // })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1'), function ($query) {
                        return $query->where('data_cancer_summary.recurrent', 1);
                    })
                    ->when(isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1'), function ($query) {
                        return $query->whereNotNull('data_patient.death_date');
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('bd_diagnosis.code', 'bd_diagnosis.name')
                    ->orderBy('bd_diagnosis.code')
                    ->get();

                break;

            default:
                return response()->json(['error' => 'Invalid area_level_id'], 400);
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json(['data' => $results], 200);
    }

    public function  exportToExcel(Request $request)
    {

        return Excel::download(new DiagnosisExport($request), 'diagnosis.xlsx');
    }
    //////Extension/////
    public function Extension_data(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurrent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = null;

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer_summary.extension_code')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->select(
                        'bd_extend.code',
                        'bd_extend.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) as male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) as female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3);

                if ($start_date && $end_date) {
                    // $results->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date]);
                    $results->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                }
                if ($gender_code) {
                    $results->where('data_patient.sex_code', $gender_code);
                }
                // if ($area_id) {
                //     $results->where('hos.health_region_id', $area_id);
                // }
                if ($behaviour_code_start && $behaviour_code_end) {
                    $results->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                }
                if ($stage_code_start && $stage_code_end) {
                    $results->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                }
                if ($morpho_start && $morpho_end) {
                    $results->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }
                if ($treatment_code) {
                    $results->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                }
                if ($age_start && $age_end) {
                    $results->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                }
                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $results->where('data_cancer_summary.recurrent', 1);
                }
                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $results->whereNotNull('data_patient.death_date');
                }
                if ($icd10_group_id) {
                    $results->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                }

                $results->groupBy('bd_extend.code', 'bd_extend.name');
                $results->orderBy('bd_extend.code',);

                $results =   $results->get();

                break;
            case 2:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer_summary.extension_code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->select(
                        'bd_extend.code',
                        'bd_extend.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) as male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) as female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3);

                if ($start_date && $end_date) {
                    // $results->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date]);
                    $results->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                }
                if ($gender_code) {
                    $results->where('data_patient.sex_code', $gender_code);
                }
                if ($area_id) {
                    $results->where('hos.health_region_id', $area_id);
                }
                if ($behaviour_code_start && $behaviour_code_end) {
                    $results->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                }
                if ($stage_code_start && $stage_code_end) {
                    $results->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                }
                if ($morpho_start && $morpho_end) {
                    $results->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }
                if ($treatment_code) {
                    $results->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                }
                if ($age_start && $age_end) {
                    $results->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                }
                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $results->where('data_cancer_summary.recurrent', 1);
                }
                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $results->whereNotNull('data_patient.death_date');
                }
                if ($icd10_group_id) {
                    $results->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                }

                $results->groupBy('bd_extend.code', 'bd_extend.name');
                $results->orderBy('bd_extend.code',);

                $results =   $results->get();

                break;
            case 3:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer_summary.extension_code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->select(
                        'bd_extend.code',
                        'bd_extend.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) as male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) as female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3);

                if ($start_date && $end_date) {
                    // $results->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date]);
                    $results->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                }
                if ($gender_code) {
                    $results->where('data_patient.sex_code', $gender_code);
                }
                if ($area_id) {
                    $results->where('hos.province_id', $area_id);
                }
                if ($behaviour_code_start && $behaviour_code_end) {
                    $results->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                }
                if ($stage_code_start && $stage_code_end) {
                    $results->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                }
                if ($morpho_start && $morpho_end) {
                    $results->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }
                if ($treatment_code) {
                    $results->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                }
                if ($age_start && $age_end) {
                    $results->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                }
                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $results->where('data_cancer_summary.recurrent', 1);
                }
                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $results->whereNotNull('data_patient.death_date');
                }
                if ($icd10_group_id) {
                    $results->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                }

                $results->groupBy('bd_extend.code', 'bd_extend.name');
                $results->orderBy('bd_extend.code',);

                $results =   $results->get();
                break;

            case 4:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer_summary.extension_code')
                    ->select(
                        'bd_extend.code',
                        'bd_extend.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) as male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) as female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3);

                if ($start_date && $end_date) {
                    // $results->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date]);
                    $results->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                }
                if ($gender_code) {
                    $results->where('data_patient.sex_code', $gender_code);
                }
                if ($area_id) {
                    $results->where('data_cancer_summary.hospital_code', $area_id);
                }
                if ($behaviour_code_start && $behaviour_code_end) {
                    $results->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                }
                if ($stage_code_start && $stage_code_end) {
                    $results->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                }
                if ($morpho_start && $morpho_end) {
                    $results->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }
                if ($treatment_code) {
                    $results->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                }
                if ($age_start && $age_end) {
                    $results->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                }
                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $results->where('data_cancer_summary.recurrent', 1);
                }
                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $results->whereNotNull('data_patient.death_date');
                }
                if ($icd10_group_id) {
                    $results->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                }

                $results->groupBy('bd_extend.code', 'bd_extend.name');
                $results->orderBy('bd_extend.code',);

                $results =   $results->get();
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json([
            'data' => $results,
        ], 200);
    }

    public function Extension_dataPop(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurrent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = null;

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer_summary.extension_code')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->select(
                        'bd_extend.code',
                        'bd_extend.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) as male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) as female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3);

                if ($start_date && $end_date) {
                    // $results->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date]);
                    $results->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                }
                if ($gender_code) {
                    $results->where('data_patient.sex_code', $gender_code);
                }
                // if ($area_id) {
                //     $results->where('hos.health_region_id', $area_id);
                // }
                if ($behaviour_code_start && $behaviour_code_end) {
                    $results->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                }
                if ($stage_code_start && $stage_code_end) {
                    $results->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                }
                if ($morpho_start && $morpho_end) {
                    $results->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }
                if ($treatment_code) {
                    $results->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                }
                if ($age_start && $age_end) {
                    $results->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                }
                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $results->where('data_cancer_summary.recurrent', 1);
                }
                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $results->whereNotNull('data_patient.death_date');
                }
                if ($icd10_group_id) {
                    $results->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                }

                $results->groupBy('bd_extend.code', 'bd_extend.name');
                $results->orderBy('bd_extend.code',);

                $results =   $results->get();

                break;
            case 2:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer_summary.extension_code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->join('ref_provinces as v', 'v.id', '=', 'data_patient.address_province_id')
                    ->select(
                        'bd_extend.code',
                        'bd_extend.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) as male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) as female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3);

                if ($start_date && $end_date) {
                    // $results->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date]);
                    $results->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                }
                if ($gender_code) {
                    $results->where('data_patient.sex_code', $gender_code);
                }
                if ($area_id) {
                    $results->where('v.health_region_id', $area_id);
                }
                if ($behaviour_code_start && $behaviour_code_end) {
                    $results->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                }
                if ($stage_code_start && $stage_code_end) {
                    $results->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                }
                if ($morpho_start && $morpho_end) {
                    $results->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }
                if ($treatment_code) {
                    $results->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                }
                if ($age_start && $age_end) {
                    $results->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                }
                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $results->where('data_cancer_summary.recurrent', 1);
                }
                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $results->whereNotNull('data_patient.death_date');
                }
                if ($icd10_group_id) {
                    $results->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                }

                $results->groupBy('bd_extend.code', 'bd_extend.name');
                $results->orderBy('bd_extend.code',);

                $results =   $results->get();

                break;
            case 3:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer_summary.extension_code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->select(
                        'bd_extend.code',
                        'bd_extend.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) as male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) as female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3);

                if ($start_date && $end_date) {
                    // $results->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date]);
                    $results->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                }
                if ($gender_code) {
                    $results->where('data_patient.sex_code', $gender_code);
                }
                if ($area_id) {
                    $results->where('data_patient.address_province_id', $area_id);
                }
                if ($behaviour_code_start && $behaviour_code_end) {
                    $results->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                }
                if ($stage_code_start && $stage_code_end) {
                    $results->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                }
                if ($morpho_start && $morpho_end) {
                    $results->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }
                if ($treatment_code) {
                    $results->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                }
                if ($age_start && $age_end) {
                    $results->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                }
                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $results->where('data_cancer_summary.recurrent', 1);
                }
                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $results->whereNotNull('data_patient.death_date');
                }
                if ($icd10_group_id) {
                    $results->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                }

                $results->groupBy('bd_extend.code', 'bd_extend.name');
                $results->orderBy('bd_extend.code',);

                $results =   $results->get();
                break;

            case 4:
                $results = DB::table('data_cancer_summary')
                    ->leftJoin('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->leftJoin('bd_extend', 'bd_extend.code', '=', 'data_cancer_summary.extension_code')
                    ->select(
                        'bd_extend.code',
                        'bd_extend.name',
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) as male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) as female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3);

                if ($start_date && $end_date) {
                    // $results->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date]);
                    $results->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date]);
                }
                if ($gender_code) {
                    $results->where('data_patient.sex_code', $gender_code);
                }
                if ($area_id) {
                    $results->where('data_cancer_summary.hospital_code', $area_id);
                }
                if ($behaviour_code_start && $behaviour_code_end) {
                    $results->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                }
                if ($stage_code_start && $stage_code_end) {
                    $results->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                }
                if ($morpho_start && $morpho_end) {
                    $results->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }
                if ($treatment_code) {
                    $results->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                }
                if ($age_start && $age_end) {
                    $results->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                }
                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $results->where('data_cancer_summary.recurrent', 1);
                }
                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $results->whereNotNull('data_patient.death_date');
                }
                if ($icd10_group_id) {
                    $results->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                }

                $results->groupBy('bd_extend.code', 'bd_extend.name');
                $results->orderBy('bd_extend.code',);

                $results =   $results->get();
                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json([
            'data' => $results,
        ], 200);
    }

    public function ExtensionTOExcel(Request $request)
    {
        return Excel::download(new ExtensionExport($request), 'extension_data.xlsx');
    }

    //////treatment//////
    public function treatment_data(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurrent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $results = null;

        switch ($area_level_id) {
            case 1:
                // Handle logic for area_level_id = 1
                break;
            case 2:
                // Handle logic for area_level_id = 2
                break;
            case 3:
                // Handle logic for area_level_id = 3
                break;
            case 4:
                $results = DB::table('data_cancer_summary')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary_treatment.patient_id', '=', 'data_cancer_summary.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->leftJoin('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->select(
                        'data_cancer_summary_treatment.treatment_code',
                        'data_cancer_summary.hospital_code',
                        DB::raw('COUNT(DISTINCT data_cancer_summary_treatment.patient_id) as treatment_count')
                    )
                    ->where('data_cancer_summary.behaviour_code', 3)
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', $gender_code);
                    })
                    ->when($start_date && $end_date, function ($query) use ($start_date, $end_date) {
                        return $query->whereBetween('data_cancer_summary.first_entrance_date', [$start_date, $end_date]);
                    })
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('data_cancer_summary.hospital_code', $area_id);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->groupBy('data_cancer_summary_treatment.treatment_code', 'data_cancer_summary.hospital_code') // แยกการนับตามโรงพยาบาล
                    ->orderBy('data_cancer_summary_treatment.treatment_code')
                    ->get();

                $totalCount = $results->sum('treatment_count');
                if ($totalCount === 0) {
                    return response()->json(['data' => []], 200);
                }

                $treatmentData = $results->map(function ($item) use ($totalCount) {
                    return [
                        'hospital_code' => $item->hospital_code, // ส่งข้อมูลรหัสโรงพยาบาล
                        'treatment_code' => $item->treatment_code,
                        'treatment_count' => $item->treatment_count,
                        'treatment_percentage' => (($item->treatment_count / $totalCount) * 100),
                    ];
                });

                return response()->json([
                    'data' => $treatmentData,
                ], 200);

                break;
        }
    }
    public function Export_Cancer_Patient_Details(Request $request)
    {
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');

        // ดึงข้อมูลจากตารางและจัดการ Join เพื่อดึงข้อมูลครบทุกคอลัมน์
        $data = DB::table('data_patient')
            ->leftJoin('data_cancer_summary', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
            ->leftJoin('bd_hospital', 'data_cancer_summary.hospital_code', '=', 'bd_hospital.code')
            ->select(
                'data_patient.id as PatientID',
                'bd_hospital.name as HospitalName',
                'data_patient.hn_no as HN',
                'data_patient.cid as NationalID',
                'data_patient.title_code as TitleCode',
                'data_patient.name as FirstName',
                'data_patient.last_name as LastName',
                'data_patient.birth_date as BirthDate',
                DB::raw('TIMESTAMPDIFF(YEAR, data_patient.birth_date, CURDATE()) as Age'),
                'data_patient.sex_code as Gender',
                'data_patient.nationality_code as Nationality',
                'data_patient.death_date as DeathDate',
                'data_cancer_summary.diagnosis_date as DiagnosisDate',
                'data_cancer_summary.behaviour_code as Behaviour',
                'data_cancer_summary.grade_code as Grade',
                'data_cancer_summary.stage_code as Stage',
                'data_cancer_summary.icd10_code as ICD10Code',
                'data_cancer_summary.icd10_text as ICD10Text',
                'data_cancer_summary.t_code as T',
                'data_cancer_summary.n_code as N',
                'data_cancer_summary.m_code as M',
                'data_cancer_summary.first_entrance_date as FirstEntranceDate'
            )
            ->whereBetween('data_cancer_summary.first_entrance_date', [$date_start, $date_end])
            ->get();

        // แปลงข้อมูลเป็น array
        $dataArray = $data->map(function ($item) {
            return (array) $item;
        })->toArray();

        return Excel::download(new class($dataArray) implements FromArray, WithHeadings, WithColumnWidths, WithMapping {
            private $data;

            public function __construct($data)
            {
                $this->data = $data;
            }

            public function array(): array
            {
                return $this->data;
            }

            public function headings(): array
            {
                return [
                    'PatientID',
                    'HospitalName',
                    'HN',
                    'NationalID',
                    'TitleCode',
                    'FirstName',
                    'LastName',
                    'BirthDate',
                    'Age',
                    'Gender',
                    'Nationality',
                    'DeathDate',
                    'DiagnosisDate',
                    'Behaviour',
                    'Grade',
                    'Stage',
                    'ICD10Code',
                    'ICD10Text',
                    'T',
                    'N',
                    'M',
                    'FirstEntranceDate'
                ];
            }

            public function columnWidths(): array
            {
                return [
                    'A' => 15,
                    'B' => 25,
                    'C' => 10,
                    'D' => 20,
                    'E' => 10,
                    'F' => 20,
                    'G' => 20,
                    'H' => 15,
                    'I' => 5,
                    'J' => 10,
                    'K' => 15,
                    'L' => 15,
                    'M' => 15,
                    'N' => 10,
                    'O' => 10,
                    'P' => 10,
                    'Q' => 15,
                    'R' => 20,
                    'S' => 5,
                    'T' => 5,
                    'U' => 5,
                    'V' => 20
                ];
            }

            public function map($row): array
            {
                return array_map(function ($value) {
                    return $value ?? 'N/A';
                }, $row);
            }
        }, 'Cancer_Patient_Details.xlsx');
    }
    public function Browse_data(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $hospital_code = array_filter($request->query('hospital', []));
        $type = $request->query('type');

        if (!$hospital_code) {
            return response()->json(['message' => 'กรุณาเลือกโรงพยาบาล'], 400);
        }

        $query = null;

        if ($type == 'SUM') {
            $query = DB::table('vw_data_export');

            if ($start_date && $end_date) {
                $query->whereBetween('vw_data_export.diagnosis_date', [$start_date, $end_date]);
            }
            if ($hospital_code) {
                $query->whereIn('vw_data_export.hospital_code', $hospital_code);
            }
        } else if ($type == 'VISIT') {
            $query = DB::table('vw_data_visit_export');

            if ($start_date && $end_date) {
                $query->whereBetween('vw_data_visit_export.diagnosis_date', [$start_date, $end_date]);
            }
            if ($hospital_code) {
                $query->whereIn('vw_data_visit_export.hospital_code', $hospital_code);
            }
        }


        $data = $query->get();

        $data = $data->groupBy('cid');

        foreach ($data as $cid => $value) {
            $data[$cid] = $value->first();
            $treatment = [];
            foreach ($value as $key => $val) {
                $treatment[] = [
                    'treatment_code' => $val->treatment_code,
                    'treatment_date' => $val->treatment_date,
                    'treatment_date_end' => $val->treatment_date_end,
                    'consult_date' => $val->consult_date,
                    'note' => $val->note,
                    'none_protocol' => $val->none_protocol,
                    'none_protocol_note' => $val->none_protocol_note,
                ];
            }
            
            $data[$cid]->treatment = $this->mergeArrayWithIndexedKeys($treatment);
        }

        $data = $data->values();

        return response()->json([
            'data' => $data,
        ], 200);
    }
    public function Browse_dataTOExcel(Request $request)
    {
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');
        $hospital_code = $request->query('hospital');

        if (empty($hospital_code)) {
            return response()->json(['message' => 'กรุณาเลือกโรงพยาบาล'], 400);
        }

        return Excel::download(new BrowseDataExport($date_start, $date_end, $hospital_code), 'browse_data.xlsx');
    }

    public function browseVisitData(Request $request)
    {
        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');
        $hospital_code = $request->query('hospital');

        if (empty($hospital_code)) {
            return response()->json(['message' => 'กรุณาเลือกโรงพยาบาล'], 400);
        }

        // สร้างชื่อไฟล์จากวันที่และเวลา
        $fileName = Carbon::now()->format('Y-m-d_H-i-s') . '_browse_data.xlsx';

        // ดาวน์โหลดไฟล์ Excel พร้อมกับชื่อไฟล์ที่กำหนด
        return Excel::download(new BrowseDataVisitExport($date_start, $date_end, $hospital_code), $fileName);
    }

    public function hospitalData(Request $request)
    {
        $hospital_code = $request->hospital;

        return Excel::download(new HospitalData, 'data' . now() . '.xlsx');
    }

    public function mergeArrayWithIndexedKeys(array $array): array
    {
        $result = [];

        foreach ($array as $index => $item) {
            $i = $index + 1;
            foreach ($item as $key => $value) {
                $result["{$key}{$i}"] = $value;
            }
        }

        return $result;
    }
}
