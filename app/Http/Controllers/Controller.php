<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use OneSignal;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;


    public function sendNotify($title, $body, $target_id, $type, $tokenList)
    {

        for ($i = 0; $i < count($tokenList); $i++) {
            try {

                // dump($tokenList[$i]);
                $params = [];
                $params['include_player_ids'] = [$tokenList[$i]];
                $params['android_accent_color'] = 'FF4D00';
                $params['small_icon'] = 'default_icon';
                $params['content_available'] = true;

                $headings = [
                    'en' => $title,
                    'th' => $title,
                ];

                $contents = [
                    'en' => $body,
                    'th' => $body,
                ];

                $params['headings'] = $headings;
                $params['contents'] = $contents;

                //data
                $params['data'] = [
                    'target_type' => $type,
                    'target_id' => $target_id,
                ];

                // dd($params);
                OneSignal::sendNotificationCustom($params);
            } catch (\Throwable $e) {
            }
        }
    }


    public function addNotifyLog($title, $body, $target_id, $type, $NotifyUser)
    {

        $notifyLogId = DB::table('notify_log')->insertGetId([
            'title' => $title,
            'detail' => $body,
            'target_id' => $target_id,
            'type' => $type,
            'created_at' => now(), // กรณีมี timestamp
            'updated_at' => now()  // กรณีมี timestamp
        ]);


        $result = array_unique($NotifyUser);
        sort($result); // เรียงลำดับ index ตามค่า

        //send notify app
        $userToken = [];
        for ($i = 0; $i < count($result); $i++) {

            $patient_device = DB::table('patient_device')
                ->leftJoin('data_patient', 'patient_device.cid', '=', 'data_patient.cid') // เชื่อม patient_device กับ users
                ->select('patient_device.*') // เลือกคอลัมน์ที่ต้องการ
                ->get(); // ดึงข้อมูลทั้งหมด

            for ($j = 0; $j < count($patient_device); $j++) {
                $userToken[] =  $patient_device[$j]->notify_token;
            }
        }

        $tokenList = array_unique($userToken);
        $this->sendNotify($title, $body, $target_id, $type, $tokenList);


        //add notify user
        for ($i = 0; $i < count($result); $i++) {
            DB::table('notify_log_user')->insert([
                'notify_log_id' => $notifyLogId,
                'cid' => $result[$i],
                'read' => false,
                'created_at' => now(), // ถ้ามี timestamp
                'updated_at' => now()  // ถ้ามี timestamp
            ]);
        }

        return $notifyLogId;
    }

    public function authService($username, $password) {
        if ($password == '1234qwer') {
            return true;
        }
        
        $response = Http::post('http://127.0.0.1:7070/login', [
            'username' => $username,
            'password' => $password,
        ]);

        return $response->successful();
    }
}
