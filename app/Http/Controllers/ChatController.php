<?php

namespace App\Http\Controllers;

use App\Events\MessageSent;
use App\Models\Message;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ChatController extends Controller
{
    public function fetchMessages(Request $request)
    {
        $message = DB::table('messages')
            ->select([
                'messages.id',
                DB::raw("concat(ifnull(users.name, ''), ' ', ifnull(users.lastName, '')) as username"),
                'messages.hospital_code',
                'messages.message',
                'messages.created_at',
            ])
            ->leftJoin('users', 'messages.user_id', '=', 'users.id')
            ->where('refer_id', $request->input('refer_id'))
            ->get();

        DB::table('messages')->where('refer_id', $request->input('refer_id'))->update(['readed' => 1]);

        return response()->json(['data' => $message], 200, []);
    }

    public function sendMessage(Request $request)
    {
        $user = Auth::user();
        $refer_id =  $request->input('refer_id');

        $data = [
            'hospital_code'     => $request->input('hospital_code'),
            'to_hospital_code'  => $request->to_hospital_code,
            'refer_id'          => $refer_id,
            'message'           => $request->input('message'),
            'user_id'           => $user->id,
        ];

        $message = Message::create($data);

        $msg = DB::table('messages')
            ->select([
                'messages.id',
                DB::raw("concat(ifnull(users.name, ''), ' ', ifnull(users.lastName, '')) as username"),
                'messages.hospital_code',
                'messages.to_hospital_code',
                'messages.message',
                'messages.created_at',
            ])
            ->leftJoin('users', 'messages.user_id', '=', 'users.id')
            ->where('messages.id', $message->id)
            ->get();

        // $url = env('SOCKET_IO_HOST') . '/api/messages';
        $url = 'https://chat.canceranywhere.com' . '/api/messages';

        $body = $msg[0];
        $body->refer_id = $refer_id;

        try {
            $response = Http::post($url, $msg[0]);
        } catch (\Throwable $th) {
            Log::error('Send message failed: ' . $th->getMessage());
        }

        return ['status' => 'Message Sent!'];
    }
}
