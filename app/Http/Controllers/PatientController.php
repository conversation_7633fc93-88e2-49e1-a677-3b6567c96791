<?php

namespace App\Http\Controllers;

use App\Http\Controllers\CancerController;
use App\Http\Controllers\WaitingTimeController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\CancerImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;

class PatientController extends Controller
{
    public function mor()
    {
        $morphology_list = DB::table('bd_mor')
            ->whereNotIn('key', [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18])
            ->pluck(DB::raw("concat(code, ' ', name) as name"), 'key')
            ->map(function ($name, $code) {
                return ['code' => $code, 'name' => $name];
            })->values();

        return response()->json([
            'morphology_list' =>  $morphology_list
        ], 200, []);
    }

    public function patientAllReference(Request $request)
    {
        $title_list = DB::table('bd_title')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $sex_list = DB::table('bd_sex')->where('status', 'Y')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $nationality_list = DB::table('bd_national')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $deathcause_list = DB::table('bd_deathcause')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $finance_support_list = DB::table('bd_finance_support')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $diagnosis_list = DB::table('bd_diagnosis')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $topo_list = DB::table('bd_topo')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $morphology_list = DB::table('bd_mor')->pluck(DB::raw("concat(code, ' ', name) as name"), 'key')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $behaviour_list = DB::table('bd_behaviour')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $grade_list = DB::table('bd_grade')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $t_list = DB::table('bd_t')->pluck('name', 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $n_list = DB::table('bd_n')->pluck('name', 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $m_list = DB::table('bd_m')->pluck('name', 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();

        $stage_list = DB::table('bd_stage')
            ->orderBy(DB::raw("concat(stage_group_id, ' ', name)")) // เพิ่มตรงนี้
            ->pluck(DB::raw("concat(stage_group_id, ' ', name) as name"), 'code')
            ->map(function ($name, $code) {
                return ['code' => $code, 'name' => $name];
            })
            ->values();

        $extension_list = DB::table('bd_extend')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $icd10_list = DB::table('bd_icd10')->pluck(DB::raw("ICD10_TEXT as name"), DB::raw('ICD10 as code'))->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();
        $treatment_list = DB::table('bd_treatment')->pluck(DB::raw("concat(code, ' ', name) as name"), 'code')->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();

        $icd9_list = DB::table('bd_icd9')->orderBy('CODE')->pluck(DB::raw("concat(code, ' ', TEXT) as name"), DB::raw('ID as code'))->map(function ($name, $code) {
            return ['code' => $code, 'name' => $name];
        })->values();

        return [
            'title_list'            => $title_list,
            'sex_list'              => $sex_list,
            'nationality_list'      => $nationality_list,
            'deathcause_list'       => $deathcause_list,
            'finance_support_list'  => $finance_support_list,
            'diagnosis_list'        => $diagnosis_list,
            'topo_list'             => $topo_list,
            'morphology_list'       => $morphology_list,
            'behaviour_list'        => $behaviour_list,
            'grade_list'            => $grade_list,
            't_list'                => $t_list,
            'n_list'                => $n_list,
            'm_list'                => $m_list,
            'stage_list'            => $stage_list,
            'extension_list'        => $extension_list,
            'icd10_list'            => $icd10_list,
            'treatment_list'        => $treatment_list,
            'icd9_list'             => $icd9_list
        ];
    }

    public function patientReferenceProvince(Request $request)
    {
        $health_region_id    = $request->get('health_region_id');

        $data  = DB::table('ref_provinces');

        if (isset($health_region_id)) {
            $data = $data->where('health_region_id', $health_region_id);
        }

        $data = $data->select(DB::raw('id as code'), DB::raw('province_name_th as name'))->orderBy('id')->get();

        return response()->json([
            'data' => $data
        ], 200, []);
    }

    public function patientReferenceDistricts(Request $request)
    {
        $province_id    = $request->get('province_id');
        $district_list  = DB::table('ref_districts')->where('province_id', $province_id)->pluck('district_name_th', 'id')->toArray();

        return [
            'district_list' => $district_list
        ];
    }

    public function patientReferenceSubDistricts(Request $request)
    {

        $district_id = $request->get('district_id');
        $sub_district_list = DB::table('ref_sub_districts_1')
            ->select('subdistrictCode as code', 'subdistrictNameTh as name', 'postalCode as postal_code')
            ->where('districtCode', $district_id)
            ->get();

        return [
            'sub_district_list' => $sub_district_list
        ];
    }

    public function patientNotification(Request $request)
    {
        $user = Auth::user();
        $notifi = [];

        $refer_0 = DB::table('data_refer')->where('to_hospital_code', $user->hosCode)->where('status', 0)->orderBy('created_at')->get();
        if ($refer_0 && sizeof($refer_0) > 0) {
            $notifi[] = [
                'notification_type_id'  => 1,
                'message'               => 'มีรายการรอรับ Refer ' . sizeof($refer_0) . ' รายการ',
                'created_at'            => $refer_0[0]->created_at,
                'url_name'              => '/refer-in',
                'count'                 => sizeof($refer_0)
            ];
        }

        $refer_1 = DB::table('data_refer')->where('from_hospital_code', $user->hosCode)->where('status', 1)->orderBy('created_at')->get();
        if ($refer_1 && sizeof($refer_1) > 0) {
            $notifi[] = [
                'notification_type_id'  => 1,
                'message'               => 'มีการขอข้อมูลการส่ง Refer เพิ่มเติม ' . sizeof($refer_1) . ' รายการ',
                'created_at'            => $refer_1[0]->created_at,
                'url_name'              => '/refer-out',
                'count'                 => sizeof($refer_1)
            ];
        }

        $message = DB::table('messages')->where('to_hospital_code', $user->hosCode)->where('readed', 0)->get();
        if ($message && sizeof($message) > 0) {
            $notifi[] = [
                'notification_type_id'  => 2,
                'message'               => 'มีข้อความที่ยังไม่อ่านจำนวน ' . sizeof($message) . ' ข้อความ',
                'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                'url_name'              => '#',
                'count'                 => sizeof($message)
            ];
        }

        return response()->json([
            'data' => $notifi
        ], 200, []);
    }

    public function patientReference(Request $request)
    {
        $user = Auth::user();

        if ($request->has('id')) {
            $patient = DB::table('data_patient')->where('id', $request->get('id'))->first();
        } else {
            $patient = null;
        }

        $hos                                = DB::table('bd_hospital')->where('code', $user->hosCode)->first();
        $user_province_id                   = substr($hos->area_code, 0, 2);

        $address_province_id                = $patient ? $patient->address_province_id : $user_province_id;
        $address_district_id                = $patient ? $patient->address_district_id : null;
        $address_sub_district_id            = $patient ? $patient->address_sub_district_id : null;
        $permanent_address_province_id      = $patient ? $patient->permanent_address_province_id : $user_province_id;
        $permanent_address_district_id      = $patient ? $patient->permanent_address_district_id : null;
        $permanent_address_sub_district_id  = $patient ? $patient->permanent_address_sub_district_id : null;

        // Fetch lists from database and convert them to arrays of objects
        $title_list = DB::table('bd_title')->select(DB::raw("code , name"))->get()->toArray();
        $deathcause_list = DB::table('bd_deathcause')->select(DB::raw("code , name"))->get()->toArray();
        $sex_list = DB::table('bd_sex')->select(DB::raw("code , name"))->get()->toArray();
        $status_list = DB::table('bd_status')->select(DB::raw("code , name"))->get()->toArray();
        $nation_list = DB::table('bd_national')->select(DB::raw("code , name"))->get()->toArray();
        $nationality_list = DB::table('bd_national')->select(DB::raw("code , name"))->get()->toArray();
        $religion_list = DB::table('bd_religion')->select(DB::raw("code , name"))->get()->toArray();
        $address_province_list = DB::table('ref_provinces')->select(DB::raw("id, province_name_th as name"))->orderBy('province_name_th')->get()->toArray();
        $address_district_list = DB::table('ref_districts')->where('province_id', $address_province_id)->select(DB::raw("id, district_name_th as name"))->orderBy('district_name_th')->get()->toArray();
        $address_sub_district_list = $address_district_id ? DB::table('ref_sub_districts')->where('district_id', $address_district_id)->select(DB::raw("id, sub_district_name_th as name"))->orderBy('sub_district_name_th')->get()->toArray() : [];
        $permanent_address_province_list = DB::table('ref_provinces')->select(DB::raw("id, province_name_th as name"))->orderBy('province_name_th')->get()->toArray();
        $permanent_address_district_list = DB::table('ref_districts')->where('province_id', $permanent_address_province_id)->select(DB::raw("id, district_name_th as name"))->orderBy('district_name_th')->get()->toArray();
        $permanent_address_sub_district_list = $permanent_address_district_id ? DB::table('ref_sub_districts')->where('district_id', $permanent_address_district_id)->select(DB::raw("id, sub_district_name_th as name"))->orderBy('sub_district_name_th')->get()->toArray() : [];

        // Return the data as array objects
        return [
            'address_province_id'               => $address_province_id,
            'address_district_id'               => $address_district_id,
            'address_sub_district_id'           => $address_sub_district_id,
            'permanent_address_province_id'     => $permanent_address_province_id,
            'permanent_address_district_id'     => $permanent_address_district_id,
            'permanent_address_sub_district_id' => $permanent_address_sub_district_id,
            'title_list'                        => $title_list,
            'deathcause_list'                   => $deathcause_list,
            'sex_list'                          => $sex_list,
            'status_list'                       => $status_list,
            'nation_list'                       => $nation_list,
            'nationality_list'                  => $nationality_list,
            'religion_list'                     => $religion_list,
            'address_province_list'             => $address_province_list,
            'address_district_list'             => $address_district_list,
            'address_sub_district_list'         => $address_sub_district_list,
            'permanent_address_province_list'   => $permanent_address_province_list,
            'permanent_address_district_list'   => $permanent_address_district_list,
            'permanent_address_sub_district_list' => $permanent_address_sub_district_list,
        ];
    }

    public function patientPatientImportOtherHospital(Request $request)
    {
        $user    = Auth::user();

        // Fetch the patient record using the provided id
        $patient = DB::table('data_patient')->find($request->get('patient_id'));

        // Check if the patient exists
        if (!$patient) {
            return response()->json([
                'data' => [
                    'status' => false,
                    'message' => 'ไม่พบข้อมูลผู้ป่วย'
                ]
            ], 422);
        }

        // Check for duplicate entries
        $check_dup = DB::table('data_patient')
            ->where('hospital_code', $user->hosCode)
            ->where(function ($query) use ($patient, $request) {
                $query->where('hn_no', $request->get('hn_no'))
                    ->orWhere('cid', $patient->cid);
            })
            ->first();

        // If a duplicate is found, return an error message
        if ($check_dup) {
            return response()->json([
                'data' => [
                    'status' => false,
                    'message' => 'ไม่สามารถนำเข้าข้อมูลได้ เนื่องจากซ้ำกับที่มีอยู่แล้ว'
                ]
            ], 422);
        }

        // Prepare the data for insertion
        $val = [
            'hospital_code'                     => $user->hosCode,
            'hn_no'                             => $request->get('hn_no'),
            'title_code'                        => $patient->title_code,
            'name'                              => $patient->name,
            'last_name'                         => $patient->last_name,
            'cid'                               => $patient->cid,
            'birth_date'                        => $patient->birth_date,
            'sex_code'                          => $patient->sex_code,
            'nationality_code'                  => $patient->nationality_code,
            'death_date'                        => $patient->death_date,
            'deathcause_code'                   => $patient->deathcause_code,
            'address_no'                        => $patient->address_no,
            'address_moo'                       => $patient->address_moo,
            'address_province_id'               => $patient->address_province_id,
            'address_district_id'               => $patient->address_district_id,
            'address_sub_district_id'           => $patient->address_sub_district_id,
            'area_code'                         => $patient->address_sub_district_id,
            'permanent_address_no'              => $patient->permanent_address_no,
            'permanent_address_moo'             => $patient->permanent_address_moo,
            'permanent_address_province_id'     => $patient->permanent_address_province_id,
            'permanent_address_district_id'     => $patient->permanent_address_district_id,
            'permanent_address_sub_district_id' => $patient->permanent_address_sub_district_id,
            'permanent_area_code'               => $patient->permanent_address_sub_district_id,
            'email'                             => $patient->email,
            'telephone_1'                       => $patient->telephone_1,
            'telephone_2'                       => $patient->telephone_2,
            'updated_at'                        => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_by'                        => $user->id,
            'created_at'                        => Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'                        => $user->id
        ];

        try {
            // Insert the patient record and get the inserted ID
            $id = DB::table('data_patient')->insertGetId($val);
            if ($id) {
                // Update state after successful insertion
                $this->updateState($user->hosCode, 'patients');
            }
        } catch (Exception $e) {
            // Return error message if there is an exception
            return response()->json([
                'data' => [
                    'status' => false,
                    'message' => $e->getMessage()
                ]
            ], 422);
        }

        // Return success message with the new patient's ID
        return response()->json([
            'data' => [
                'status' => true,
                'message' => 'บันทึกข้อมูลสำเร็จ.',
                'id' => $id
            ]
        ], 200);
    }


    public function patientPatientById(Request $request)
    {
        $patient = $this->patientPatientDetail($request->id);

        if (!$patient) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูล',
            ], 404);
        }

        return $patient;
    }

    public function patientPatientDetail($id)
    {
        $patient = DB::table('data_patient')
            ->select([
                'data_patient.id',
                DB::raw('bd_hospital.name as hospital_name'),
                'data_patient.hn_no',
                DB::raw('bd_title.name as title_name'),
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.cid',
                'data_patient.birth_date',
                'data_patient.sex_code',
                DB::raw('bd_sex.name as sex_name'),
                'data_patient.address_no',
                'data_patient.address_moo',
                'ref_sub_districts.sub_district_name_th',
                'ref_districts.district_name_th',
                'ref_provinces.province_name_th',
                'data_patient.address_zipcode',
                'data_patient.email',
                'data_patient.telephone_1',
                'data_patient.telephone_2',
                'data_patient.death_date',
                DB::raw('bd_deathcause.name as deathcause_name')
            ])
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('bd_deathcause', 'data_patient.deathcause_code', '=', 'bd_deathcause.code')
            ->leftJoin('ref_provinces', 'data_patient.address_province_id', '=', 'ref_provinces.id')
            ->leftJoin('ref_districts', 'data_patient.address_district_id', '=', 'ref_districts.id')
            ->leftJoin('ref_sub_districts', 'data_patient.address_sub_district_id', '=', 'ref_sub_districts.id')
            ->where('data_patient.id', $id)
            ->first();

        return $patient;
    }

    public function patientPatientByCid(Request $request)
    {
        // Get all the input data from the request
        $input = $request->all();

        // Check if 'hospital_code' and 'cid' exist in the input
        if (!isset($input['hospital_code']) || !isset($input['cid'])) {
            return [
                'state' => false,
                'message' => 'Missing hospital_code or cid in the request',
                'patient' => null
            ];
        }

        // Query the patient by hospital_code and cid
        $patient = DB::table('data_patient')
            ->select(['id'])
            ->where('hospital_code', $input['hospital_code'])
            ->where('cid', $input['cid'])
            ->first();

        // Return the appropriate response based on whether the patient is found
        if ($patient) {
            return [
                'state' => true,
                'patient' => $patient
            ];
        } else {
            return [
                'state' => false,
                'patient' => null
            ];
        }
    }

    public function patientHospitals(Request $request)
    {
        $province_id = $request->query('province_id');

        $hospitals_list = DB::table('bd_hospital')
            ->select('code', DB::raw("concat(code, ' ', name) as name"))
            ->where('dropdown', '=', '1');

        if (isset($province_id)) {
            $hospitals_list = $hospitals_list->where('province_id', $province_id);
        }

        $hospitals_list = $hospitals_list->get();

        return response()->json([
            'status' => true,
            'data' => $hospitals_list
        ], 200);
    }

    public function patientHospitalsList(Request $request)
    {
        $input = $request->all();

        $query = DB::table('bd_hospital')
            ->where(function ($query) use ($input) {
                if (isset($input['hospital_code'])) {
                    $query->where(DB::raw("concat(code, name)"), 'LIKE', '%' . str_replace(' ', '', $input['hospital_code']) . '%');
                }
            })
            ->select('code', DB::raw("concat(code, ' ', name) as name"));

        if (isset($input['health_region_id'])) {
            $query->where('health_region_id', $input['health_region_id']);
        }

        // ถ้ามี length และเป็นตัวเลขมากกว่า 0 ให้ limit
        if (isset($input['length']) && is_numeric($input['length']) && $input['length'] > 0) {
            $query->limit(intval($input['length']));
        }

        $hospitals_list = $query->get()->toArray();

        return [
            'hospitals_list' => $hospitals_list,
        ];
    }

    public function patientPatientSearchbyName(Request $request)
    {
        $input = $request->all();
        $limit = isset($input['limit']) ? (int)$input['limit'] : 20;

        if (isset($input['name'])) {
            // กรณีค้นหาด้วย name
            $hospitals_list = DB::table('bd_hospital')
                ->where(DB::raw("REPLACE(name, ' ', '')"), 'LIKE', '%' . str_replace(' ', '', $input['name']) . '%')
                ->select('code', DB::raw("concat(code, ' ', name) as name"))
                ->limit($limit) // ใช้ limit แทน paginate
                ->get()
                ->toArray();
        } else {
            // กรณีไม่มีการค้นหา name (ใช้ much_keyin = 1)
            $hospitals_list = DB::table('bd_hospital')
                ->where('much_keyin', '=', '1')
                ->select('code', DB::raw("concat(code, ' ', name) as name"))
                ->get() // ไม่ใช้ paginate
                ->toArray();
        }

        return [
            'hospitals_list' => $hospitals_list,
        ];
    }

    public function patientPatientSearch(Request $request)
    {
        // dd($request->all());
        $input = $request->all();
        $user = Auth::user();
        $patient = DB::table('data_patient')
            ->select([
                'data_patient.id',
                DB::raw('bd_hospital.name as hospital_name'),
                'data_patient.hn_no',
                'bd_title.code as title_code',
                DB::raw("concat(bd_title.code, ' ', bd_title.name) as title_name"),
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.cid',
                'data_patient.birth_date',
                'bd_sex.code as sex_code',
                DB::raw("concat(bd_title.code, ' ', bd_sex.name) as sex_name"),
                'data_patient.address_no',
                'data_patient.address_moo',
                'data_patient.address_sub_district_id',
                'ref_sub_districts.sub_district_name_th',
                'data_patient.address_district_id',
                'ref_districts.district_name_th',
                'data_patient.address_province_id',
                'ref_provinces.province_name_th',
                'data_patient.address_zipcode',
                'data_patient.email',
                'data_patient.telephone_1',
                'data_patient.telephone_2'
            ])
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('ref_provinces', 'data_patient.address_province_id', '=', 'ref_provinces.id')
            ->leftJoin('ref_districts', 'data_patient.address_district_id', '=', 'ref_districts.id')
            ->leftJoin('ref_sub_districts', 'data_patient.address_sub_district_id', '=', 'ref_sub_districts.id')
            ->where('data_patient.cid', $input['cid'])
            // ->where('data_patient.hospital_code', '<>', $user->hosCode)
            ->first();

        $files = null;
        $cancers = null;
        $status = false;

        if ($patient) {
            $status = true;
            $ids = DB::table('data_patient')
                ->where('data_patient.cid', $input['cid'])
                ->pluck('id');

            $cancers_summary = DB::table('data_cancer_summary')
                ->whereIn('patient_id', $ids)
                ->orderBy('entrance_date', 'desc')
                ->get();

            if (sizeof($cancers_summary) > 0) {
                $cancers = $cancers_summary;

                foreach ($cancers as $idx => $cancer) {
                    $hos = DB::table('bd_hospital')
                        ->where('code', $cancer->hospital_code)
                        ->first();

                    $cancers[$idx]->hospital_name = $hos ? $hos->name : '';

                    $treatments = DB::table('data_cancer_summary_treatment')
                        ->where('patient_id', $patient->id)
                        ->where('icd10_code', $cancer->icd10_code)
                        ->orderBy('treatment_date', 'asc')
                        ->get();

                    $cancers[$idx]->treatments = [];
                    foreach ($treatments as $treat) {
                        $cancers[$idx]->treatments[] = $treat;
                    }

                    $files = DB::table('data_cancer_summary_file')
                        ->where('patient_id', $patient->id)
                        // ->where('icd10_code', $cancer->icd10_code)
                        ->get();
                    $cancers[$idx]->files = [];
                    foreach ($files as $file) {
                        $cancers[$idx]->files[] = $file;
                    }
                }

                // $files = DB::table('data_file')
                //     ->where('patient_id', $patient->id)
                //     ->get();
            } else {
                $cancers = DB::table('data_cancer')
                    ->whereIn('patient_id', $ids)
                    ->orderBy('entrance_date', 'desc')
                    ->get();

                foreach ($cancers as $idx => $cancer) {
                    $hos = DB::table('bd_hospital')
                        ->where('code', $cancer->hospital_code)
                        ->first();
                    $cancers[$idx]->hospital_name = $hos ? $hos->name : '';

                    $treatments = DB::table('data_treatment')
                        ->where('cancer_id', $cancer->id)
                        ->orderBy('treatment_date', 'asc')
                        ->get();
                    $cancers[$idx]->treatments = [];
                    foreach ($treatments as $treat) {
                        $cancers[$idx]->treatments[] = $treat;
                    }

                    $files = DB::table('data_file')
                        ->where('cancer_id', $cancer->id)
                        ->get();
                    $cancers[$idx]->files = [];
                    foreach ($files as $file) {
                        $cancers[$idx]->files[] = $file;
                    }
                }

                // $files = DB::table('data_file')
                //     ->where('patient_id', $patient->id)
                //     ->get();
            }
        }

        if ($cancers != null) {
            $grouped = $cancers->groupBy('hospital_name');

            // Convert to a collection
            $cancers = $grouped->map(function ($cancers, $key) {
                return [
                    'name' => $key,
                    'cancers' => collect($cancers)->map(fn($cancer) => $cancer)->all()
                ];
            })->values(); // Reset the keys
        }

        $message = 'ไม่พบข้อมูล';

        return [
            'status'        => $status,
            'message'       => $message,
            'curr_patient'  => $patient,
            'curr_cancers'  => $cancers,
            'curr_files'    => $files,
        ];
    }

    public function patientPatientTable(Request $request)
    {
        $user    = Auth::user();
        $input = $request->all();
        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search', "");

        $searchable = [
            'data_patient.hn_no',
            'data_patient.cid',
            // DB::raw("GROUP_CONCAT(DISTINCT data_cancer.topo_code SEPARATOR ', ') AS 'topo_text'"),
            DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name)") // Add full_name to the searchable array
        ];

        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $dataset = DB::table('data_patient')->select([
            'data_patient.id',
            'data_patient.hn_no',
            DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name) as full_name"),
            'bd_sex.name',
            'data_patient.cid',
            DB::raw('null as topo_text'),
            'data_patient.updated_at',
            DB::raw('null as action'),
        ])
            ->join('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->join('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            // ->join('data_cancer', 'data_cancer.patient_id', '=', 'data_patient.id')
            ->where('data_patient.hospital_code', $user->hosCode)
            ->where(function ($query) use ($input, $searchable, $search) {
                if (isset($search['value']) && !empty($search['value'])) {
                    
                    foreach ($searchable as $s) {
                        if ($s === 'data_patient.cid') {
                            $searchValue = str_replace('-', '', $search['value']); // ลบ - ออกจากค่าที่ใช้ค้นหา
                            $query->orWhere(DB::raw("REPLACE(data_patient.cid, '-', '')"), 'LIKE', '%' . $searchValue . '%');
                        } else {
                            $query->orWhere($s, 'LIKE', '%' . $search['value'] . '%');
                        }
                    }
                }
            })
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->groupBy('data_patient.id')
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);

        return $dataset;
    }

    public function patientIcd10(Request $request)
    {
        $suth_url       = 'https://caw.canceranywhere.com/convert_icdo';
        $auth_username  = 'tcb_2022';
        $auth_password  = 'tcb2022HAusdf7ew7YHyqw76jhjqwe';

        $params = $request->only("sex", "birth_date", "diag_date", "age", "topography", "morphology", "behaviour");

        $response = Http::withoutVerifying()
            ->withBasicAuth($auth_username, $auth_password)
            ->withBody(json_encode($params), 'application/json')
            ->post($suth_url);

        $body = $response->json();

        return $body;
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        $input = $request->all();

        DB::beginTransaction();

        try {
            $val_patient = $this->getValPatient($input);

            $val_patient['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
            $val_patient['updated_by'] = $user->id;
            $val_patient['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
            $val_patient['created_by'] = $user->id;
            $patient_id = DB::table('data_patient')->insertGetId($val_patient);

            if ($patient_id) {
                $this->updateState($val_patient['hospital_code'], 'patients');
            }

            if ($input['cancer_add']) {
                if (isset($input['morphology_text']) && $input['morphology_text'] != '') {
                    $mor = DB::table('bd_mor')->where(DB::raw("concat(code, ' ', name)"), $input['morphology_text'])->first();
                    if ($mor) {
                        $mor_key = $mor->key;
                    } else {
                        return response()->json([
                            'status' => false,
                            'message' => 'Morphology ไม่ถูกต้อง กรุณาเลือกจากตัวเลือกเท่านั้น'
                        ], 422);
                    }
                } else {
                    $mor_key = null;
                }

                $val_cancer                 = CancerController::getValCancer($patient_id, $input, $mor_key);
                $val_cancer['source_id']    = $user->roleCancer == 'Y' ? 1 : 2;
                $val_cancer['updated_at']   = Carbon::now()->format('Y-m-d H:i:s');
                $val_cancer['updated_by']   = $user->id;
                $val_cancer['created_at']   = Carbon::now()->format('Y-m-d H:i:s');
                $val_cancer['created_by']   = $user->id;

                $cancer_id                  = DB::table('data_cancer')->insertGetId($val_cancer);

                if ($cancer_id) {
                    $this->updateState($val_cancer['hospital_code'], 'cancers');
                }

                // Previous Treatments
                $data_treatment = [];
                if (isset($input['prev_treatments']) && sizeof($input['prev_treatments']) > 0) {
                    foreach ($input['prev_treatments'] as $val_treatment) {
                        if (empty($val_treatment['treatment_code'])) {
                            continue;
                        }

                        $val_treat = [
                            'patient_id'            => $patient_id,
                            'cancer_id'             => $cancer_id,
                            'treatment_type_id'     => $this->getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                            'treatment_code'        => $val_treatment['treatment_code'],
                            'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                            'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                            'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                            'none_protocol'         => $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                            'none_protocol_note'    => $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                            'note'                  => $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                            'updated_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                            'updated_by'            => $user->id,
                            'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                            'created_by'            => $user->id,
                            'is_prev'               => 1,
                            'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                        ];
                        DB::table('data_treatment')->insert($val_treat);
                        $data_treatment[] = $val_treat;
                    }
                }

                // Treatments
                if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {
                    foreach ($input['curr_treatments'] as $val_treatment) {
                        $val_treat = [
                            'patient_id'            => $patient_id,
                            'cancer_id'             => $cancer_id,
                            'treatment_type_id'     => $this->getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
                            'treatment_code'        => $val_treatment['treatment_code'],
                            'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
                            'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
                            'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
                            'none_protocol'         => $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
                            'none_protocol_note'    => $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
                            'note'                  => $val_treatment['note'] != '' ? $val_treatment['note'] : null,
                            'updated_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                            'updated_by'            => $user->id,
                            'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                            'created_by'            => $user->id,
                            'is_prev'               => null,
                            'icd9_ids'              => isset($val_treatment['icd9_ids']) ? json_encode($val_treatment['icd9_ids']) : null,
                        ];
                        DB::table('data_treatment')->insert($val_treat);
                        $data_treatment[] = $val_treat;
                    }
                }

                // Files
                $data_file = [];
                if (isset($input['curr_files']) && sizeof($input['curr_files']) > 0) {
                    foreach ($input['curr_files'] as $idx => $val_file) {
                        if ($request->hasFile('curr_files.' . $idx . '.file')) {
                            $file           = $request->file('curr_files.' . $idx . '.file');
                            $file_size      = $file->getSize();
                            $store_path     = "upload/files/" . Carbon::now()->format('Ymd');
                            $name           = md5(uniqid(rand(), true)) . str_replace(' ', '-', $file->getClientOriginalName());
                            $file->move(public_path('/' . $store_path), $name);

                            $val_file['file_path']      = $store_path . '/' . $name;
                            $val_file['file_name']      = $file->getClientOriginalName();
                            $val_file['file_size']      = $file_size;
                            $val_file['patient_id']     = $patient_id;
                            $val_file['cancer_id']      = $cancer_id;
                            $val_file['updated_at']     = Carbon::now()->format('Y-m-d H:i:s');
                            $val_file['updated_by']     = $user->id;
                            $val_file['created_at']     = Carbon::now()->format('Y-m-d H:i:s');
                            $val_file['created_by']     = $user->id;

                            unset($val_file['file']);
                            DB::table('data_file')->insert($val_file);
                            $data_file[] = $val_file;
                        }
                    }
                }

                // ถ้า User เป็น "ทะเบียนมะเร็ง" ให้ทำการสร้างใบสรุปทันที
                if ($user->roleCancer == 'Y') {
                    // unset($val_cancer['source_id']);
                    $val_cancer['source_id'] = 5;
                    $val_cancer['first_entrance_cancer_id'] = $cancer_id;

                    $val_cancer['diagnosis_date'] = CancerController::determineDiagnosisDate($input);

                    $cancer_summary_id = DB::table('data_cancer_summary')->insertGetId($val_cancer);

                    // Treatments
                    if (sizeof($data_treatment) > 0) {
                        foreach ($data_treatment as $val_treatment) {
                            $val_treatment['cancer_id'] = $cancer_summary_id;
                            $val_treatment['icd10_code'] = $val_cancer['icd10_code'];
                            DB::table('data_cancer_summary_treatment')->insert($val_treatment);
                        }
                    }

                    // Files
                    if (sizeof($data_file) > 0) {
                        foreach ($data_file as $idx => $val_file) {
                            $val_file['cancer_id'] = $cancer_summary_id;
                            // $val_file['icd10_code'] = $val_cancer['icd10_code'];
                            DB::table('data_cancer_summary_file')->insert($val_file);
                        }
                    }

                    // กำหนดว่า Cancer นี้ผ่านการอนุมัติแล้ว
                    DB::table('data_cancer')->where('id', $cancer_id)->update(['user_cancer' => 1]);

                    // กำหนดว่า Patient นี้ผ่านการอนุมัติแล้ว
                    DB::table('data_patient')->where('id', $patient_id)->update(['checked_api' => 1]);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            Log::error($e);

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        return response()->json([
            'status' => true,
            'message' => 'บันทึกข้อมูลสำเร็จ.',
            'id' => $patient_id
        ], 200);
    }

    public function edit($id)
    {
        $patient = DB::table('data_patient')->where('data_patient.id', $id)->first();

        return response()->json($patient, 200, []);
    }

    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $input = $request->all();

        $val_patient = $this->getValPatient($input);

        $val_patient['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
        $val_patient['updated_by'] = $user->id;

        try {
            $val_patient['birth_date'] = getDateTimeTHtoEN($input['birth_date']);

            if (isset($input['death_date']) && $input['death_date'] != '') {
                $val_patient['death_date'] = getDateTimeTHtoEN($input['death_date']);
            }

            DB::table('data_patient')->where('id', $id)->update($val_patient);
        } catch (Exception $e) {

            Log::error($e->getMessage());

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        return response()->json([
            'status' => true,
            'message' => 'บันทึกข้อมูลสำเร็จ.',
            'id' => $id
        ], 200);
    }

    public function importCancer(Request $request)
    {
        $user = Auth::user();

        $rows = Excel::toArray(new CancerImport, $request->file('file'));

        $errors = [];
        foreach ($rows[0] as $key => $row) {
            $input = $this->mapDataForImport($row);
            if (isset($input['morphology_code']) && $input['morphology_code'] != '') {
                $mor = DB::table('bd_mor')->where('code', $input['morphology_code'])->first();
                if ($mor) {
                    $mor_key = $mor->key;
                } else {
                    $message = '[ROW ' . $key + 2 . ']' . ' Morphology ' . $input['morphology_code'] . ' ไม่ถูกต้อง กรุณาเลือกจากตัวเลือกเท่านั้น';

                    $errors[] = $message;
                    continue;
                }
            } else {
                $mor_key = null;
            }

            // $val_patient = $this->getValPatient($input);

            // $val_patient['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
            // $val_patient['updated_by'] = $user->id;
            // $val_patient['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
            // $val_patient['created_by'] = $user->id;
            // $patient_id = DB::table('data_patient')->insertGetId($val_patient);

            // if ($patient_id) {
            //     $this->updateState($val_patient['hospital_code'], 'patients');
            // }


            // $val_cancer                 = CancerController::getValCancer($patient_id, $input, $mor_key);
            // $val_cancer['source_id']    = $user->roleCancer == 'Y' ? 1 : 2;
            // $val_cancer['updated_at']   = Carbon::now()->format('Y-m-d H:i:s');
            // $val_cancer['updated_by']   = $user->id;
            // $val_cancer['created_at']   = Carbon::now()->format('Y-m-d H:i:s');
            // $val_cancer['created_by']   = $user->id;

            // $cancer_id                  = DB::table('data_cancer')->insertGetId($val_cancer);

            // if ($cancer_id) {
            //     $this->updateState($val_cancer['hospital_code'], 'cancers');
            // }

            // Treatments
            // $data_treatment = [];
            // if (isset($input['curr_treatments']) && sizeof($input['curr_treatments']) > 0) {
            //     foreach ($input['curr_treatments'] as $val_treatment) {
            //         $val_treat = [
            //             'patient_id'            => $patient_id,
            //             'cancer_id'             => $cancer_id,
            //             'treatment_type_id'     => $this->getCodeFromText($val_treatment, 'treatment_code', 'bd_treatment'),
            //             'treatment_code'        => $val_treatment['treatment_code'],
            //             'treatment_date'        => $val_treatment['treatment_date'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date']) : null,
            //             'treatment_date_end'    => $val_treatment['treatment_date_end'] != '' ? getDateTimeTHtoEN($val_treatment['treatment_date_end']) : null,
            //             'consult_date'          => $val_treatment['consult_date'] != '' ? getDateTimeTHtoEN($val_treatment['consult_date']) : null,
            //             'none_protocol'         => $val_treatment['none_protocol'] != '' ? $val_treatment['none_protocol'] : null,
            //             'none_protocol_note'    => $val_treatment['none_protocol_note'] != '' ? $val_treatment['none_protocol_note'] : null,
            //             'note'                  => $val_treatment['note'] != '' ? $val_treatment['note'] : null,
            //             'updated_at'            => Carbon::now()->format('Y-m-d H:i:s'),
            //             'updated_by'            => $user->id,
            //             'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
            //             'created_by'            => $user->id,
            //         ];
            //         DB::table('data_treatment')->insert($val_treat);
            //         $data_treatment[] = $val_treat;
            //     }
            // }

            // ถ้า User เป็น "ทะเบียนมะเร็ง" ให้ทำการสร้างใบสรุปทันที
            // if ($user->roleCancer == 'Y') {
            //     // unset($val_cancer['source_id']);
            //     $val_cancer['source_id'] = 5;
            //     $cancer_summary_id = DB::table('data_cancer_summary')->insertGetId($val_cancer);

            //     // Treatments
            //     if (sizeof($data_treatment) > 0) {
            //         foreach ($data_treatment as $val_treatment) {
            //             $val_treatment['cancer_id'] = $cancer_summary_id;
            //             DB::table('data_cancer_summary_treatment')->insert($val_treatment);
            //         }
            //     }

            //     // Files
            //     if (sizeof($data_file) > 0) {
            //         foreach ($data_file as $idx => $val_file) {
            //             $val_file['cancer_id'] = $cancer_summary_id;
            //             DB::table('data_cancer_summary_file')->insert($val_file);
            //         }
            //     }
            // }
        }

        if (sizeof($errors) > 0) {
            return response()->json([
                'status' => false,
                'message' => 'นำเข้าไฟล์ไม่สำเร็จ',
                'errors' => $errors,
            ], 422);
        }

        foreach ($rows[0] as $key => $row) {
            $input = $this->mapDataForImport($row);

            $val_patient = $this->getValPatient($input);

            $val_patient['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
            $val_patient['updated_by'] = $user->id;
            $val_patient['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
            $val_patient['created_by'] = $user->id;
            $patient_id = DB::table('data_patient')->insertGetId($val_patient);
        }

        return response()->json([
            'status' => true,
            'message' => 'บันทึกข้อมูลสำเร็จ.',
        ], 200);
    }

    private function mapDataForImport($input)
    {
        $val = [
            'hospital_code'                     => $input['hosp'],
            'hn_no'                             => $input['hn'],
            'title_code'                        => $input['title'],
            'name'                              => $input['name'],
            'last_name'                         => $input['lastn'],
            'cid'                               => str_replace("-", "", $input['id']),
            'birth_date'                        => getDateTimeTHtoEN($input['birthd']),
            'sex_code'                          => $input['sex'],
            'nationality_code'                  => $input['nat'],
            'death_date'                        => isset($input['diedate']) && $input['diedate'] != '' ? getDateTimeTHtoEN($input['diedate']) : null,
            'deathcause_code'                   => isset($input['cause']) ? $input['cause'] : null,
            // 'address_no'                        => isset($input['address_no']) ? $input['address_no'] : null,
            // 'address_moo'                       => isset($input['address_moo']) ? $input['address_moo'] : null,
            // 'address_province_id'               => isset($input['address_province_id']) ? $input['address_province_id'] : null,
            // 'address_district_id'               => isset($input['address_district_id']) ? $input['address_district_id'] : null,
            // 'address_sub_district_id'           => isset($input['address_sub_district_id']) ? $input['address_sub_district_id'] : null,
            // 'address_zipcode'                   => isset($input['address_zipcode']) ? $input['address_zipcode'] : null,
            'area_code'                         => isset($input['addcode']) ? $input['addcode'] : null,
            // 'permanent_address_no'              => isset($input['permanent_address_no']) ? $input['permanent_address_no'] : null,
            // 'permanent_address_moo'             => isset($input['permanent_address_moo']) ? $input['permanent_address_moo'] : null,
            // 'permanent_address_province_id'     => isset($input['permanent_address_province_id']) ? $input['permanent_address_province_id'] : null,
            // 'permanent_address_district_id'     => isset($input['permanent_address_district_id']) ? $input['permanent_address_district_id'] : null,
            // 'permanent_address_sub_district_id' => isset($input['permanent_address_sub_district_id']) ? $input['permanent_address_sub_district_id'] : null,
            // 'permanent_address_zipcode'         => isset($input['permanent_address_zipcode']) ? $input['permanent_address_zipcode'] : null,
            'permanent_area_code'               => isset($input['addcode2']) ? $input['addcode2'] : null,
            'email'                             => isset($input['email']) ? $input['email'] : null,
            'telephone_1'                       => isset($input['tel']) ? $input['tel'] : null,
            'telephone_2'                       => isset($input['tel2']) ? $input['tel2'] : null,
            // 'entrance_date'                     => $input['entrance_date'] != '' ? getDateTimeTHtoEN($input['entrance_date']) : null,
            // 'finance_support_code'              => static::getCodeFromText($input, 'finance_support_text', 'bd_finance_support'),        // isset($input['finance_support_text']) && trim($input['finance_support_text']) != '' ? substr($input['finance_support_text'], 0, strpos($input['finance_support_text'], ' ')) : null,
            // 'finance_support_text'              => isset($input['finance_support_text']) ? $input['finance_support_text'] : null,
            // 'diagnosis_date'                    => $input['diagnosis_date'] != '' ? getDateTimeTHtoEN($input['diagnosis_date']) : null,
            // 'diagnosis_code'                    => isset($input['diagnosis_text']) ? substr($input['diagnosis_text'], 0, 1) : null,
            // 'diagnosis_text'                    => isset($input['diagnosis_text']) ? $input['diagnosis_text'] : null,
            // 'diagnosis_out'                     => isset($input['diagnosis_out']) ? $input['diagnosis_out'] : 0,
            // 'excision_in_cut_date'              => isset($input['excision_in_cut_date']) && $input['excision_in_cut_date'] != '' ? getDateTimeTHtoEN($input['excision_in_cut_date']) : null,
            // 'excision_in_read_date'             => isset($input['excision_in_read_date']) && $input['excision_in_read_date'] != '' ? getDateTimeTHtoEN($input['excision_in_read_date']) : null,
            // 'topo_id'                           => $topo_id,
            // 'topo_code'                         => $topo_code,
            // 'topo_text'                         => isset($input['topo_text']) ? $input['topo_text'] : null,
            // 'recurrent'                         => isset($input['recurrent']) ? $input['recurrent'] : null,
            // 'recurrent_date'                    => isset($input['recurrent_date']) && $input['recurrent_date'] != '' ? getDateTimeTHtoEN($input['recurrent_date']) : null,
            'morphology_code'                   => $input['mor'],
            // 'morphology_text'                   => isset($input['morphology_text']) ? $input['morphology_text'] : null,
            // 'behaviour_code'                    => static::getCodeFromText($input, 'behaviour_text', 'bd_behaviour'),
            // 'behaviour_text'                    => isset($input['behaviour_text']) ? $input['behaviour_text'] : null,
            // 'grade_code'                        => static::getCodeFromText($input, 'grade_text', 'bd_grade'),  // isset($input['grade_text']) ? substr($input['grade_text'], 0, strpos($input['grade_text'], ' ')) : null,
            // 'grade_text'                        => isset($input['grade_text']) ? $input['grade_text'] : null,
            // 'm_code'                            => isset($input['m_code']) ? intval($input['m_code']) : null,
            // 'n_code'                            => isset($input['n_code']) ? intval($input['n_code']) : null,
            // 't_code'                            => isset($input['t_code']) ? intval($input['t_code']) : null,
            // 'tnm_date'                          => isset($input['tnm_date']) && $input['tnm_date'] != '' ? getDateTimeTHtoEN($input['tnm_date']) : null,
            // 'stage_code'                        => isset($input['stage_code']) ? intval($input['stage_code']) : null,
            // 'extension_code'                    => isset($input['extension_code']) ? intval($input['extension_code']) : null,
            // 'icd10_code'                        => $icd10_code,
            // 'icd10_text'                        => $icd10_text,
            // 'met_1'                             => isset($input['met_1']) ? $input['met_1'] : 0,
            // 'met_1_date'                        => isset($input['met_1_date']) && $input['met_1_date'] != '' ? getDateTimeTHtoEN($input['met_1_date']) : null,
            // 'met_2'                             => isset($input['met_2']) ? $input['met_2'] : 0,
            // 'met_2_date'                        => isset($input['met_2_date']) && $input['met_2_date'] != '' ? getDateTimeTHtoEN($input['met_2_date']) : null,
            // 'met_3'                             => isset($input['met_3']) ? $input['met_3'] : 0,
            // 'met_3_date'                        => isset($input['met_3_date']) && $input['met_3_date'] != '' ? getDateTimeTHtoEN($input['met_3_date']) : null,
            // 'met_4'                             => isset($input['met_4']) ? $input['met_4'] : 0,
            // 'met_4_date'                        => isset($input['met_4_date']) && $input['met_4_date'] != '' ? getDateTimeTHtoEN($input['met_4_date']) : null,
            // 'met_5'                             => isset($input['met_5']) ? $input['met_5'] : 0,
            // 'met_5_date'                        => isset($input['met_5_date']) && $input['met_5_date'] != '' ? getDateTimeTHtoEN($input['met_5_date']) : null,
            // 'met_6'                             => isset($input['met_6']) ? $input['met_6'] : 0,
            // 'met_6_date'                        => isset($input['met_6_date']) && $input['met_6_date'] != '' ? getDateTimeTHtoEN($input['met_6_date']) : null,
            // 'met_7'                             => isset($input['met_7']) ? $input['met_7'] : 0,
            // 'met_7_date'                        => isset($input['met_7_date']) && $input['met_7_date'] != '' ? getDateTimeTHtoEN($input['met_7_date']) : null,
            // 'met_7_other'                       => isset($input['met_7_other']) ? $input['met_7_other'] : null,
            // 'clinical_summary'                  => isset($input['clinical_summary']) ? $input['clinical_summary'] : null,
            // 'created_by'                        => $user->id,
            // 'created_at'                        => Carbon::now()->format('Y-m-d H:i:s'),
            // 'user_cancer'                       => $user->roleCancer == 'Y' ? 1 : 0
        ];

        return $val;
    }

    public static function getPatientDetail($id)
    {
        $patient = DB::table('data_patient')
            ->select([
                'data_patient.id',
                DB::raw('bd_hospital.name as hospital_name'),
                'data_patient.hn_no',
                DB::raw('bd_title.name as title_name'),
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.cid',
                'data_patient.birth_date',
                'data_patient.sex_code',
                DB::raw('bd_sex.name as sex_name'),
                'data_patient.address_no',
                'data_patient.address_moo',
                'ref_sub_districts.sub_district_name_th',
                'ref_districts.district_name_th',
                'ref_provinces.province_name_th',
                'data_patient.address_zipcode',
                'data_patient.email',
                'data_patient.telephone_1',
                'data_patient.telephone_2',
                'data_patient.death_date',
                DB::raw('bd_deathcause.name as deathcause_name')
            ])
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('bd_deathcause', 'data_patient.deathcause_code', '=', 'bd_deathcause.code')
            ->leftJoin('ref_provinces', 'data_patient.address_province_id', '=', 'ref_provinces.id')
            ->leftJoin('ref_districts', 'data_patient.address_district_id', '=', 'ref_districts.id')
            ->leftJoin('ref_sub_districts', 'data_patient.address_sub_district_id', '=', 'ref_sub_districts.id')
            ->where('data_patient.id', $id)
            ->first();

        return $patient;
    }

    public function getValPatientData($input)
    {
        $val = [
            'hospital_code'                     => $input['hospital_code'],
            'hn_no'                             => $input['hn_no'],
            'title_code'                        => $input['title_code'],
            'name'                              => $input['name'],
            'last_name'                         => $input['last_name'],
            'cid'                               => $input['cid'],
            'birth_date'                        => getDateTimeTHtoEN($input['birth_date']),
            'sex_code'                          => $input['sex_code'],
        ];

        return $val;
    }

    private function getValPatient($input)
    {
        $val = [
            'hospital_code'                     => $input['hospital_code'],
            'hn_no'                             => $input['hn_no'],
            'title_code'                        => $input['title_code'],
            'name'                              => $input['name'],
            'last_name'                         => $input['last_name'],
            'cid'                               => $input['cid'],
            'birth_date'                        => getDateTimeTHtoEN($input['birth_date']),
            'sex_code'                          => $input['sex_code'],
            'nationality_code'                  => $input['nationality_code'],
            'death_date'                        => isset($input['death_date']) && $input['death_date'] != '' ? getDateTimeTHtoEN($input['death_date']) : null,
            'deathcause_code'                   => isset($input['deathcause_code']) ? $input['deathcause_code'] : null,
            'address_no'                        => isset($input['address_no']) ? $input['address_no'] : null,
            'address_moo'                       => isset($input['address_moo']) ? $input['address_moo'] : null,
            'address_province_id'               => isset($input['address_province_id']) ? $input['address_province_id'] : null,
            'address_district_id'               => isset($input['address_district_id']) ? $input['address_district_id'] : null,
            'address_sub_district_id'           => isset($input['address_sub_district_id']) ? $input['address_sub_district_id'] : null,
            'address_zipcode'                   => isset($input['address_zipcode']) ? $input['address_zipcode'] : null,
            'area_code'                         => isset($input['address_sub_district_id']) ? $input['address_sub_district_id'] : null,
            'permanent_address_no'              => isset($input['permanent_address_no']) ? $input['permanent_address_no'] : null,
            'permanent_address_moo'             => isset($input['permanent_address_moo']) ? $input['permanent_address_moo'] : null,
            'permanent_address_province_id'     => isset($input['permanent_address_province_id']) ? $input['permanent_address_province_id'] : null,
            'permanent_address_district_id'     => isset($input['permanent_address_district_id']) ? $input['permanent_address_district_id'] : null,
            'permanent_address_sub_district_id' => isset($input['permanent_address_sub_district_id']) ? $input['permanent_address_sub_district_id'] : null,
            'permanent_address_zipcode'         => isset($input['permanent_address_zipcode']) ? $input['permanent_address_zipcode'] : null,
            'permanent_area_code'               => isset($input['permanent_address_sub_district_id']) ? $input['permanent_address_sub_district_id'] : null,
            'email'                             => isset($input['email']) ? $input['email'] : null,
            'telephone_1'                       => isset($input['telephone_1']) ? $input['telephone_1'] : null,
            'telephone_2'                       => isset($input['telephone_2']) ? $input['telephone_2'] : null,
        ];

        return $val;
    }

    private function updateState($hospital_code, $field_name)
    {
        $hospital = DB::table('bd_hospital')->where('code', $hospital_code)->first();
        if ($hospital) {
            DB::table('state_patient')->updateOrInsert(
                [
                    'curr_date'         => Carbon::now()->format('Y-m-d'),
                    'hospital_code'     => $hospital_code
                ],
                [
                    'curr_date'         => Carbon::now()->format('Y-m-d'),
                    'health_region_id'  => $hospital->health_region_id,
                    'province_id'       => $hospital->province_id,
                    'district_id'       => $hospital->district_id,
                    'sub_district_id'   => $hospital->sub_district_id,
                    'hospital_code'     => $hospital_code,
                    $field_name         => DB::raw('ifnull(' . $field_name . ', 0) + 1')
                ]
            );
        }
    }

    private function getCodeFromText($input, $field_name, $table_name)
    {
        if (isset($input[$field_name]) && strlen($input[$field_name]) > 0) {
            $vals = explode(' ', trim($input[$field_name]));
            if (sizeof($vals) > 0) {
                $rec = DB::table($table_name)->where('code', $vals[0])->first();
                if ($rec) {
                    return $vals[0];
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    public function ocr(Request $request)
    {

        $token = $request->header('authorization');
        if (!isset($token) || $token != 'Basic YXNoYXRlY2g6aUBQYXNzdzByZA==') {
            return response()->json([
                'status' => false,
            ], 401);
        }

        Log::info('*****OCR DATA*****');
        Log::info($request->all());

        return response()->json([
            'status' => true,
            'message' => 'บันทึกข้อมูลสำเร็จ.',
        ], 200);
    }

    public function storeFromApi(Request $request)
    {
        //check authorization
        $username = $request->getUser();
        $password = $request->getPassword();

        $response = $this->authService($username, $password);
        if (!$response) {
            return response()->json([
                'status' => false
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'title_code'            => 'required|exists:bd_title,code',
            'sex_code'              => 'required',
            // 'permanent_area_code'   => 'required',
            'hn'                    => 'required',
            'area_code'             => 'required|exists:bd_area,code',
            'birth_date'            => 'required|date_format:Ymd',
            'name'                  => 'required',
            'last_name'             => 'required',
            'nationality_code'      => 'required',
            'cid'                   => 'required|regex:/^\d{1}-\d{4}-\d{5}-\d{2}-\d{1}$/',
        ], [
            'title_code.required'            => 'Missing or not in list, please see reference',
            'sex_code.required'              => 'Missing or not in list, please see reference',
            // 'permanent_area_code.required'   => 'Missing or not in list, please see reference',
            'hn.required'                    => 'Missing',
            'area_code.required'             => 'Missing or not in list, please see reference',
            'birth_date.required'            => 'Missing or doesn\'t match the format or invalid date',
            'name.required'                  => 'Missing',
            'last_name.required'             => 'Missing',
            'nationality_code.required'      => 'Missing or not in list, please see reference',
            'cid.required'                   => 'Missing or doesn\'t match the format',
            'cid.regex'                      => 'Format X-XXXX-XXXXX-XX-X',
        ]);

        if ($validator->fails()) {
            $errors = Arr::map($validator->errors()->toArray(), fn($messages) => $messages[0]);

            return response()->json(
                $errors,
                400
            );
        }

        $input = $request->all();

        $input['hospital_code'] = $username;

        DB::beginTransaction();

        try {
            // Check if patient exists with the given cid
            $existingPatient = null;

            if ($input['cid'] == '0-0000-00000-00-0' || $input['cid'] == '9-9999-99999-99-9') {
                $existingPatient = DB::table('data_patient')
                    ->where('hospital_code', $input['hospital_code'])
                    ->where('cid', $input['cid'])
                    ->where('hn_no', $input['hn'])
                    ->first();
            } else {
                $existingPatient = DB::table('data_patient')
                    ->where('hospital_code', $input['hospital_code'])
                    ->where('cid', $input['cid'])
                    ->first();
            }

            $val_patient = [
                'hn_no'                             => $input['hn'],
                'title_code'                        => $input['title_code'],
                'name'                              => $input['name'],
                'last_name'                         => $input['last_name'],
                'cid'                               => $input['cid'],
                'birth_date'                        => getDateTimeFormStr($input['birth_date']),
                'hospital_code'                     => $input['hospital_code'],
                'sex_code'                          => $input['sex_code'],
                'nationality_code'                  => $input['nationality_code'],
                'death_date'                        => isset($input['death_date']) && $input['death_date'] != '' ? getDateTimeFormStr($input['death_date']) : null,
                'deathcause_code'                   => $input['deathcause_code'] ?? null,
                'address_no'                        => $input['address_no'] ?? null,
                'address_moo'                       => $input['address_moo'] ?? null,
                'area_code'                         => $input['area_code'] ?? null,
                'address_province_id'               => isset($input['area_code']) ? substr($input['area_code'], 0, 2) : null,
                'address_district_id'               => isset($input['area_code']) ? substr($input['area_code'], 0, 4) : null,
                'address_sub_district_id'           => isset($input['area_code']) ? substr($input['area_code'], 0, 6) : null,
                'permanent_address_no'              => $input['permanent_address_no'] ?? null,
                'permanent_address_moo'             => $input['permanent_address_moo'] ?? null,
                'permanent_area_code'               => $input['permanent_area_code'] ?? null,
                'permanent_address_province_id'     => isset($input['permanent_area_code']) ? substr($input['permanent_area_code'], 0, 2) : null,
                'permanent_address_district_id'     => isset($input['permanent_area_code']) ? substr($input['permanent_area_code'], 0, 4) : null,
                'permanent_address_sub_district_id' => isset($input['permanent_area_code']) ? substr($input['permanent_area_code'], 0, 6) : null,
                'email'                             => $input['email'] ?? null,
                'telephone_1'                       => $input['telephone_1'] ?? null,
                'source_id'                         => 3,
            ];

            $message = '';

            $log = [
                'log_date_time' => Carbon::now()->format('Y-m-d H:i:s'),
                'log_type' => null,
                'hospital_code' => $request->getUser(),
                'ref' => $input['cid'],
                'data' => json_encode($request->all())
            ];

            if ($existingPatient) {

                $message = 'Updated';

                $log['log_type'] = 'API_PATIENT_UPDATE';

                DB::table('log_api')->insert($log);

                if ($existingPatient->checked_api == 0) {
                    // Update existing patient if cid exists
                    $val_patient['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
                    DB::table('data_patient')
                        ->where('id', $existingPatient->id)
                        ->update($val_patient);

                    $patient_id = $existingPatient->id; // Return the ID of the existing patient
                }
            } else {
                // Insert new patient if cid does not exist
                $val_patient['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
                $val_patient['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
                $patient_id = DB::table('data_patient')->insertGetId($val_patient);

                $message = 'Created';

                $log['log_type'] = 'API_PATIENT_NEW';

                DB::table('log_api')->insert($log);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();

            Log::error($e);

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        return response()->json([
            'status' => true,
            'message' => $message,
            'id' => $patient_id
        ], 200);
    }

    public function getFromApi(Request $request)
    {
        //check authorization
        $username = $request->getUser();
        $password = $request->getPassword();

        $response = $this->authService($username, $password);
        if (!$response) {
            return response()->json([
                'status' => false
            ], 401);
        }

        $patient = DB::table('data_patient')
            ->where('hospital_code', $username)
            ->get();

        return response()->json([
            'status' => true,
            'data' => $patient
        ], 200);
    }

    public function getCidFromApi(Request $request, $cid)
    {
        //check authorization
        $username = $request->getUser();
        $password = $request->getPassword();

        $response = $this->authService($username, $password);
        if (!$response) {
            return response()->json([
                'status' => false
            ], 401);
        }

        $patient = DB::table('data_patient')
            ->where('hospital_code', $username)
            ->where('cid', $cid)
            ->first();

        return response()->json([
            'status' => true,
            'data' => $patient
        ], 200);
    }

    public function delete(Request $request, $id)
    {
        //check $id is exist
        $patient = DB::table('data_patient')->where('id', $id)->first();
        if (!$patient) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูลผู้ป่วย'
            ], 404);
        }

        DB::beginTransaction();

        try {
            DB::table('data_file')->where('patient_id', $id)->delete();
            DB::table('data_patient')->where('id', $id)->delete();

            DB::commit();

            return response()->json([
                'status' => true,
                'message' => 'ลบข้อมูลผู้ป่วยสำเร็จ'
            ], 200);
        } catch (Exception $e) {
            DB::rollback();

            Log::error($e);

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }
}
