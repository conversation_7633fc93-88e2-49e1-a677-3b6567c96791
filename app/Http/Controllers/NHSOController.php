<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NHSOController extends Controller
{
    public function index(Request $request)
    {
        // 🔹 ตรวจสอบ API Key
        $apiKey = $request->header('x-api-key');
        $validApiKey = env('API_KEY', 'ba4091db-182e-4cd8-9d74-73b8e5fd5bb0');

        if ($apiKey !== $validApiKey) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        // ✅ ถ้า API Key ถูกต้อง ดำเนินการต่อ
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        $orderBy = $request->input('order_by', 'DATE_update'); // ค่าเริ่มต้น
        $sort = strtolower($request->input('sort', 'desc')); // ค่าเริ่มต้น 'desc'

        $filter_DATE_update = $request->input('filter_DATE_update', null);

        // 🔹 ฟิลด์ที่อนุญาตให้เรียง
        $allowedFields = [
            'PID',
            'HCODE',
            'ICD10',
            'icd10_text',
            'Morphology',
            'morphology_text',
            'StageName',
            'Stage',
            'DATE_diag',
            'DATE_update'
        ];

        // 🔹 ตรวจสอบว่า order_by ถูกต้อง
        if (!in_array($orderBy, $allowedFields)) {
            return response()->json(['message' => 'Invalid order_by field'], 400);
        }

        // 🔹 ตรวจสอบค่า sort ให้เป็น 'asc' หรือ 'desc' เท่านั้น
        if (!in_array($sort, ['asc', 'desc'])) {
            return response()->json(['message' => 'Invalid sort value'], 400);
        }

        // vw_nhso_patient
        $query = DB::table('vw_nhso_patient')
            ->when(isset($filter_DATE_update), function ($query) use ($filter_DATE_update) {
                return $query->whereRaw("DATE(vw_nhso_patient.DATE_update) = ?", [$filter_DATE_update]);
            })
            ->orderBy($orderBy, $sort);
        // ->limit(100)
        // ->get();


        // 🔹 ตรวจสอบว่า filter_DATE_update เป็นวันที่ที่ถูกต้อง
        // $query = DB::table('data_patient')
        //     ->join('data_cancer', 'data_patient.id', '=', 'data_cancer.patient_id')
        //     ->join('bd_stage', 'bd_stage.code', '=', 'data_cancer.stage_code')
        //     ->select([
        //         'data_patient.cid as PID',
        //         'data_patient.hospital_code as HCODE',
        //         'data_cancer.icd10_code as ICD10',
        //         'data_cancer.icd10_text',
        //         'data_cancer.morphology_text',
        //         'bd_stage.name as StageName',
        //         'data_cancer.diagnosis_date as DATE_diag',
        //         'data_cancer.updated_at as DATE_update'
        //     ])
        //     // 🔹 เรียงลำดับตามค่าที่กำหนด
        //     ->when(isset($filter_DATE_update), function ($query) use ($filter_DATE_update) {
        //         return $query->whereRaw("DATE(data_cancer.updated_at) = ?", [$filter_DATE_update]);
        //     })
        //     ->where('data_patient.cid', '<>', '9-9999-99999-99-9')
        //     ->where('data_patient.cid', '<>', '0-0000-00000-00-0')
        //     ->orderBy($orderBy, $sort);

        $patients = $query->paginate($perPage, ['*'], 'page', $page);

        return response()->json($patients);
        // return response()->json($query);
    }

    public function refer(Request $request)
    {
        // 🔹 ตรวจสอบ API Key
        $apiKey = $request->header('x-api-key');
        $validApiKey = env('API_KEY', 'ba4091db-182e-4cd8-9d74-73b8e5fd5bb0');

        if ($apiKey !== $validApiKey) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        // ✅ ถ้า API Key ถูกต้อง ดำเนินการต่อ
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        $orderBy = $request->input('order_by', 'DATE_update'); // ค่าเริ่มต้น
        $sort = strtolower($request->input('sort', 'desc')); // ค่าเริ่มต้น 'desc'

        $filter_DATE_update = $request->input('filter_DATE_update', null);

        // 🔹 ฟิลด์ที่อนุญาตให้เรียง
        $allowedFields = [
            'PID',
            'HCODE_refer_out',
            'ICD10',
            'Morphology',
            'Stage',
            'Payer',
            'HCODE_refer_in',
            'Refer_reason',
            'Refer_status',
            'DATE_update'
        ];

        // 🔹 ตรวจสอบว่า order_by ถูกต้อง
        if (!in_array($orderBy, $allowedFields)) {
            return response()->json(['message' => 'Invalid order_by field'], 400);
        }

        // 🔹 ตรวจสอบค่า sort ให้เป็น 'asc' หรือ 'desc' เท่านั้น
        if (!in_array($sort, ['asc', 'desc'])) {
            return response()->json(['message' => 'Invalid sort value'], 400);
        }

        // vw_nhso_patient
        $query = DB::table('vw_refer_for_nhso')
            ->when(isset($filter_DATE_update), function ($query) use ($filter_DATE_update) {
                return $query->whereRaw("DATE(vw_refer_for_nhso.DATE_update) = ?", [$filter_DATE_update]);
            })
            ->orderBy($orderBy, $sort);

        $patients = $query->paginate($perPage, ['*'], 'page', $page);

        $patients->getCollection()->transform(function ($item) {
            $files = json_decode($item->files, true) ?? [];

            $filteredFiles = array_filter($files, function ($file) {
                return is_array($file) && isset($file['id']) && $file['id'] !== null;
            });

            $mappedFiles = array_map(function ($file) {
                if (!empty($file['file_path'])) {
                    // ต่อ path เต็มโดยใช้ public_path()
                    $file['file_path'] = env('APP_URL') . '/' . (ltrim($file['file_path'], '/'));
                } else {
                    $file['file_path'] = null;
                }
                return $file;
            }, $filteredFiles);

            $item->files = array_values($mappedFiles);
            return $item;
        });

        return response()->json($patients);
    }
}
