<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Mgmt\MgmtForm;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;


class NotifyLogUserController extends Controller
{


    public function readNotify($id)
    {
        // ดึงข้อมูล NotifyLogUser ตาม ID
        $NotifyLogUser = DB::table('notify_log_user')
            ->where('id', $id)
            ->first();

        if ($NotifyLogUser) {
            // อัปเดตคอลัมน์ read และ send
            DB::table('notify_log_user')
                ->where('id', $id)
                ->update([
                    'read' => true,
                    'updated_at' => now() // กรณีมี timestamp
                ]);

            // รวมข้อมูล NotifyLog (เหมือนกับการใช้ with)
            $NotifyLog = DB::table('notify_log')
                ->where('id', $NotifyLogUser->notify_log_id)
                ->first();

            // รวมข้อมูลที่อัปเดตและข้อมูล notifyLog
            $NotifyLogUser->read = true;
            $NotifyLogUser->send = true;
            $NotifyLogUser->notifyLog = $NotifyLog;
        }


        return response()->json([
            'status' => true,
            'message' => 'สำเร็จ',
            'data' => $NotifyLogUser
        ], 200);
    }

    public function get(Request $request)
    {

        $notify_log_id = $request->notify_log_id;
        $cid = $request->cid;
        $approve = $request->approve;
        $read = $request->read;

        $date_start = $request->date_start;
        $date_stop = $request->date_stop;

        $notifyLogUserQuery = DB::table('notify_log_user')
            ->join('notify_log', 'notify_log_user.notify_log_id', '=', 'notify_log.id') // เชื่อมกับ notify_log
            ->select('notify_log_user.*', 'notify_log.title', 'notify_log.detail', 'notify_log.type', 'notify_log.created_at') // เลือกคอลัมน์ที่ต้องการ
            ->orderBy('notify_log_user.id', 'desc');

        // กรณีมี $date_start
        if (isset($date_start)) {
            $notifyLogUserQuery->whereBetween('notify_log.created_at', [
                $date_start . ' 00:00:00',
                $date_stop . ' 23:59:59',
            ]);
        }

        // กรณีไม่มี $date_start
        if (empty($date_start)) {
            $notifyLogUserQuery->whereNotIn('notify_log.type', [
                'sup_req_pr_approve',
                'plan_req_ma_approve',
                'plan_req_item_approve',
                'plan_project_approve',
                'plan_budget_approve',
                'ot_year_approve',
                'ot_month_approve',
            ]);
        }

        // เงื่อนไข notify_log_id
        if (isset($notify_log_id)) {
            $notifyLogUserQuery->where('notify_log_user.notify_log_id', $notify_log_id);
        }

        // เงื่อนไข cid
        if (isset($cid)) {
            $notifyLogUserQuery->where('notify_log_user.cid', $cid);
        }

        // เงื่อนไข read
        if (isset($read)) {
            $notifyLogUserQuery->where('notify_log_user.read', $read === 'false' ? false : true);
        }

        // ดึงข้อมูลทั้งหมด
        $NotifyLogUser = $notifyLogUserQuery->get()->toArray();

        if (!empty($NotifyLogUser)) {

            // $check_status = false;
            for ($i = 0; $i < count($NotifyLogUser); $i++) {
            }
        }
        return response()->json([
            'status' => true,
            'message' => 'สำเร็จ',
            'data' => $NotifyLogUser
        ], 200);
    }

    public function Page(Request $request)
    {

        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search.value', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $notify_log_id = $request->notify_log_id;
        $cid = $request->cid;
        $approve = $request->approve;
        $read = $request->read;
        $url = $request->page_type;


        $date_start = $request->date_start;
        $date_stop = $request->date_stop;


        $searchable = [''];

        // สร้าง Query สำหรับ NotifyLogUser
        $query = DB::table('notify_log_user')
            ->join('notify_log', 'notify_log_user.notify_log_id', '=', 'notify_log.id') // เชื่อม notify_log_user กับ notify_log
            ->select('notify_log_user.*', 'notify_log.title', 'notify_log.detail', 'notify_log.type', 'notify_log.created_at') // เลือกคอลัมน์ที่ต้องการ
            ->orderBy('notify_log_user.id', 'desc'); // เรียงลำดับ

        // กรณีมี $date_start
        if (isset($date_start)) {
            $query->whereBetween('notify_log.created_at', [
                $date_start . ' 00:00:00',
                $date_stop . ' 23:59:59',
            ]);
        }

        // กรณีไม่มี $date_start
        if (empty($date_start)) {
            $query->whereNotIn('notify_log.type', [
                'sup_req_pr_approve',
                'plan_req_ma_approve',
                'plan_req_item_approve',
                'plan_project_approve',
                'plan_budget_approve',
                'ot_year_approve',
                'ot_month_approve',
            ]);
        }

        // เงื่อนไข notify_log_id
        if (isset($notify_log_id)) {
            $query->where('notify_log_user.notify_log_id', $notify_log_id);
        }

        // เงื่อนไข cid
        if (isset($cid)) {
            $query->where('notify_log_user.cid', $cid);
        }

        // เงื่อนไข read
        if (isset($read)) {
            $query->where('notify_log_user.read', $read === 'false' ? false : true);
        }

        // เงื่อนไขค้นหาด้วย LIKE
        if ($search) {
            $query->where(function ($subQuery) use ($search, $searchable) {
                foreach ($searchable as $s) {
                    $subQuery->orWhere($s, 'LIKE', '%' . $search . '%');
                }
            });
        }

        if (!empty($columns)) {
            $query->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir']);
        }

        // ดึงข้อมูลและแปลงเป็นการแบ่งหน้า
        $NotifyLogUser = $query->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);

        if ($NotifyLogUser->isNotEmpty()) {

            //run no
            $No = (($page - 1) * $perPage);

            for ($i = 0; $i < count($NotifyLogUser); $i++) {


                $No = $No + 1;
                $NotifyLogUser[$i]->No = $No;
            }
        }

        return response()->json([
            'status' => true,
            'message' => 'สำเร็จ',
            'data' => $NotifyLogUser
        ], 200);
    }
}
