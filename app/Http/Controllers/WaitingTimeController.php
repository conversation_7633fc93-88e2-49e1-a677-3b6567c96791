<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class WaitingTimeController extends Controller
{
    protected const TRT_EXC = "0 Biopsy";
    protected const TRT_SUR = "1 Surgery";
    protected const TRT_RADIATION = "2 Radiation";
    protected const TRT_CHEMO = "3 Chemotherapy";
    protected const TABLE = "data_treatment_group";

    public function waitCid($cid = null)
    {
        $result = DB::table(self::TABLE)
            ->distinct()
            ->select('key_')
            ->where('cid', '!=', '9-9999-99999-99-9')
            ->where('cid', '!=', '0-0000-00000-00-0')
            ->when($cid != null, function ($query) use ($cid) {
                return $query->where('cid', $cid);
            })
            ->whereNotNull('key_')
            ->get();

        //จะได้ CID แยกตามโรค
        foreach ($result as $value) {
            //คำนวณทีละโรค
            $this->processTreatmentGroup($value->key_);
        }

        return response()->json([
            'status'    => true,
            'data'      => $result
        ], 200);
    }

    private function processTreatmentGroup($key_)
    {
        $treatments = $this->processRadiation($key_);

        $treatments = $this->processSur($key_, $treatments);

        $treatments = collect($treatments)->sortBy('start_date')->toArray();

        $treatments = $this->processChemo($key_, $treatments);

        $treatments = collect($treatments)->sortBy('start_date')->toArray();

        $treatments = $this->processExcision($key_, $treatments);

        $treatments = collect($treatments)->sortBy('start_date')->toArray();

        foreach ($treatments as $key => $treatment) {
            $treatments[$key]['key_'] = $key_;
        }

        DB::table('treatment_group')->where('key_', '=', $key_)->delete();

        try {
            DB::table('treatment_group')->insert($treatments);
        } catch (\Throwable $th) {
            Log::error($key_ . 'error' . $th->getMessage());
        }
    }

    private function processRadiation($key_)
    {
        $query = DB::table(self::TABLE)->where('key_', '=', $key_)
            ->where('treatment_code', self::TRT_RADIATION)
            ->orderBy('trt_date_dt')
            ->get();

        $treatments = [];
        $treatmentGroup = [
            'treatment_code'    => self::TRT_RADIATION,
            'start_date'        => null,
            'end_date'          => null,
            'previous_date'     => null,
            'area'              => null,
            'cid'               => null,
            'name'              => null,
            'last_name'         => null,
            'hospital_code'     => null,
        ];

        foreach ($query as $key => $value) {
            if ($treatmentGroup['start_date'] == null) {
                $treatmentGroup['start_date'] = $value->trt_date_dt;
                $treatmentGroup['end_date'] = $value->trt_date_dt;
            } else if ((Carbon::parse($treatmentGroup['end_date'])->diffInDays(Carbon::parse($value->trt_date_dt))) > 90) {
                $treatments[] = $treatmentGroup;
                $treatmentGroup = [
                    'treatment_code' => self::TRT_RADIATION,
                    'start_date' => null,
                    'end_date' => null,
                    'previous_date' => null,
                    'area' => $value->area
                ];
                $treatmentGroup['start_date'] = $value->trt_date_dt;
                $treatmentGroup['end_date'] = $value->trt_date_dt;
            } else {
                $treatmentGroup['end_date'] = $value->trt_date_dt;
            }

            $treatmentGroup['area'] = $value->area;
            $treatmentGroup['cid'] = $value->cid;
            $treatmentGroup['name'] = $value->name;
            $treatmentGroup['last_name'] = $value->last_name;
            $treatmentGroup['hospital_code'] = $value->hospital_code;
        }

        if ($treatmentGroup['end_date'] != null) {
            $treatments[] = $treatmentGroup;
        }

        return $treatments;
    }

    private function processExcision($key_, array $treatments)
    {
        $key = explode('#', $key_);
        $hos_code = $key[0];
        $cid = $key[1];
        $icd10 = $key[2];

        $query = DB::table(self::TABLE)
            ->select([
                'key_',
                'area',
                'cid',
                'name',
                'last_name',
                'hospital_code',
                DB::raw('MIN(trt_date_dt) AS first_visit_date'),
                DB::raw("MIN(CASE WHEN treatment_code IN ('CUT', 'READ', 'DIAG', 'VISIT') THEN trt_date_dt ELSE NULL END) AS diag_date"),
                DB::raw("MIN(CASE WHEN treatment_code = 'READ' THEN trt_date_dt ELSE NULL END) AS start_date"),
                DB::raw("MAX(CASE WHEN treatment_code = 'READ' THEN trt_date_dt ELSE NULL END) AS end_date"),
                DB::raw("MIN(CASE WHEN treatment_code = 'CUT' THEN trt_date_dt ELSE NULL END) AS prev_date"),
            ])
            ->where('key_', '=', $key_)
            ->groupBy('key_')
            ->get();

        foreach ($query as $row) {
            if ($row->start_date == null && $row->end_date == null) {
                continue;
            }

            $treatmentGroup = [
                'treatment_code'    => self::TRT_EXC,
                'start_date'        => $row->start_date,
                'end_date'          => $row->end_date,
                'previous_date'     => $row->prev_date,
                'area'              => $row->area,
                'cid'               => $row->cid,
                'name'              => $row->name,
                'last_name'         => $row->last_name,
                'hospital_code'     => $row->hospital_code,
            ];
            $treatments[] = $treatmentGroup;
        }

        return $treatments;
    }

    private function processSur($key_, array $treatments)
    {
        $query = DB::table(self::TABLE)->where('key_', '=', $key_)
            ->where('treatment_code', self::TRT_SUR)
            ->whereNotNull('trt_date_dt')
            ->orderBy('trt_date_dt')
            ->get();

        foreach ($query as $key => $value) {
            $dateToCheck = new DateTime($value->trt_date_dt);
            $checkDate = $this->checkDateInRange($treatments, $dateToCheck);
            if (!$checkDate) {
                $treatmentGroup = [
                    'treatment_code' => self::TRT_SUR,
                    'start_date' => null,
                    'end_date' => null,
                    'previous_date' => null,
                    'area'  => $value->area,
                    'cid'               => $value->cid,
                    'name'              => $value->name,
                    'last_name'         => $value->last_name,
                    'hospital_code'     => $value->hospital_code,
                ];
                $treatmentGroup['start_date'] = $value->trt_date_dt;
                $treatmentGroup['end_date'] = $value->trt_date_dt;

                // $treatmentGroup['previous_date'] = $value->diagnosis_date_dt;


                // $treatments[] = $treatmentGroup;

                // ตรวจสอบว่ามี TreatmentGroup ใดที่มี treatment_code เหมือนกันหรือไม่
                $lastTreatmentGroup = null;

                // วนลูปจากท้ายอาร์เรย์เพื่อหา TreatmentGroup ตัวล่าสุดที่มี treatment_code เหมือนกัน
                for ($i = count($treatments) - 1; $i >= 0; $i--) {
                    if ($treatments[$i]['treatment_code'] === $treatmentGroup['treatment_code']) {
                        $lastTreatmentGroup = $treatments[$i];
                        break;
                    }
                }

                if ($lastTreatmentGroup !== null) {
                    // หากพบ TreatmentGroup ที่มี treatment_code เหมือนกัน
                    $lastEndDate = new DateTime(date('Y-m-d', strtotime($lastTreatmentGroup['end_date'])));
                    $newStartDate = new DateTime(date('Y-m-d', strtotime($treatmentGroup['start_date'])));

                    // คำนวณความแตกต่างระหว่างวันที่
                    $interval = $lastEndDate->diff($newStartDate);
                    $daysDifference = $interval->days;

                    // ตรวจสอบว่าความแตกต่างเกิน 4 สัปดาห์ (28 วัน) หรือไม่
                    if ($daysDifference > 28) {
                        // ถ้าเกิน 4 สัปดาห์ ให้เพิ่ม TreatmentGroup ใหม่เข้าไปใน $treatments
                        $treatments[] = $treatmentGroup;
                    }
                } else {
                    // หากไม่พบ TreatmentGroup ที่มี treatment_code เหมือนกัน
                    // ให้เพิ่ม TreatmentGroup ใหม่เข้าไปใน $treatments โดยไม่ต้องตรวจสอบเงื่อนไข
                    $treatments[] = $treatmentGroup;
                }
            }
        }

        return $treatments;
    }

    private function processChemo($key_, array $treatments)
    {
        $query = DB::table(self::TABLE)->where('key_', '=', $key_)
            ->where('treatment_code', self::TRT_CHEMO)
            ->orderBy('trt_date_dt')
            ->get();

        $treatmentGroup = [
            'treatment_code'    => self::TRT_CHEMO,
            'start_date'        => null,
            'end_date'          => null,
            'previous_date'     => null,
            'area'              => null,
            'cid'               => null,
            'name'              => null,
            'last_name'         => null,
            'hospital_code'     => null,
        ];

        foreach ($query as $key => $value) {
            $treatmentGroup['area'] = $value->area;
            $treatmentGroup['cid'] = $value->cid;
            $treatmentGroup['name'] = $value->name;
            $treatmentGroup['last_name'] = $value->last_name;
            $treatmentGroup['hospital_code'] = $value->hospital_code;

            $dateToCheck = new DateTime($value->trt_date_dt);
            $checkDate = $this->checkDateInRange($treatments, $dateToCheck);

            if ($checkDate) {
                continue;
            }

            $checkDate2 = $this->checkDateInRange2(
                $treatments,
                ($treatmentGroup['start_date'] == null
                    ? $dateToCheck
                    : new DateTime(!$treatmentGroup['start_date'])),
                $dateToCheck
            );

            if (!$checkDate2) {
                if ($treatmentGroup['start_date'] == null) {
                    $treatmentGroup['start_date'] = $value->trt_date_dt;
                    $treatmentGroup['end_date'] = $value->trt_date_dt;
                } else {
                    $treatmentGroup['end_date'] = $value->trt_date_dt;
                }
            } else if ($treatmentGroup['start_date'] != null) {
                $treatments[] = $treatmentGroup;
                $treatmentGroup = [
                    'treatment_code' => self::TRT_CHEMO,
                    'start_date' => $value->trt_date_dt,
                    'end_date' => $value->trt_date_dt
                ];
            }
        }

        if ($treatmentGroup['end_date'] != null) {
            $treatments[] = $treatmentGroup;
        }

        return $treatments;
    }

    private function checkDateInRange(array $treatmentGroups, DateTime $dateToCheck): bool
    {
        foreach ($treatmentGroups as $group) {
            $startDate = new DateTime($group['start_date']);
            $endDate = new DateTime($group['end_date']);

            if (
                $dateToCheck == $startDate ||
                $dateToCheck == $endDate ||
                ($dateToCheck > $startDate && $dateToCheck < $endDate)
            ) {
                return true;
            }
        }
        return false;
    }

    private function checkDateInRange2(array $treatmentGroups, DateTime $newStartDate, DateTime $newEndDate): bool
    {
        foreach ($treatmentGroups as $group) {
            $groupStartDate = new DateTime($group['start_date']);
            $groupEndDate = new DateTime($group['end_date']);

            if (
                $newStartDate == $groupStartDate ||
                $newStartDate == $groupEndDate ||
                $newEndDate == $groupStartDate ||
                $newEndDate == $groupEndDate ||
                ($newStartDate > $groupStartDate && $newStartDate < $groupEndDate) ||
                ($newEndDate > $groupStartDate && $newEndDate < $groupEndDate) ||
                ($groupStartDate > $newStartDate && $groupStartDate < $newEndDate) ||
                ($groupEndDate > $newStartDate && $groupEndDate < $newEndDate)
            ) {
                return true;
            }
        }
        return false;
    }

    public function createTreamentFromOldData()
    {
        $result = DB::table('data_treatment')
            ->join('data_cancer', 'data_treatment.cancer_id', '=', 'data_cancer.id')
            ->join('data_patient', 'data_treatment.id', '=', 'data_cancer.patient_id')
            // ->join('data_patient', function ($join) {
            //     $join->on('data_cancer.patient_id', '=', 'data_patient.id')
            //         ->on('data_treatment.patient_id', '=', 'data_patient.id');
            // })
            ->join('bd_treatment', 'data_treatment.treatment_code', '=', 'bd_treatment.code')
            ->select(
                'data_patient.hospital_code',
                'data_patient.hn_no',
                'data_patient.cid',
                'data_patient.area_code',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.sex_code',
                'data_patient.birth_date',
                'data_cancer.icd10_code',
                'data_cancer.diagnosis_date',
                'data_cancer.excision_in_cut_date',
                'data_cancer.excision_in_read_date',
                'data_cancer.entrance_date',
                DB::raw("CONCAT(bd_treatment.code, ' ', bd_treatment.name) AS treatment_name"),
                'data_treatment.treatment_date'
            )
            ->whereNotNull('data_treatment.treatment_date')
            // ->where('data_cancer.hospital_code', '10690')
            ->orderBy('data_treatment.id')
            ->chunk(1000, function ($results) { // Process 100 records at a time
                $data = [];
                foreach ($results as $value) {
                    $icd10_code = substr($value->icd10_code, 0, 3);
                    $key_ = $value->hospital_code . '#' . $value->cid . '#' . $icd10_code;
                    $val_can_treat = [
                        'key_'                      => $key_,
                        'hospital_code'             => $value->hospital_code,
                        'hn_no'                     => $value->hn_no,
                        'cid'                       => $value->cid,
                        'name'                      => $value->name,
                        'last_name'                 => $value->last_name,
                        'sex_code'                  => $value->sex_code,
                        'birth_date'                => date('d/m/Y', strtotime($value->birth_date)),
                        'icd10'                     => $icd10_code,
                        'treatment_code'            => $value->treatment_name,
                        'trt_date'                  => date('d/m/Y', strtotime($value->treatment_date)),
                        'trt_date_dt'               => $value->treatment_date,
                        'area'                      => $value->area_code,
                        'diagnosis_date_dt'         => $value->diagnosis_date,
                        'excision_cut_date_dt'      => $value->excision_in_cut_date,
                        'excision_read_date_dt'     => $value->excision_in_read_date,
                        'entrance_date_dt'          => $value->entrance_date,
                    ];

                    $data[] = $val_can_treat;
                }

                DB::table('data_treatment_group')->insert($data);
                try {
                } catch (\Throwable $th) {
                    // Log or handle the error as needed
                    // Example: Log::error('Error inserting treatment data: ' . $th->getMessage());
                }
            });

        DB::table('data_cancer')
            ->join('data_patient', 'data_cancer.patient_id', '=', 'data_patient.id')
            ->selectRaw("
                CONCAT(data_cancer.hospital_code, '#', data_patient.cid, '#', SUBSTRING(data_cancer.icd10_code, 1, 3)) AS key_,
                data_cancer.hospital_code,
                data_patient.hn_no,
                data_patient.cid,
                data_patient.name,
                data_patient.last_name,
                data_patient.sex_code,
                data_patient.birth_date,
                SUBSTRING(data_cancer.icd10_code, 1, 3) AS icd10,
                '0 Biopsy' AS treatment_code,
                MAX(data_cancer.excision_in_read_date) AS trt_date_dt,
                MAX(data_cancer.excision_in_read_date) AS treatment_end_date,
                data_patient.area_code AS area,
                MAX(data_cancer.excision_in_cut_date) AS trt_date_end_dt,
                MAX(data_cancer.diagnosis_date) AS diagnosis_date_dt,
                MAX(data_cancer.excision_in_cut_date) AS excision_cut_date_dt,
                MAX(data_cancer.excision_in_read_date) AS excision_read_date_dt,
                MAX(data_cancer.entrance_date) AS entrance_date_dt
            ")
            ->whereNotNull('data_cancer.excision_in_cut_date')
            ->whereNotNull('data_cancer.excision_in_read_date')
            ->whereNotNull('data_cancer.icd10_code')
            ->groupBy(
                'data_cancer.hospital_code',
                'data_patient.cid',
                'data_cancer.icd10_code',
                'data_patient.hn_no',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.sex_code',
                'data_patient.birth_date',
                'data_patient.area_code'
            )
            ->orderBy('key_')
            ->chunk(1000, function ($results) { // Process 100 records at a time
                $data = [];
                foreach ($results as $value) {
                    $val_can_treat = [
                        'key_'                      => $value->key_,
                        'hospital_code'             => $value->hospital_code,
                        'hn_no'                     => $value->hn_no,
                        'cid'                       => $value->cid,
                        'name'                      => $value->name,
                        'last_name'                 => $value->last_name,
                        'sex_code'                  => $value->sex_code,
                        'birth_date'                => date('d/m/Y', strtotime($value->birth_date)),
                        'icd10'                     => $value->icd10,
                        'treatment_code'            => $value->treatment_code,
                        'trt_date'                  => date('d/m/Y', strtotime($value->trt_date_dt)),
                        'trt_date_dt'               => $value->trt_date_dt,
                        'trt_date_end'              => date('d/m/Y', strtotime($value->trt_date_end_dt)),
                        'trt_date_end_dt'           => $value->trt_date_end_dt,
                        'area'                      => $value->area,
                        'diagnosis_date_dt'         => $value->diagnosis_date_dt,
                        'excision_cut_date_dt'      => $value->excision_cut_date_dt,
                        'excision_read_date_dt'     => $value->excision_read_date_dt,
                        'entrance_date_dt'          => $value->entrance_date_dt,
                    ];

                    $data[] = $val_can_treat;
                }

                DB::table('data_treatment_group')->insert($data);
                try {
                } catch (\Throwable $th) {
                    // Log or handle the error as needed
                    // Example: Log::error('Error inserting treatment data: ' . $th->getMessage());
                }
            });
    }

    public function _createTreamentFromOldData()
    {
        $result = DB::table('data_treatment')
            ->join('data_cancer', 'data_treatment.cancer_id', '=', 'data_cancer.id')
            ->join('data_patient', function ($join) {
                $join->on('data_cancer.patient_id', '=', 'data_patient.id')
                    ->on('data_treatment.patient_id', '=', 'data_patient.id');
            })
            ->join('bd_treatment', 'data_treatment.treatment_code', '=', 'bd_treatment.code')
            ->select(
                'data_patient.hospital_code',
                'data_patient.hn_no',
                'data_patient.cid',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.sex_code',
                'data_patient.birth_date',
                'data_cancer.icd10_code',
                DB::raw("CONCAT(bd_treatment.code, ' ', bd_treatment.name) AS treatment_name"),
                'data_treatment.treatment_date'
            )
            ->where('data_cancer.hospital_code', '14199')
            ->get();

        foreach ($result as $key => $value) {
            $key_ = $value->hospital_code . $value->cid . $value->icd10_code;
            $val_can_treat = [
                'key_'              => $key_,
                'hospital_code'     => $value->hospital_code,
                'hn_no'             => $value->hn_no,
                'cid'               => $value->cid,
                'name'              => $value->name,
                'last_name'         => $value->last_name,
                'sex_code'          => $value->sex_code,
                'birth_date'        => date('d/m/Y', strtotime($value->birth_date)),
                'icd10'             => $value->icd10_code,
                'treatment_code'    => $value->treatment_name,
                'trt_date'          => date('d/m/Y', strtotime($value->treatment_date)),
                'trt_date_dt'       => $value->treatment_date
            ];

            try {
                DB::table('data_treatment_group')->insert($val_can_treat);
            } catch (\Throwable $th) {
                //throw $th;
            }
        }
    }

    private function calculateWaitingFlag($waiting_days, $threshold_pass, $threshold_fail)
    {
        if ($waiting_days < $threshold_pass) {
            return 'PASS';
        } elseif ($waiting_days <= $threshold_fail) {
            return 'FAIL';
        } else {
            return 'NO';
        }
    }

    public function waitingRp1(Request $request)
    {
        $user = Auth::user();

        $date_start = $request->query('date_start');
        $date_end = $request->query('date_end');

        $results = DB::table('treatment_group', 't')
            ->select(
                DB::raw("COUNT(CASE WHEN t.treatment_code = '1 Surgery' THEN 0 END) AS surg"),
                DB::raw("COUNT(CASE WHEN t.treatment_code = '2 Radiation' THEN 0 END) AS radi"),
                DB::raw("COUNT(CASE WHEN t.treatment_code = '3 Chemotherapy' THEN 0 END) AS chem"),
            )
            ->whereBetween('t.start_date', [$date_start, $date_end])
            ->where('hospital_code', $user->hosCode)
            ->first();

        $results2 = DB::table('treatment_group', 't')
            ->select(
                DB::raw("COUNT(CASE WHEN t.treatment_code = '0 Biopsy' THEN 0 END) AS exci"),
            )
            ->whereBetween('t.previous_date', [$date_start, $date_end])
            ->where('hospital_code', $user->hosCode)
            ->first();

        return response()->json([
            'status' => true,
            // 'message' => 'บันทึกข้อมูลสำเร็จ.',
            'data' => [
                'excision' => $results2->exci,
                'surgery' => $results->surg,
                'radiation' => $results->radi,
                'chemotherapy' => $results->chem,
            ]
        ], 200);
    }

    public function waitingRp2(Request $request)
    {
        $user = Auth::user();

        $years = $request->year;

        $data = [];
        foreach ($years as $year) {
            $yearCE = $year - 543; // แปลง พ.ศ. เป็น ค.ศ.
            $date_start = $yearCE . '-10-01';
            $date_end = $yearCE + 1 . '-09-30';

            $results = DB::table('treatment_group', 't')
                ->select(
                    DB::raw("COUNT(CASE WHEN t.treatment_code = '1 Surgery' THEN 0 END) AS surg"),
                    DB::raw("COUNT(CASE WHEN t.treatment_code = '2 Radiation' THEN 0 END) AS radi"),
                    DB::raw("COUNT(CASE WHEN t.treatment_code = '3 Chemotherapy' THEN 0 END) AS chem")
                )
                ->whereBetween('t.start_date', [$date_start, $date_end])
                ->where('hospital_code', $user->hosCode)
                ->first();

            $results2 = DB::table('treatment_group', 't')
                ->select(
                    DB::raw("COUNT(CASE WHEN t.treatment_code = '0 Biopsy' THEN 0 END) AS exci")
                )
                ->whereBetween('t.previous_date', [$date_start, $date_end])
                ->where('hospital_code', $user->hosCode)
                ->first();

            $data[] = [
                'year' => $year,
                'data' => [
                    'excision' => $results2->exci ?? 0,
                    'surgery' => $results->surg ?? 0,
                    'radiation' => $results->radi ?? 0,
                    'chemotherapy' => $results->chem ?? 0,
                ]
            ];
        }

        return response()->json([
            'status' => true,
            'data' => $data
        ], 200);
    }

    public function waitingRp3(Request $request)
    {
        $date_start = $request->date_start;
        $date_end = $request->date_end;

        $area_level_id = $request->area_level_id;
        $area_id = $request->area_id;

        $data = [];

        if ($area_level_id == 1) {
            $ref_health_regions = DB::table('ref_health_regions')
                ->select('full_id', 'health_region_name')
                ->get();

            $result = DB::table('treatment_group')
                ->select(
                    'treatment_group.hospital_area_code',
                    'treatment_group.treatment_code',
                    'treatment_group.waiting_flag',
                    DB::raw('COUNT(*) as count')
                )
                // ->where('treatment_group.area_level_id', $area_level_id)
                // ->where('treatment_group.area_id', $area_id)
                ->whereBetween('treatment_group.start_date', [$date_start, $date_end])
                ->groupBy('treatment_group.treatment_code', 'treatment_group.waiting_flag', 'treatment_group.hospital_area_code')
                ->get();

            foreach ($ref_health_regions as $ref_health_region) {
                $row = [
                    'name'  => $ref_health_region->health_region_name,
                    'total' => 0,
                    'calculated' => 0,
                    'no_calculated' => 0,
                    'exsicion_total' => 0,
                    'exsicion_pass' => 0,
                    'exsicion_fail' => 0,
                    'exsicion_no' => 0,
                    'exsicion_cal' => 0,
                    'surgery_total' => 0,
                    'surgery_pass' => 0,
                    'surgery_fail' => 0,
                    'surgery_no' => 0,
                    'surgery_cal' => 0,
                    'radiation_total' => 0,
                    'radiation_pass' => 0,
                    'radiation_fail' => 0,
                    'radiation_no' => 0,
                    'radiation_cal' => 0,
                    'chemo_total' => 0,
                    'chemo_pass' => 0,
                    'chemo_fail' => 0,
                    'chemo_no' => 0,
                    'chemo_cal' => 0,
                ];

                foreach ($result as $item) {

                    if ($item->hospital_area_code == $ref_health_region->full_id) {
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'PASS') {
                            $row['surgery_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'FAIL') {
                            $row['surgery_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'NO') {
                            $row['surgery_no'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'PASS') {
                            $row['radiation_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'FAIL') {
                            $row['radiation_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'NO') {
                            $row['radiation_no'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'PASS') {
                            $row['chemo_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'FAIL') {
                            $row['chemo_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'NO') {
                            $row['chemo_no'] = $item->count;
                        }
                    }
                }

                $data[] = $row;
            }
        } elseif ($area_level_id == 2) {
            $provoinces = DB::table('ref_provinces')
                ->select('id', 'province_name_th')
                ->where('health_region_id', $area_id)
                ->orderBy('id')
                ->get();

            $result = DB::table('treatment_group')
                ->select(
                    'treatment_group.hospital_area_code',
                    'treatment_group.treatment_code',
                    'treatment_group.waiting_flag',
                    'treatment_group.area',
                    DB::raw('COUNT(*) as count')
                )
                ->where('treatment_group.health_region', $area_id)
                // ->where('treatment_group.area_level_id', $area_level_id)
                // ->where('treatment_group.area_id', $area_id)
                ->whereBetween('treatment_group.start_date', [$date_start, $date_end])
                ->groupBy('treatment_group.treatment_code', 'treatment_group.waiting_flag', 'treatment_group.hospital_area_code', 'treatment_group.area')
                ->get();

            foreach ($provoinces as $provoince) {
                $row = [
                    'name'  => $provoince->province_name_th,
                    'total' => 0,
                    'calculated' => 0,
                    'no_calculated' => 0,
                    'exsicion_total' => 0,
                    'exsicion_pass' => 0,
                    'exsicion_fail' => 0,
                    'exsicion_no' => 0,
                    'exsicion_cal' => 0,
                    'surgery_total' => 0,
                    'surgery_pass' => 0,
                    'surgery_fail' => 0,
                    'surgery_no' => 0,
                    'surgery_cal' => 0,
                    'radiation_total' => 0,
                    'radiation_pass' => 0,
                    'radiation_fail' => 0,
                    'radiation_no' => 0,
                    'radiation_cal' => 0,
                    'chemo_total' => 0,
                    'chemo_pass' => 0,
                    'chemo_fail' => 0,
                    'chemo_no' => 0,
                    'chemo_cal' => 0,
                ];

                foreach ($result as $item) {
                    $provoince_id = substr($item->area, 0, 2);
                    if ($provoince_id == $provoince->id) {
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'PASS') {
                            $row['surgery_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'FAIL') {
                            $row['surgery_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'NO') {
                            $row['surgery_no'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'PASS') {
                            $row['radiation_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'FAIL') {
                            $row['radiation_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'NO') {
                            $row['radiation_no'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'PASS') {
                            $row['chemo_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'FAIL') {
                            $row['chemo_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'NO') {
                            $row['chemo_no'] = $item->count;
                        }
                    }
                }

                $data[] = $row;
            }
        } elseif ($area_level_id == 3) {
            $hospitals = DB::table('bd_hospital')
                ->where('province_id', $area_id)
                ->where('much_keyin', 1)
                ->orderBy('code')
                ->get();

            $result = DB::table('treatment_group')
                ->select(
                    'treatment_group.hospital_code',
                    'treatment_group.treatment_code',
                    'treatment_group.waiting_flag',
                    'treatment_group.area',
                    DB::raw('COUNT(*) as count')
                )
                ->where('treatment_group.hospital_province_code', $area_id)
                ->whereBetween('treatment_group.start_date', [$date_start, $date_end])
                ->groupBy('treatment_group.treatment_code', 'treatment_group.waiting_flag', 'treatment_group.hospital_code', 'treatment_group.area')
                ->get();

            foreach ($hospitals as $hospital) {
                $row = [
                    'name'  => $hospital->name,
                    'total' => 0,
                    'calculated' => 0,
                    'no_calculated' => 0,
                    'exsicion_total' => 0,
                    'exsicion_pass' => 0,
                    'exsicion_fail' => 0,
                    'exsicion_no' => 0,
                    'exsicion_cal' => 0,
                    'surgery_total' => 0,
                    'surgery_pass' => 0,
                    'surgery_fail' => 0,
                    'surgery_no' => 0,
                    'surgery_cal' => 0,
                    'radiation_total' => 0,
                    'radiation_pass' => 0,
                    'radiation_fail' => 0,
                    'radiation_no' => 0,
                    'radiation_cal' => 0,
                    'chemo_total' => 0,
                    'chemo_pass' => 0,
                    'chemo_fail' => 0,
                    'chemo_no' => 0,
                    'chemo_cal' => 0,
                ];

                foreach ($result as $item) {
                    if ($item->hospital_code == $hospital->code) {
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'PASS') {
                            $row['surgery_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'FAIL') {
                            $row['surgery_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '1 Surgery' && $item->waiting_flag == 'NO') {
                            $row['surgery_no'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'PASS') {
                            $row['radiation_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'FAIL') {
                            $row['radiation_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '2 Radiation' && $item->waiting_flag == 'NO') {
                            $row['radiation_no'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'PASS') {
                            $row['chemo_pass'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'FAIL') {
                            $row['chemo_fail'] = $item->count;
                        }
                        if ($item->treatment_code === '3 Chemotherapy' && $item->waiting_flag == 'NO') {
                            $row['chemo_no'] = $item->count;
                        }
                    }
                }

                $data[] = $row;
            }
        } elseif ($area_level_id == 4) {
        }

        return response()->json([
            'status' => true,
            'data' => $data
        ], 200);
    }

    function findPreviousTreatmentDate($patientRecords, $treatmentDate)
    {
        // กำหนดค่าเริ่มต้น
        $previousTreatmentDate = null;
        $source = null; // เพิ่มตัวแปรเพื่อเก็บแหล่งที่มาของข้อมูล
        $maxDays = 180;

        // แปลงวันที่เข้ารับการรักษาเป็น Carbon
        $treatmentDate = Carbon::parse($treatmentDate);

        // ค้นหาใบสรุปในโรงพยาบาลของเรา
        foreach ($patientRecords['hospital_summaries'] as $record) {
            if (isset($record->recurrent_date) && $treatmentDate->diffInDays(Carbon::parse($record->recurrent_date)) <= $maxDays) {
                $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->recurrent_date));
                $source = 'hospital_summaries (Recurrent)';
            } else if (isset($record->diagnosis_date) && $treatmentDate->diffInDays(Carbon::parse($record->diagnosis_date)) <= $maxDays) {
                $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->diagnosis_date));
                $source = 'hospital_summaries (Diagnosis)';
            } else if (isset($record->excision_in_cut_date) && $treatmentDate->diffInDays(Carbon::parse($record->excision_in_cut_date)) <= $maxDays) {
                $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->excision_in_cut_date));
                $source = 'hospital_summaries (Excision in Cut)';
            } else if (isset($record->excision_in_read_date) && $treatmentDate->diffInDays(Carbon::parse($record->excision_in_read_date)) <= $maxDays) {
                $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->excision_in_read_date));
                $source = 'hospital_summaries (Excision in Read)';
            }
        }

        // ถ้าไม่พบในโรงพยาบาลของเรา ให้ค้นหาในโรงพยาบาลอื่น
        if ($previousTreatmentDate === null) {
            foreach ($patientRecords['external_summaries'] as $record) {
                if (isset($record->recurrent_date) && $treatmentDate->diffInDays(Carbon::parse($record->recurrent_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->recurrent_date));
                    $source = 'external_summaries (Recurrent)';
                }
                if (isset($record->diagnosis_date) && $treatmentDate->diffInDays(Carbon::parse($record->diagnosis_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->diagnosis_date));
                    $source = 'external_summaries (Diagnosis)';
                }
                if (isset($record->excision_in_cut_date) && $treatmentDate->diffInDays(Carbon::parse($record->excision_in_cut_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->excision_in_cut_date));
                    $source = 'external_summaries (Excision in Cut)';
                }
                if (isset($record->excision_in_read_date) && $treatmentDate->diffInDays(Carbon::parse($record->excision_in_read_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->excision_in_read_date));
                    $source = 'external_summaries (Excision in Read)';
                }
            }
        }

        // ถ้าไม่พบใบสรุปเลย ให้ค้นหาใน Visit
        if ($previousTreatmentDate === null) {
            foreach ($patientRecords['visits'] as $visit) {
                if (isset($visit->recurrent_date) && $treatmentDate->diffInDays(Carbon::parse($visit->recurrent_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($visit->recurrent_date));
                    $source = 'visits (Recurrent)';
                }
                if (isset($visit->diagnosis_date) && $treatmentDate->diffInDays(Carbon::parse($visit->diagnosis_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($visit->diagnosis_date));
                    $source = 'visits (Diagnosis)';
                }
                if (isset($visit->excision_in_cut_date) && $treatmentDate->diffInDays(Carbon::parse($visit->excision_in_cut_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($visit->excision_in_cut_date));
                    $source = 'visits (Excision in Cut)';
                }
                if (isset($visit->excision_in_read_date) && $treatmentDate->diffInDays(Carbon::parse($visit->excision_in_read_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($visit->excision_in_read_date));
                    $source = 'visits (Excision in Read)';
                }
            }
        }

        // ถ้ายังไม่พบ ให้ใช้วันที่มาติดต่อครั้งแรก
        if ($previousTreatmentDate === null) {
            $lasted = null;
            foreach ($patientRecords['visits'] as $visit) {
                if (isset($visit->entrance_date)) {
                    if ($lasted === null) {
                        $lasted = Carbon::parse($visit->entrance_date);
                    } else {
                        $lasted = $lasted->gt(Carbon::parse($visit->entrance_date))
                            ? $lasted
                            : Carbon::parse($visit->entrance_date);
                    }
                }
            }
            $previousTreatmentDate = $lasted;
            $source = 'visits (First Visit)';
        }

        // คืนค่าทั้งวันที่และแหล่งที่มา
        return [
            'date' => $previousTreatmentDate,
            'source' => $source
        ];
    }

    // ฟังก์ชันช่วยหาวันที่ล่าสุด
    function getLatestDate($currentDate, $newDate)
    {
        if ($currentDate === null || $newDate->gt($currentDate)) {
            return $newDate;
        }
        return $currentDate;
    }

    function cancer(Request $request)
    {
        $cid = '3-1604-00391-89-2';

        $patient = DB::table('data_patient')
            ->where('cid', $cid)
            ->where('hospital_code', '10690')
            ->first();

        $treatments = DB::table('data_treatment')
            ->where('patient_id', $patient->id)
            ->where('treatment_type_id', '<>', 11)
            ->orderBy('treatment_date')
            ->get();

        $cancer_summary = DB::table('data_cancer_summary')
            ->where('patient_id', $patient->id)
            ->get();

        $cancers = DB::table('data_cancer')
            ->select('data_cancer.id', 'data_cancer.icd10_code', 'data_cancer.hospital_code')
            // ->join('data_treatment', 'data_treatment.cancer_id', 'data_cancer.id')
            ->join('data_patient', 'data_cancer.patient_id', 'data_patient.id')
            ->where('data_patient.cid', $cid)
            ->where('data_patient.hospital_code', '10690')
            // ->select('icd10_code', DB::raw('count(*) as count'))
            // ->groupBy('icd10_code')
            ->get();

        foreach ($cancers as $cancer) {
            $cancer->treatment = DB::table('data_treatment')
                ->where('cancer_id', $cancer->id)
                ->get();
        }

        return response()->json([
            'status' => true,
            'data' => $cancers
        ], 200);
    }

    public function calWaitingPatient($cid, $hospital_code)
    {
        $patient = DB::table('data_patient')
            ->select(
                'data_patient.id',
                'data_patient.cid',
                'data_patient.hospital_code',
                'data_patient.hn_no',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.sex_code',
                'data_patient.birth_date',
                'data_patient.area_code',
            )
            ->where('data_patient.cid', $cid)
            ->where('data_patient.hospital_code', $hospital_code)
            ->first();

        // summary
        $sum = DB::table('data_cancer_summary')
            ->select(
                'data_cancer_summary.id',
                'data_cancer_summary.patient_id',
                'data_cancer_summary.icd10_code',
                'data_cancer_summary.hospital_code',
                'data_cancer_summary.excision_in_cut_date',
                'data_cancer_summary.excision_in_read_date',
                'data_cancer_summary.diagnosis_date',
                'data_cancer_summary.entrance_date',
                'data_cancer_summary.first_entrance_date',
            )
            ->join('data_patient', 'data_cancer_summary.patient_id', 'data_patient.id')
            ->where('data_cancer_summary.patient_id', $patient->id)
            ->where('data_patient.cid', $patient->cid)
            ->where('data_patient.hospital_code', $patient->hospital_code)
            ->get();

        foreach ($sum as $s) {
            $data = [];
            // Excision TRT_EXCISION
            //excision_in_cut_date, excision_in_read_date, diagnosis_date
            // $excision_in_cut_date   = Carbon::parse($s->excision_in_cut_date);
            // $excision_in_read_date  = Carbon::parse($s->excision_in_read_date);
            // $diagnosis_date         = Carbon::parse($s->diagnosis_date);

            // dd($excision_in_read_date->diffInDays($excision_in_cut_date));

            $icd10_code = substr($s->icd10_code, 0, 3);
            $key_ = $s->hospital_code . '#' . $patient->cid . '#' . $icd10_code;

            $val_can_treat = [
                'key_'                      => $key_,
                'hospital_code'             => $s->hospital_code,
                'hn_no'                     => $patient->hn_no,
                'cid'                       => $patient->cid,
                'name'                      => $patient->name,
                'last_name'                 => $patient->last_name,
                'sex_code'                  => $patient->sex_code,
                'birth_date'                => date('d/m/Y', strtotime($patient->birth_date)),
                'icd10'                     => $icd10_code,
                'treatment_code'            => self::TRT_EXC,
                'trt_date'                  => date('d/m/Y', strtotime($s->excision_in_read_date)),
                'trt_date_dt'               => $s->excision_in_read_date,
                'trt_date_end'              => date('d/m/Y', strtotime($s->excision_in_read_date)),
                'trt_date_end_dt'           => $s->excision_in_read_date,
                'area'                      => $patient->area_code,
                'diagnosis_date_dt'         => $s->diagnosis_date,
                'excision_cut_date_dt'      => $s->excision_in_cut_date,
                'excision_read_date_dt'     => $s->excision_in_read_date,
                'entrance_date_dt'          => $s->entrance_date,
            ];

            $data[] = $val_can_treat;

            $treatments = DB::table('data_cancer_summary_treatment')
                ->where('cancer_id', $s->id)
                ->where('is_prev', null)
                ->get();

            foreach ($treatments as $treatment) {
                $val_can_treat = [
                    'key_'                      => $key_,
                    'hospital_code'             => $s->hospital_code,
                    'hn_no'                     => $patient->hn_no,
                    'cid'                       => $patient->cid,
                    'name'                      => $patient->name,
                    'last_name'                 => $patient->last_name,
                    'sex_code'                  => $patient->sex_code,
                    'birth_date'                => date('d/m/Y', strtotime($patient->birth_date)),
                    'icd10'                     => $icd10_code,
                    'treatment_code'            => $treatment->treatment_code,
                    'trt_date'                  => date('d/m/Y', strtotime($treatment->treatment_date)),
                    'trt_date_dt'               => $treatment->treatment_date,
                    'trt_date_end'              => date('d/m/Y', strtotime($treatment->treatment_date_end)),
                    'trt_date_end_dt'           => $treatment->treatment_date_end,
                    'area'                      => $patient->area_code,
                    'diagnosis_date_dt'         => $s->diagnosis_date,
                    'excision_cut_date_dt'      => $s->excision_in_cut_date,
                    'excision_read_date_dt'     => $s->excision_in_read_date,
                    'entrance_date_dt'          => $s->entrance_date,
                ];

                $data[] = $val_can_treat;

                DB::table('data_treatment_group')->where('key_', $key_)->delete();

                DB::table('data_treatment_group')->insert($data);
            }
            $this->waitCid($cid);
            $this->waitting2($key_);
        }
    }

    public function newWaitingTime(Request $request)
    {
        $patients = DB::table('data_patient')
            ->select(
                'data_patient.id',
                'data_patient.cid',
                'data_patient.hospital_code',
                'data_patient.hn_no',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.sex_code',
                'data_patient.birth_date',
                'data_patient.area_code',
            )
            ->get();

        foreach ($patients as $patient) {
            // summary
            $sum = DB::table('data_cancer_summary')
                ->select(
                    'data_cancer_summary.id',
                    'data_cancer_summary.patient_id',
                    'data_cancer_summary.icd10_code',
                    'data_cancer_summary.hospital_code',
                    'data_cancer_summary.excision_in_cut_date',
                    'data_cancer_summary.excision_in_read_date',
                    'data_cancer_summary.diagnosis_date',
                    'data_cancer_summary.entrance_date',
                    'data_cancer_summary.first_entrance_date',
                )
                ->join('data_patient', 'data_cancer_summary.patient_id', 'data_patient.id')
                ->where('data_patient.cid', $patient->cid)
                ->where('data_patient.hospital_code', $patient->hospital_code)
                ->get();


            foreach ($sum as $s) {
                $data = [];
                // Excision TRT_EXCISION
                //excision_in_cut_date, excision_in_read_date, diagnosis_date
                // $excision_in_cut_date   = Carbon::parse($s->excision_in_cut_date);
                // $excision_in_read_date  = Carbon::parse($s->excision_in_read_date);
                // $diagnosis_date         = Carbon::parse($s->diagnosis_date);

                // dd($excision_in_read_date->diffInDays($excision_in_cut_date));

                $icd10_code = substr($s->icd10_code, 0, 3);
                $key_ = $s->hospital_code . '#' . $patient->cid . '#' . $icd10_code;

                $val_can_treat = [
                    'key_'                      => $key_,
                    'hospital_code'             => $s->hospital_code,
                    'hn_no'                     => $patient->hn_no,
                    'cid'                       => $patient->cid,
                    'name'                      => $patient->name,
                    'last_name'                 => $patient->last_name,
                    'sex_code'                  => $patient->sex_code,
                    'birth_date'                => date('d/m/Y', strtotime($patient->birth_date)),
                    'icd10'                     => $icd10_code,
                    'treatment_code'            => self::TRT_EXC,
                    'trt_date'                  => date('d/m/Y', strtotime($s->excision_in_read_date)),
                    'trt_date_dt'               => $s->excision_in_read_date,
                    'trt_date_end'              => date('d/m/Y', strtotime($s->excision_in_read_date)),
                    'trt_date_end_dt'           => $s->excision_in_read_date,
                    'area'                      => $patient->area_code,
                    'diagnosis_date_dt'         => $s->diagnosis_date,
                    'excision_cut_date_dt'      => $s->excision_in_cut_date,
                    'excision_read_date_dt'     => $s->excision_in_read_date,
                    'entrance_date_dt'          => $s->entrance_date,
                ];

                $data[] = $val_can_treat;

                $treatments = DB::table('data_cancer_summary_treatment')
                    ->where('cancer_id', $s->id)
                    ->where('is_prev', null)
                    ->get();

                foreach ($treatments as $treatment) {
                    $val_can_treat = [
                        'key_'                      => $key_,
                        'hospital_code'             => $s->hospital_code,
                        'hn_no'                     => $patient->hn_no,
                        'cid'                       => $patient->cid,
                        'name'                      => $patient->name,
                        'last_name'                 => $patient->last_name,
                        'sex_code'                  => $patient->sex_code,
                        'birth_date'                => date('d/m/Y', strtotime($patient->birth_date)),
                        'icd10'                     => $icd10_code,
                        'treatment_code'            => $treatment->treatment_code,
                        'trt_date'                  => date('d/m/Y', strtotime($treatment->treatment_date)),
                        'trt_date_dt'               => $treatment->treatment_date,
                        'trt_date_end'              => date('d/m/Y', strtotime($treatment->treatment_date_end)),
                        'trt_date_end_dt'           => $treatment->treatment_date_end,
                        'area'                      => $patient->area_code,
                        'diagnosis_date_dt'         => $s->diagnosis_date,
                        'excision_cut_date_dt'      => $s->excision_in_cut_date,
                        'excision_read_date_dt'     => $s->excision_in_read_date,
                        'entrance_date_dt'          => $s->entrance_date,
                    ];

                    $data[] = $val_can_treat;

                    DB::table('data_treatment_group')->where('key_', $key_)->delete();

                    DB::table('data_treatment_group')->insert($data);
                }
            }
        }

        $this->wait();
        $this->waitting2();

        return response()->json([
            'status'    => true,
            'message'   => 'บันทึกข้อมูลสำเร็จ.',
        ], 200);
    }

    public function popbase466(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $result = [];

        switch ($area_level_id) {
            case 1:
                $result = DB::table('treatment_group')
                    ->join('ref_health_regions', 'ref_health_regions.id', '=', 'treatment_group.health_region')
                    ->select(
                        'ref_health_regions.health_region_name as name',
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS exsicion_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS exsicion_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS exsicion_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS surgery_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS surgery_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS surgery_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS radiation_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS radiation_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS radiation_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS chemo_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS chemo_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS chemo_pass"),

                        // ค่าเฉลี่ยเวลารอคอยแต่ละประเภท
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_biopsy"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_surgery"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_radiation"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_chemo")
                    )
                    ->whereBetween('treatment_group.start_date', [$start_date, $end_date])
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('treatment_group.icd10_group', $icd10_group_id);
                    })
                    ->groupBy('ref_health_regions.health_region_name')
                    ->orderBy('ref_health_regions.id')
                    ->get();

                break;

            case 2:
                $result = DB::table('treatment_group')
                    ->join('ref_provinces', 'ref_provinces.id', '=', 'treatment_group.hospital_province_code')
                    ->select(
                        'ref_provinces.province_name_th as name',
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS exsicion_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS exsicion_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS exsicion_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS surgery_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS surgery_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS surgery_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS radiation_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS radiation_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS radiation_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS chemo_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS chemo_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS chemo_pass"),

                        // ค่าเฉลี่ยเวลารอคอยแต่ละประเภท
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_biopsy"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_surgery"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_radiation"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_chemo")
                    )
                    ->whereBetween('treatment_group.start_date', [$start_date, $end_date])
                    ->where('treatment_group.health_region', '=', $area_id)
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('treatment_group.icd10_group', $icd10_group_id);
                    })
                    ->groupBy('ref_provinces.province_name_th')
                    ->orderBy('ref_provinces.id')
                    ->get();

                break;

            case 3:
                $result = DB::table('treatment_group')
                    ->join('bd_hospital', 'bd_hospital.code', '=', 'treatment_group.hospital_code')
                    ->select(
                        'bd_hospital.name as name',
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS exsicion_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS exsicion_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS exsicion_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS surgery_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS surgery_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS surgery_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS radiation_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS radiation_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS radiation_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS chemo_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS chemo_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS chemo_pass"),

                        // ค่าเฉลี่ยเวลารอคอยแต่ละประเภท
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_biopsy"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_surgery"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_radiation"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_chemo")
                    )
                    ->whereBetween('treatment_group.start_date', [$start_date, $end_date])
                    ->where('treatment_group.hospital_province_code', '=', $area_id)
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('treatment_group.icd10_group', $icd10_group_id);
                    })
                    ->groupBy('bd_hospital.name')
                    ->orderBy('bd_hospital.code')
                    ->get();

                break;

            case 4:
                $result = DB::table('treatment_group')
                    ->join('bd_hospital', 'bd_hospital.code', '=', 'treatment_group.hospital_code')
                    ->select(
                        'bd_hospital.name as name',
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS exsicion_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS exsicion_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS exsicion_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS surgery_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS surgery_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS surgery_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS radiation_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS radiation_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS radiation_pass"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'FAIL' THEN 1 END) AS chemo_fail"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'NO' THEN 1 END) AS chemo_no"),
                        DB::raw("COUNT(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag = 'PASS' THEN 1 END) AS chemo_pass"),

                        // ค่าเฉลี่ยเวลารอคอยแต่ละประเภท
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '0 Biopsy' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_biopsy"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '1 Surgery' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_surgery"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '2 Radiation' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_radiation"),
                        DB::raw("ROUND(AVG(CASE WHEN treatment_group.treatment_code = '3 Chemotherapy' AND treatment_group.waiting_flag IN ('PASS', 'FAIL') THEN waiting_days END), 2) AS avg_waiting_chemo")
                    )
                    ->whereBetween('treatment_group.start_date', [$start_date, $end_date])
                    ->where('treatment_group.hospital_code', '=', $area_id)
                    ->when(sizeof($icd10_group_id), function ($query) use ($icd10_group_id) {
                        return $query->whereIn('treatment_group.icd10_group', $icd10_group_id);
                    })
                    ->groupBy('bd_hospital.name')
                    ->orderBy('bd_hospital.code')
                    ->get();

                break;

            default:
                # code...
                break;
        }

        return response()->json([
            'data' => $result
        ], 200, []);
    }

    public function test(Request $request)
    {
        DB::table('data_treatment_group')->delete();

        DB::table('data_cancer')
            ->join('data_patient', 'data_cancer.patient_id', '=', 'data_patient.id')
            ->selectRaw("
            CONCAT(data_cancer.hospital_code, '#', data_patient.cid, '#', SUBSTRING(data_cancer.icd10_code, 1, 3)) AS key_,
            data_cancer.hospital_code,
            data_patient.hn_no,
            data_patient.cid,
            data_patient.name,
            data_patient.last_name,
            data_patient.sex_code,
            data_patient.birth_date,
            SUBSTRING(data_cancer.icd10_code, 1, 3) AS icd10,
            data_cancer.excision_in_read_date,
            data_cancer.excision_in_cut_date,
            data_patient.area_code AS area,
            data_cancer.diagnosis_date,
            data_cancer.entrance_date")
            ->whereNotNull('data_cancer.diagnosis_date')
            ->whereNotNull('data_cancer.excision_in_cut_date')
            ->whereNotNull('data_cancer.excision_in_read_date')
            ->whereNotNull('data_cancer.icd10_code')
            ->whereNot('data_patient.cid', '0-0000-00000-00-0')
            ->whereNot('data_patient.cid', '9-9999-99999-99-9')
            // ->where('data_patient.cid', '3-4512-00117-91-4')
            // ->groupBy(
            //     'data_cancer.hospital_code',
            //     'data_patient.cid',
            //     'data_cancer.icd10_code',
            //     'data_patient.hn_no',
            //     'data_patient.name',
            //     'data_patient.last_name',
            //     'data_patient.sex_code',
            //     'data_patient.birth_date',
            //     'data_patient.area_code'
            // )
            ->orderBy('key_')
            ->chunk(500, function ($results) {
                $data = [];

                foreach ($results as $value) {
                    if ($value->excision_in_cut_date) {
                        $data[] = [
                            'key_' => $value->key_,
                            'hospital_code' => $value->hospital_code,
                            'hn_no' => $value->hn_no,
                            'cid' => $value->cid,
                            'name' => $value->name,
                            'last_name' => $value->last_name,
                            'sex_code' => $value->sex_code,
                            'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                            'icd10' => $value->icd10,
                            'treatment_code' => 'CUT',
                            'trt_date' => date('d/m/Y', strtotime($value->excision_in_cut_date)),
                            'trt_date_dt' => $value->excision_in_cut_date,
                            'trt_date_end' => null,
                            'trt_date_end_dt' => null,
                            'area' => $value->area,
                            'diagnosis_date_dt' => null,
                            'excision_cut_date_dt' => null,
                            'excision_read_date_dt' => null,
                            'entrance_date_dt' => null,
                        ];
                    }

                    if ($value->excision_in_read_date) {
                        $data[] = [
                            'key_' => $value->key_,
                            'hospital_code' => $value->hospital_code,
                            'hn_no' => $value->hn_no,
                            'cid' => $value->cid,
                            'name' => $value->name,
                            'last_name' => $value->last_name,
                            'sex_code' => $value->sex_code,
                            'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                            'icd10' => $value->icd10,
                            'treatment_code' => 'READ',
                            'trt_date' => date('d/m/Y', strtotime($value->excision_in_read_date)),
                            'trt_date_dt' => $value->excision_in_read_date,
                            'trt_date_end' => null,
                            'trt_date_end_dt' => null,
                            'area' => $value->area,
                            'diagnosis_date_dt' => null,
                            'excision_cut_date_dt' => null,
                            'excision_read_date_dt' => null,
                            'entrance_date_dt' => null,
                        ];
                    }

                    if ($value->diagnosis_date) {
                        $data[] = [
                            'key_' => $value->key_,
                            'hospital_code' => $value->hospital_code,
                            'hn_no' => $value->hn_no,
                            'cid' => $value->cid,
                            'name' => $value->name,
                            'last_name' => $value->last_name,
                            'sex_code' => $value->sex_code,
                            'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                            'icd10' => $value->icd10,
                            'treatment_code' => 'DIAG',
                            'trt_date' => date('d/m/Y', strtotime($value->diagnosis_date)),
                            'trt_date_dt' => $value->diagnosis_date,
                            'trt_date_end' => null,
                            'trt_date_end_dt' => null,
                            'area' => $value->area,
                            'diagnosis_date_dt' => null,
                            'excision_cut_date_dt' => null,
                            'excision_read_date_dt' => null,
                            'entrance_date_dt' => null,
                        ];
                    }

                    if ($value->entrance_date) {
                        $data[] = [
                            'key_' => $value->key_,
                            'hospital_code' => $value->hospital_code,
                            'hn_no' => $value->hn_no,
                            'cid' => $value->cid,
                            'name' => $value->name,
                            'last_name' => $value->last_name,
                            'sex_code' => $value->sex_code,
                            'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                            'icd10' => $value->icd10,
                            'treatment_code' => 'VISIT',
                            'trt_date' => date('d/m/Y', strtotime($value->entrance_date)),
                            'trt_date_dt' => $value->entrance_date,
                            'trt_date_end' => null,
                            'trt_date_end_dt' => null,
                            'area' => $value->area,
                            'diagnosis_date_dt' => null,
                            'excision_cut_date_dt' => null,
                            'excision_read_date_dt' => null,
                            'entrance_date_dt' => null,
                        ];
                    }
                }

                $uniqueData = collect($data)->unique()->values()->all();

                DB::table('data_treatment_group')->insert($uniqueData);
            });
    }
}
