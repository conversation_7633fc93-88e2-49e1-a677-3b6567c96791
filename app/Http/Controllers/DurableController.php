<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Exception;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DurableController extends Controller
{
    public function storeOrUpdate(Request $request)
    {
        $user = Auth::user();
        $input = $request->all();

        // ดึงค่าที่ต้องการตรวจสอบ
        $val_durable = $this->getValDurable($request);

        if (empty($val_durable['hospital_code'])) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบรหัสสถานพยาบาลที่ระบุ กรุณาเลือกจากตัวเลือกเท่านั้น!'
            ], 422);
        }

        try {
            // ค้นหาข้อมูลในตาราง `prop_durables` ตาม year_id และ hospital_code
            $existingRecord = DB::table('prop_durables')
                ->where('year_id', $val_durable['year_id'])
                ->where('hospital_code', $val_durable['hospital_code'])
                ->first();

            if ($existingRecord) {
                // ถ้าพบข้อมูล → อัปเดต
                $val_durable['updated_at'] = now()->format('Y-m-d H:i:s');
                $val_durable['updated_by'] = $user->id;

                DB::table('prop_durables')
                    ->where('id', $existingRecord->id)
                    ->update($val_durable);

                return response()->json([
                    'status' => true,
                    'message' => 'แก้ไขข้อมูลสำเร็จ.',
                    'id' => $existingRecord->id
                ], 200);
            } else {
                // ถ้าไม่พบข้อมูล → เพิ่มใหม่
                $val_durable['created_at'] = now()->format('Y-m-d H:i:s');
                $val_durable['created_by'] = $user->id;
                $val_durable['updated_at'] = now()->format('Y-m-d H:i:s');
                $val_durable['updated_by'] = $user->id;

                $newId = DB::table('prop_durables')->insertGetId($val_durable);

                return response()->json([
                    'status' => true,
                    'message' => 'บันทึกข้อมูลใหม่สำเร็จ.',
                    'id' => $newId
                ], 201);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }


    public function show($id)
    {
        $item = DB::table('prop_durables')
            ->where('prop_durables.id', $id)
            ->first();

        $hospital = DB::table('bd_hospital')
            ->select(['code', 'name'])
            ->where('code', $item->hospital_code)
            ->first();

        $item->hospital_name_text = $hospital->code . ' ' . $hospital->name;

        return response()->json($item, 200, []);
    }

    public function destroy(string $id)
    {
        DB::table('prop_durables')->where('id', $id)->delete();

        return response()->json([
            'status' => true,
            'message' => 'ลบข้อมูลสำเร็จ.'
        ], 200);
    }

    public function getReport(Request $request)
    {
        $year_id = $request->query('year_id');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $fields = [
            (object)['name' => 'f_1_1', 'field' => DB::raw('sum(prop_durables.f_1_1) as f_1_1'), 'label' => '1.1 xxx'],
            (object)['name' => 'f_2_1', 'field' => DB::raw('sum(prop_durables.f_2_1) as f_2_1'), 'label' => '2.1 xxx'],
            (object)['name' => 'f_2_2', 'field' => DB::raw('sum(prop_durables.f_2_2) as f_2_2'), 'label' => '2.2 xxx'],
            (object)['name' => 'f_2_3', 'field' => DB::raw('sum(prop_durables.f_2_3) as f_2_3'), 'label' => '2.3 xxx'],
            (object)['name' => 'f_2_4', 'field' => DB::raw('sum(prop_durables.f_2_4) as f_2_4'), 'label' => '2.4 xxx'],
            (object)['name' => 'f_2_5', 'field' => DB::raw('sum(prop_durables.f_2_5) as f_2_5'), 'label' => '2.5 xxx'],
            (object)['name' => 'f_2_6', 'field' => DB::raw('sum(prop_durables.f_2_6) as f_2_6'), 'label' => '2.6 xxx'],
            (object)['name' => 'f_2_7', 'field' => DB::raw('sum(prop_durables.f_2_7) as f_2_7'), 'label' => '2.7 xxx'],
            (object)['name' => 'f_2_8', 'field' => DB::raw('sum(prop_durables.f_2_8) as f_2_8'), 'label' => '2.8 xxx'],
            (object)['name' => 'f_2_9', 'field' => DB::raw('sum(prop_durables.f_2_9) as f_2_9'), 'label' => '2.9 xxx'],
            (object)['name' => 'f_2_10', 'field' => DB::raw('sum(prop_durables.f_2_10) as f_2_10'), 'label' => '2.10 xxx'],
            (object)['name' => 'f_2_11', 'field' => DB::raw('sum(prop_durables.f_2_11) as f_2_11'), 'label' => '2.11 xxx'],
            (object)['name' => 'f_2_12', 'field' => DB::raw('sum(prop_durables.f_2_12) as f_2_12'), 'label' => '2.12 xxx'],
            (object)['name' => 'f_3_1', 'field' => DB::raw('sum(prop_durables.f_3_1) as f_3_1'), 'label' => '3.1 xxx'],
            (object)['name' => 'f_3_2', 'field' => DB::raw('sum(prop_durables.f_3_2) as f_3_2'), 'label' => '3.2 xxx'],
            (object)['name' => 'f_3_3', 'field' => DB::raw('sum(prop_durables.f_3_3) as f_3_3'), 'label' => '3.3 xxx'],
            (object)['name' => 'f_3_4', 'field' => DB::raw('sum(prop_durables.f_3_4) as f_3_4'), 'label' => '3.4 xxx'],
            (object)['name' => 'f_4_1', 'field' => DB::raw('sum(prop_durables.f_4_1) as f_4_1'), 'label' => '4.1 xxx'],
            (object)['name' => 'f_4_2', 'field' => DB::raw('sum(prop_durables.f_4_2) as f_4_2'), 'label' => '4.2 xxx'],
            (object)['name' => 'f_4_3', 'field' => DB::raw('sum(prop_durables.f_4_3) as f_4_3'), 'label' => '4.3 xxx'],
            (object)['name' => 'f_4_4', 'field' => DB::raw('sum(prop_durables.f_4_4) as f_4_4'), 'label' => '4.4 xxx'],
            (object)['name' => 'f_4_5', 'field' => DB::raw('sum(prop_durables.f_4_5) as f_4_5'), 'label' => '4.5 xxx'],
            (object)['name' => 'f_4_6', 'field' => DB::raw('sum(prop_durables.f_4_6) as f_4_6'), 'label' => '4.6 xxx'],
        ];
        $data_table = null;
        switch ($area_level_id) {
            case 1:
                $data_table = DB::table('ref_health_regions')
                    ->select(array_merge(
                        [
                            DB::raw('ref_health_regions.id as id'),
                            DB::raw("concat('เขต ', ref_health_regions.id) as name"),
                        ],
                        array_map(fn($d) => $d->field, $fields)
                    ))
                    ->leftJoin('bd_hospital', 'ref_health_regions.id', '=', 'bd_hospital.health_region_id')
                    ->leftJoin(
                        'prop_durables',
                        function (JoinClause $join) use ($year_id) {
                            $join->on('prop_durables.hospital_code', '=', 'bd_hospital.code')
                                ->where('prop_durables.year_id', $year_id);
                        }
                    )
                    ->groupBy('ref_health_regions.id', DB::raw("concat('เขต ', ref_health_regions.id)"))
                    ->orderBy('ref_health_regions.id')
                    ->get();
                break;

            case 2:
            case 3:
                $data_table = DB::table('prop_durables')
                    ->select(array_merge(
                        [
                            DB::raw('bd_hospital.code as id'),
                            DB::raw("bd_hospital.name as name"),
                        ],
                        array_map(fn($d) => $d->field, $fields)
                    ))
                    ->leftJoin('bd_hospital', 'prop_durables.hospital_code', '=', 'bd_hospital.code')
                    ->where(function ($q) use ($area_level_id, $year_id, $area_id) {
                        if ($area_level_id == 2) {
                            $q->where('bd_hospital.health_region_id', $area_id)->where('prop_durables.year_id', $year_id);
                        } else if ($area_level_id == 3) {
                            $q->where('bd_hospital.province_id', $area_id)->where('prop_durables.year_id', $year_id);
                        }
                    })
                    ->groupBy('bd_hospital.code', 'bd_hospital.name')
                    ->orderBy('bd_hospital.code')
                    ->get();
                break;

            default:
                # code...
                break;
        }

        if (is_null($year_id)) {
            return response()->json([
                'data' => [],
            ]);
        }
    
        return response()->json([
            'data' => $data_table,
        ]);
  }

    public function getValDurable(Request $request)
    {
        $input = $request->all();
        $hospital_code = substr(trim($input['hospital_name_text']), 0, 5);
        $hospitals = DB::table('bd_hospital')->where('code', $hospital_code)->first();

        $val = [
            'hospital_code' => $hospitals ? $hospital_code : '',
            'year_id'       => $input['year_id'],
            'f_1_1'         => $input['f_1_1'] != '' ? $input['f_1_1'] : null,
            'f_2_1'         => $input['f_2_1'] != '' ? $input['f_2_1'] : null,
            'f_2_2'         => $input['f_2_2'] != '' ? $input['f_2_2'] : null,
            'f_2_3'         => $input['f_2_3'] != '' ? $input['f_2_3'] : null,
            'f_2_4'         => $input['f_2_4'] != '' ? $input['f_2_4'] : null,
            'f_2_5'         => $input['f_2_5'] != '' ? $input['f_2_5'] : null,
            'f_2_6'         => $input['f_2_6'] != '' ? $input['f_2_6'] : null,
            'f_2_7'         => $input['f_2_7'] != '' ? $input['f_2_7'] : null,
            'f_2_8'         => $input['f_2_8'] != '' ? $input['f_2_8'] : null,
            'f_2_9'         => $input['f_2_9'] != '' ? $input['f_2_9'] : null,
            'f_2_10'        => $input['f_2_10'] != '' ? $input['f_2_10'] : null,
            'f_2_11'        => $input['f_2_11'] != '' ? $input['f_2_11'] : null,
            'f_2_12'        => $input['f_2_12'] != '' ? $input['f_2_12'] : null,
            'f_3_1'         => $input['f_3_1'] != '' ? $input['f_3_1'] : null,
            'f_3_2'         => $input['f_3_2'] != '' ? $input['f_3_2'] : null,
            'f_3_3'         => $input['f_3_3'] != '' ? $input['f_3_3'] : null,
            'f_3_4'         => $input['f_3_4'] != '' ? $input['f_3_4'] : null,
            'f_4_1'         => $input['f_4_1'] != '' ? $input['f_4_1'] : null,
            'f_4_2'         => $input['f_4_2'] != '' ? $input['f_4_2'] : null,
            'f_4_3'         => $input['f_4_3'] != '' ? $input['f_4_3'] : null,
            'f_4_4'         => $input['f_4_4'] != '' ? $input['f_4_4'] : null,
            'f_4_5'         => $input['f_4_5'] != '' ? $input['f_4_5'] : null,
            'f_4_6'         => $input['f_4_6'] != '' ? $input['f_4_6'] : null,
        ];

        return $val;
    }

    public function getDurable(Request $request)
    {
        $input = $request->all();
        $dataset = DB::table('prop_durables')
            ->select([
                'prop_durables.id',
                'prop_durables.year_id',
                'prop_durables.hospital_code',
                DB::raw('bd_hospital.name as hospital_name'),
                DB::raw('ref_health_regions.health_region_name'),
                DB::raw("concat('จ.', ref_provinces.province_name_th) as province_name"),
                'prop_durables.created_at',
                'prop_durables.updated_at',
                'prop_durables.f_1_1',
                'prop_durables.f_2_1',
                'prop_durables.f_2_2',
                'prop_durables.f_2_3',
                'prop_durables.f_2_4',
                'prop_durables.f_2_5',
                'prop_durables.f_2_6',
                'prop_durables.f_2_7',
                'prop_durables.f_2_8',
                'prop_durables.f_2_9',
                'prop_durables.f_2_10',
                'prop_durables.f_2_11',
                'prop_durables.f_2_12',
                'prop_durables.f_3_1',
                'prop_durables.f_3_2',
                'prop_durables.f_3_3',
                'prop_durables.f_3_4',
                'prop_durables.f_4_1',
                'prop_durables.f_4_2',
                'prop_durables.f_4_3',
                'prop_durables.f_4_4',
                'prop_durables.f_4_5',
                'prop_durables.f_4_6',
                DB::raw("concat(uc.name, ' ', uc.lastName) as created_by_name"),
                DB::raw("concat(uu.name, ' ', uu.lastName) as updated_by_name"),
            ])
            ->leftJoin('bd_hospital', 'prop_durables.hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('ref_health_regions', 'bd_hospital.health_region_id', '=', 'ref_health_regions.id')
            ->leftJoin('ref_provinces', 'bd_hospital.province_id', '=', 'ref_provinces.id')
            ->leftJoin(DB::raw('users uc'), 'prop_durables.created_by', '=', DB::raw('uc.id'))
            ->leftJoin(DB::raw('users uu'), 'prop_durables.updated_by', '=', DB::raw('uu.id'))
            ->orderBy('prop_durables.year_id', 'desc')
            ->orderBy('bd_hospital.health_region_id')
            ->orderBy('bd_hospital.province_id')
            ->orderBy('prop_durables.hospital_code')
            ->get();

        return [
            'dataset' => $dataset
        ];
    }

    public function equipment(Request $request)
    {
        $year_id = $request->query('year_id');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');

        $fields = [
            (object)['field' => DB::raw('sum(prop_durables.f_1_1) as f_1_1')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_1) as f_2_1')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_2) as f_2_2')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_3) as f_2_3')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_4) as f_2_4')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_5) as f_2_5')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_6) as f_2_6')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_7) as f_2_7')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_8) as f_2_8')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_9) as f_2_9')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_10) as f_2_10')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_11) as f_2_11')],
            (object)['field' => DB::raw('sum(prop_durables.f_2_12) as f_2_12')],
            (object)['field' => DB::raw('sum(prop_durables.f_3_1) as f_3_1')],
            (object)['field' => DB::raw('sum(prop_durables.f_3_2) as f_3_2')],
            (object)['field' => DB::raw('sum(prop_durables.f_3_3) as f_3_3')],
            (object)['field' => DB::raw('sum(prop_durables.f_3_4) as f_3_4')],
            (object)['field' => DB::raw('sum(prop_durables.f_4_1) as f_4_1')],
            (object)['field' => DB::raw('sum(prop_durables.f_4_2) as f_4_2')],
            (object)['field' => DB::raw('sum(prop_durables.f_4_3) as f_4_3')],
            (object)['field' => DB::raw('sum(prop_durables.f_4_4) as f_4_4')],
            (object)['field' => DB::raw('sum(prop_durables.f_4_5) as f_4_5')],
            (object)['field' => DB::raw('sum(prop_durables.f_4_6) as f_4_6')],
        ];

        $data_table = null;
        switch ($area_level_id) {
            case 1:
                case 2:
                    case 3:
               
                $data_table  = DB::table('ref_health_regions')
                ->select(array_merge(
                    [
                        DB::raw('ref_health_regions.health_region_name'),
                        DB::raw('bd_health_region.province'),
                        DB::raw('bd_hospital.code AS hospital_code'),
                        DB::raw('bd_hospital.name AS hospital_name'),
                        DB::raw('prop_durables.year_id'),
                    ],
                    array_map(fn($d) => $d->field, $fields),
                    [
                        DB::raw('prop_durables.created_at AS created_at'),
                        DB::raw('users.name AS created_by_user'),
                        DB::raw('prop_durables.updated_at AS updated_at'),
                        DB::raw('users.name AS updated_by_user'),
                    ]
                ))
                ->leftJoin('bd_hospital', 'ref_health_regions.id', '=', 'bd_hospital.health_region_id')
                ->where('bd_hospital.much_keyin', '=', 1)
                ->leftJoin('bd_health_region', 'bd_hospital.province_id', '=', 'bd_health_region.provine_code')
                ->leftJoin('prop_durables', function (JoinClause $join) use ($year_id) {
                    $join->on('prop_durables.hospital_code', '=', 'bd_hospital.code')
                        ->where('prop_durables.year_id', $year_id);
                })
                ->leftJoin('users', 'prop_durables.created_by', '=', 'users.id')
                ->when($area_level_id == 2, function ($query) use ($area_id) { // เงื่อนไขระดับจังหวัด
                    return $query->where('bd_hospital.health_region_id', '=', $area_id);
                })
                ->when($area_level_id == 3, function ($query) use ($area_id) { // เงื่อนไขระดับอำเภอ
                    return $query->where('bd_hospital.province_id', '=', $area_id);
                })
                ->groupBy(
                    'ref_health_regions.id',
                    'ref_health_regions.health_region_name',
                    'bd_health_region.province',
                    'bd_hospital.code',
                    'bd_hospital.name',
                    'prop_durables.year_id',
                    'users.name',
                    'prop_durables.created_at',
                    'prop_durables.updated_at'
                )
                ->orderBy('ref_health_regions.id', 'ASC')
                ->orderBy('bd_health_region.provine_code', 'ASC')
                ->get();
            break;

        default:
            return response()->json(['error' => 'Invalid area_level_id'], 400);
    }

    if (is_null($year_id)) {
        return response()->json([
            'data' => [],
        ]);
    }

    return response()->json([
        'data' => $data_table,
    ]);
    }
}
