<?php

namespace App\Http\Controllers;

use Exception;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CancerSummaryController extends Controller
{
    public static function show($id)
    {
        $cancer = DB::table('data_cancer_summary')->select([
            'id',
            'data_cancer_summary.hospital_code',
            DB::raw("bd_hospital.name as hospital_name"),
            'patient_id',
            'entrance_date',
            'first_entrance_date',
            'finance_support_code',
            'finance_support_text',
            'recurrent',
            'recurrent_date',
            'diagnosis_code',
            'diagnosis_text',
            'diagnosis_out',
            'topo_code',
            'topo_text',
            'morphology_code',
            'morphology_text',
            'behaviour_code',
            'behaviour_text',
            'grade_code',
            'grade_text',
            'stage_code',
            't_code',
            'n_code',
            'm_code',
            'tnm_date',
            'biopsy_sent_date',
            'extension_code',
            'metastasis',
            'metastasis_organ',
            'lifestatus_code',
            'last_seen',
            'death_date',
            'deathcause_code',
            'txt_clinical_sammary',
            'txt_patho',
            'txt_lab',
            'txt_xray',
            'create_by',
            'create_date',
            'last_update_by',
            'last_update_date',
            'diagnosis_date',
            'age',
            'excision_in_num',
            'excision_in_cut_date',
            'excision_in_read_date',
            'excision_out_num',
            'excision_out_cut_date',
            'excision_out_read_date',
            'recurent_code',
            'laterality_code',
            'contact_last_date',
            'refer_from_hos_code',
            'refer_from_date',
            'refer_from_hn',
            'refer_from_detail',
            'refer_to_hos_code',
            'refer_to_date',
            'refer_to_hn',
            'refer_to_detail',
            'icd10_code',
            'icd10_text',
            'met_1',
            'met_1_date',
            'met_2',
            'met_2_date',
            'met_3',
            'met_3_date',
            'met_4',
            'met_4_date',
            'met_5',
            'met_5_date',
            'met_6',
            'met_6_date',
            'met_7',
            'met_7_date',
            'met_8',
            DB::raw("null as metastasis"),
            DB::raw("null as treatments"),
            DB::raw("null as prev_treatments"),
            DB::raw("null as files"),
        ])
            ->join('bd_hospital', 'data_cancer_summary.hospital_code', '=', 'bd_hospital.code')
            ->where('id', $id)
            ->first();

        // ✅ ตรวจสอบก่อนใช้งาน
        if (!$cancer) {
            return response()->json(['message' => 'Data not found', $cancer], 404);
        }

        $cancer->treatments = [];
        $cancer->prev_treatments = [];
        $cancer->files = [];

        $treatments = DB::table('data_cancer_summary_treatment')
            // ->where('patient_id', $cancer->patient_id)
            ->where('cancer_id', $cancer->id)
            // ->where('is_prev', null)

            // ->orderBy('treatment_date', 'asc')
            ->get();

        foreach ($treatments as $treat) {
            $cancer->treatments[] = $treat;
        }

        // $prev_treatments = DB::table('data_cancer_summary_treatment')
        //     // ->where('patient_id', $cancer->patient_id)
        //     ->where('cancer_id', $cancer->id)
        //     // ->where('is_prev', 1)
        //     // ->orderBy('treatment_date', 'asc')
        //     ->get();

        // foreach ($prev_treatments as $treat) {
        //     $cancer->prev_treatments[] = $treat;
        // }

        $files = DB::table('data_cancer_summary_file')
            ->where('patient_id', $cancer->patient_id)
            ->orderBy('id', 'asc')
            ->get();

        foreach ($files as $file) {
            $cancer->files[] = $file;
        }

        return response()->json($cancer, 200, [], JSON_NUMERIC_CHECK);
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        $input = $request->all();

        if (isset($input['morphology_text']) && $input['morphology_text'] != '') {
            $mor = DB::table('bd_mor')->where(DB::raw("concat(code, ' ', name)"), $input['morphology_text'])->first();
            if ($mor) {
                $mor_key = $mor->key;
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Morphology ไม่ถูกต้อง กรุณาเลือกจากตัวเลือกเท่านั้น'
                ], 422);
            }
        } else {
            $mor_key = null;
        }

        try {
            $val_cancer = $this->getValCancer($input, $mor_key);
            $val_cancer['first_entrance_date'] = $input['first_entrance_date'] != '' ? getDateTimeTHtoEN($input['first_entrance_date']) : null;
            $val_cancer['entrance_date'] = Carbon::now()->format('Y-m-d');
            $val_cancer['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
            $val_cancer['updated_by'] = $user->id;

            // 1. อัปเดตหรือเพิ่ม cancer summary
            DB::table('data_cancer_summary')->updateOrInsert(
                [
                    'patient_id' => $val_cancer['patient_id'],
                    'topo_id' => $val_cancer['topo_id']
                ],
                $val_cancer
            );

            // สร้าง Reg ใหม่
            $val_cancer['user_cancer'] = 1;
            $val_cancer['source_id'] = 1;
            $cancer_id = DB::table('data_cancer')->insertGetId($val_cancer);

            if (isset($input['treatmentsn']) && sizeof($input['treatmentsn']) > 0) {
                $treatments = $input['treatmentsn'];
                foreach ($treatments as $treatment) {
                    DB::table('data_treatment')->insert([
                        'patient_id'            => $treatment['patient_id'],
                        'cancer_id'             => $cancer_id, // ✅ ใส่ id ที่ได้
                        'treatment_code'        => $treatment['treatment_code'],
                        'treatment_date'        => $treatment['treatment_date'] != null ? getDateTimeTHtoEN($treatment['treatment_date']) : null,
                        'treatment_date_end'    => $treatment['treatment_date_end'] != null ? getDateTimeTHtoEN($treatment['treatment_date_end']) : null,
                        'none_protocol'         => $treatment['none_protocol'],
                        'none_protocol_note'    => $treatment['none_protocol_note'],
                        'note'                  => $treatment['note']
                    ]);
                }
            }

            // 2. ดึง cancer summary ที่อัปเดตล่าสุด เพื่อใช้ id
            $cancer_summary = DB::table('data_cancer_summary')
                ->where('patient_id', $val_cancer['patient_id'])
                ->where('topo_id', $val_cancer['topo_id'])
                ->latest('updated_at') // ถ้ามี field updated_at
                ->first();

            // 3. ลบ treatment เดิม
            DB::table('data_cancer_summary_treatment')
                ->where('cancer_id', $cancer_summary->id)
                ->delete();

            // 4. เพิ่ม treatment ใหม่ พร้อม cancer_id
            if (isset($input['treatmentsn']) && sizeof($input['treatmentsn']) > 0) {
                $treatments = $input['treatmentsn'];
                foreach ($treatments as $treatment) {
                    DB::table('data_cancer_summary_treatment')->insert([
                        'patient_id'            => $treatment['patient_id'],
                        'cancer_id'             => $cancer_summary->id, // ✅ ใส่ id ที่ได้
                        'treatment_code'        => $treatment['treatment_code'],
                        'treatment_date'        => $treatment['treatment_date'] != null ? getDateTimeTHtoEN($treatment['treatment_date']) : null,
                        'treatment_date_end'    => $treatment['treatment_date_end'] != null ? getDateTimeTHtoEN($treatment['treatment_date_end']) : null,
                        'none_protocol'         => $treatment['none_protocol'],
                        'none_protocol_note'    => $treatment['none_protocol_note'],
                        'note'                  => $treatment['note']
                    ]);
                }
            }

            // Files
            if (isset($input['curr_files']) && sizeof($input['curr_files']) > 0) {
                foreach ($input['curr_files'] as $idx => $val_file) {
                    if ($request->hasFile('curr_files.' . $idx . '.file')) {
                        $file           = $request->file('curr_files.' . $idx . '.file');
                        $file_size      = $file->getSize();
                        $store_path     = "upload/files/" . Carbon::now()->format('Ymd');
                        $name           = md5(uniqid(rand(), true)) . str_replace(' ', '-', $file->getClientOriginalName());
                        $file->move(public_path('/' . $store_path), $name);

                        $val_file['file_path']      = $store_path . '/' . $name;
                        $val_file['file_name']      = $file->getClientOriginalName();
                        $val_file['file_size']      = $file_size;
                        $val_file['patient_id']     = $treatment['patient_id'];
                        $val_file['cancer_id']      = $cancer_summary->id;
                        $val_file['updated_at']     = Carbon::now()->format('Y-m-d H:i:s');
                        $val_file['updated_by']     = $user->id;
                        $val_file['created_at']     = Carbon::now()->format('Y-m-d H:i:s');
                        $val_file['created_by']     = $user->id;

                        unset($val_file['file']);
                        unset($val_file['type']);

                        DB::table('data_cancer_summary_file')->insert($val_file);
                    }
                }
            }

            DB::table('data_cancer')->where('patient_id', $val_cancer['patient_id'])
                ->where('icd10_code', $val_cancer['icd10_code'])
                ->update(['user_cancer' => 1]);

            // กำหนดว่า Patient นี้ผ่านการอนุมัติแล้ว
            DB::table('data_patient')->where('id', $val_cancer['patient_id'])->update(['checked_api' => 1]);
            
        } catch (Exception $e) {
            Log::error('CancerSum : ' . $e);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        return response()->json([
            'status'            => true,
            'message'           => 'บันทึกข้อมูลสำเร็จ.',
        ], 200);
    }

    private function getValCancer($input, $mor_key)
    {
        $patient = DB::table('data_patient')->where('id', $input['patient_id'])->first();

        if (isset($input['topo_text'])) {
            Log::info('CancerSum : topo_text: ' . $input['topo_text']);
        }

        $topo_code  = $this->getCodeFromText($input, 'topo_text', 'bd_topo');
        $topo_id    = $topo_code ? substr($topo_code, 0, 2) : null;
        $icd10_code = isset($input['icd10_code']) ? str_replace('.', '', strtoupper($input['icd10_code']))  : null;
        $icd10_text = $icd10_code ? $this->getIcd10Text($icd10_code) : null;
        $icd10_group_id = $icd10_code ? $this->getIcd10group($icd10_code) : null;

        $val = [
            'hospital_code'             => $patient->hospital_code,
            'patient_id'                => $patient->id,
            'finance_support_code'      => $this->getCodeFromText($input, 'finance_support_text', 'bd_finance_support'),  // isset($input['finance_support_text']) ? substr($input['finance_support_text'], 0, strpos($input['finance_support_text'], ' ')) : null,
            'finance_support_text'      => isset($input['finance_support_text']) ? $input['finance_support_text'] : null,
            'diagnosis_date'            => $input['diagnosis_date'] != '' ? getDateTimeTHtoEN($input['diagnosis_date']) : null,
            'diagnosis_code'            => isset($input['diagnosis_text']) ? (substr($input['diagnosis_text'], 0, 1) ?: null) : null,
            'diagnosis_text'            => isset($input['diagnosis_text']) ? $input['diagnosis_text'] : null,
            'diagnosis_out'             => isset($input['diagnosis_out']) ? $input['diagnosis_out'] : 0,
            'excision_in_cut_date'      => isset($input['excision_in_cut_date']) && $input['excision_in_cut_date'] != '' ? getDateTimeTHtoEN($input['excision_in_cut_date']) : null,
            'excision_in_read_date'     => isset($input['excision_in_read_date']) && $input['excision_in_read_date'] != '' ? getDateTimeTHtoEN($input['excision_in_read_date']) : null,
            'topo_id'                   => $topo_id,
            'topo_code'                 => $topo_code,
            'topo_text'                 => isset($input['topo_text']) ? $input['topo_text'] : null,
            'recurrent'                 => isset($input['recurrent']) ? $input['recurrent'] : null,
            'recurrent_date'            => isset($input['recurrent_date']) && $input['recurrent_date'] != '' ? getDateTimeTHtoEN($input['recurrent_date']) : null,
            'morphology_code'           => $mor_key,
            'morphology_text'           => isset($input['morphology_text']) ? $input['morphology_text'] : null,
            'behaviour_code'            => $this->getCodeFromText($input, 'behaviour_text', 'bd_behaviour'),
            'behaviour_text'            => isset($input['behaviour_text']) ? $input['behaviour_text'] : null,
            'grade_code'                => $this->getCodeFromText($input, 'grade_text', 'bd_grade'),  // isset($input['grade_text']) ? substr($input['grade_text'], 0, strpos($input['grade_text'], ' ')) : null,
            'grade_text'                => isset($input['grade_text']) ? $input['grade_text'] : null,
            'm_code'                    => isset($input['m_code']) ? $input['m_code'] : null,
            'n_code'                    => isset($input['n_code']) ? $input['n_code'] : null,
            't_code'                    => isset($input['t_code']) ? $input['t_code'] : null,
            'tnm_date'                  => isset($input['tnm_date']) && $input['tnm_date'] != '' ? getDateTimeTHtoEN($input['tnm_date']) : null,
            'stage_code'                => isset($input['stage_code']) ? $input['stage_code'] : null,
            'extension_code'            => isset($input['extension_code']) ? $input['extension_code'] : null,
            'icd10_code'                => $icd10_code,
            'icd10_text'                => $icd10_text,
            'icd10_group_id'            => $icd10_group_id,
            'met_1'                     => isset($input['met_1']) ? $input['met_1'] : 0,
            'met_1_date'                => isset($input['met_1_date']) && $input['met_1_date'] != '' ? getDateTimeTHtoEN($input['met_1_date']) : null,
            'met_2'                     => isset($input['met_2']) ? $input['met_2'] : 0,
            'met_2_date'                => isset($input['met_2_date']) && $input['met_2_date'] != '' ? getDateTimeTHtoEN($input['met_2_date']) : null,
            'met_3'                     => isset($input['met_3']) ? $input['met_3'] : 0,
            'met_3_date'                => isset($input['met_3_date']) && $input['met_3_date'] != '' ? getDateTimeTHtoEN($input['met_3_date']) : null,
            'met_4'                     => isset($input['met_4']) ? $input['met_4'] : 0,
            'met_4_date'                => isset($input['met_4_date']) && $input['met_4_date'] != '' ? getDateTimeTHtoEN($input['met_4_date']) : null,
            'met_5'                     => isset($input['met_5']) ? $input['met_5'] : 0,
            'met_5_date'                => isset($input['met_5_date']) && $input['met_5_date'] != '' ? getDateTimeTHtoEN($input['met_5_date']) : null,
            'met_6'                     => isset($input['met_6']) ? $input['met_6'] : 0,
            'met_6_date'                => isset($input['met_6_date']) && $input['met_6_date'] != '' ? getDateTimeTHtoEN($input['met_6_date']) : null,
            'met_7'                     => isset($input['met_7']) ? $input['met_7'] : 0,
            'met_7_date'                => isset($input['met_7_date']) && $input['met_7_date'] != '' ? getDateTimeTHtoEN($input['met_7_date']) : null,
            'met_7_other'               => isset($input['met_7_other']) ? $input['met_7_other'] : null,
            'clinical_summary'          => isset($input['clinical_summary']) ? $input['clinical_summary'] : null,
        ];

        return $val;
    }

    private function getCodeFromText($input, $field_name, $table_name)
    {
        if (isset($input[$field_name]) && strlen($input[$field_name]) > 0) {
            $text       = trim($input[$field_name]);
            $position   = strpos($text, ' ');
            if ($position > 0) {
                $code = trim(substr($text, 0, $position));
            } else {
                $code = $text;
            }
            $rec = DB::table($table_name)->where('code', $code)->first();
            if ($rec) {
                return $code;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    private function getIcd10Text($icd10_code)
    {
        $icd10 = DB::table('bd_icd10')->where('ICD10', strtoupper($icd10_code))->first();
        if ($icd10) {
            return $icd10->ICD10_TEXT;
        } else {
            return null;
        }
    }

    public function getlist_ICD10(Request $request)
    {
        $patientId = $request->query('patient_id');
        if (!$patientId) {
            return response()->json(['message' => 'ID is required'], 400);
        }

        $icd10Texts = DB::table('data_cancer')
            ->distinct()
            ->select('icd10_code', 'icd10_text')
            ->where('data_cancer.patient_id', $patientId)
            ->get();

        if ($icd10Texts->isEmpty()) {
            return response()->json(['message' => 'No data found'], 404);
        }

        return response()->json([
            'data' => $icd10Texts
        ], 200);
    }
    public function get_description_ByID(Request $request)
    {
        $patient_id = $request->query('patient_id');
        $icd10_code = $request->query('icd10_code');

        if (!$patient_id || !$icd10_code) {
            return response()->json([
                'message' => 'กรุณาระบุ patient_id และ icd10_code'
            ], 400);
        }

        $cids = DB::table('data_patient')
            ->where('id', $patient_id)
            ->pluck('cid');

        if ($cids->isEmpty()) {
            return response()->json([
                'message' => 'ไม่พบข้อมูลผู้ป่วย'
            ], 404);
        }

        $patientIds = DB::table('data_patient')
            ->whereIn('cid', $cids)
            ->pluck('id');

        $data = DB::table('data_suggest')
            ->whereIn('patient_id', $patientIds)
            ->where('icd10_code', $icd10_code)
            ->orderBy('updated_at', 'desc')
            ->first();

        if (!$data) {
            return response()->json([
                'message' => 'ไม่พบข้อมูล'
            ], 404);
        }

        return response()->json([
            'data' => $data
        ], 200);
    }

    public function get_Templete(Request $request)
    {
        $ICD10 = $request->query('icd10');

        $data = DB::table('bd_template_suggest')
            ->where('icd10_code', $ICD10)
            ->select('icd10_code', 'template')
            ->get();
        return response()->json([
            'data' => $data
        ], 200);
    }
    public function advice_mobile(Request $request)
    {
        $cid = $request->query('cid');
        if (! $cid) {
            return response()->json([
                'message' => 'กรุณาระบุ เลขบัตรประชาชน'
            ], 404);
        }

        $patientId = DB::table('data_patient')
            ->where('cid', $cid)
            ->pluck('id');

        if (!$patientId) {
            return response()->json([
                'message' => 'ไม่พบข้อมูลผู้ป่วย'
            ], 404);
        }
        $data = DB::table('data_suggest')
            ->whereIn('patient_id', $patientId)
            ->orderBy('updated_at', 'desc')
            ->get();

        if (!$data) {
            return response()->json([
                'message' => 'ไม่พบข้อมูล'
            ], 404);
        }

        return response()->json([
            'data' => $data
        ], 200);
    }
    public function advice_mobileBYID(Request $request)
    {
        $id = $request->query('id');
        if (!$id) {
            return response()->json([
                'message' => 'กรุณาระบุ เลขบัตรประชาชน'
            ], 404);
        }
        $data = DB::table('data_suggest')
            ->where('id',  [$id])
            // ->orderBy('updated_at', 'desc')
            ->first();

        if (!$data) {
            return response()->json([
                'message' => 'ไม่พบข้อมูล'
            ], 404);
        }
        return response()->json([
            'data' => $data
        ], 200);
    }
    public function Appointment_list(Request $request)
    {
        $cid = $request->query('cid');
        if (! $cid) {
            return response()->json([
                'message' => 'กรุณาระบุ เลขบัตรประชาชน'
            ], 404);
        }

        $data = DB::table('data_refer as R')
            ->leftJoin('data_refer_patient as RP', 'R.id', '=', 'RP.refer_id')
            ->select('R.id', 'R.status', 'R.appoint_date', 'R.message', 'R.file')
            ->where('RP.cid', $cid)
            ->where('R.status', 2)
            ->get();

        if (!$data) {
            return response()->json([
                'message' => 'ไม่พบข้อมูล'
            ], 404);
        }

        foreach ($data as $key => $value) {
            if ($value->file) {
                $data[$key]->file = env('APP_URL') . '/' . $value->file;
            }
        }

        return response()->json([
            'data' => $data
        ], 200);
    }

    public function getIcd10group($icd10_code)
    {
        $group = DB::table('bd_sitegroup')->where('icd10_list', 'like', '%' . substr($icd10_code, 0, 3) . '%')->first();

        if (!$group) {
            return null;
        }

        return $group->group_id;
    }
}
