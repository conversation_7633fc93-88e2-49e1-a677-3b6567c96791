<?php

namespace App\Http\Controllers;

use App\Http\Controllers\OTP\OTPController;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use App\Models\User;
use Carbon\Carbon;

class AuthController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['authChangePassword', 'login', 'register', 'refresh', 'login_mobile_app', 'authLogin', 'authUpdateInfomation']]);
    }

    public function login(Request $request)
    {
        $suth_url       = 'https://canceranywhere.com/tcbplus/auth';
        $auth_username  = 'tcb_2022';
        $auth_password  = 'tcb2022HAusdf7ew7YHyqw76jhjqwe';

        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('username', 'password');

        $response = Http::withoutVerifying()
            ->withBasicAuth($auth_username, $auth_password)
            ->asForm()
            ->post($suth_url, [
                'userName' => $request->get('username'),
                'password' => $request->get('password'),
            ]);

        $body = $response->json();

        // $body = [
        //     'authStatus'            => true,
        //     'pid'                   => '',
        //     'name'                  => 'cancer001',
        //     'lastName'              => '',
        //     'hosCode'               => '88881',
        //     'hosName'               => 'workshop01',
        //     'hospitalPosition'      => '',
        //     'hospitalDepartment'    => '',
        //     'phoneNumber'           => '',
        //     'active'                => 'Y',
        //     'completePassword'      => 'Y',
        //     'completeInformation'   => 'Y',
        //     'completeOTPConfirm'    => 'Y',
        //     'roleCaw'               => 'N',
        //     'roleCancer'            => 'Y',
        //     'roleRefer'             => 'N',
        //     'roleProvince'          => 'N',
        //     'roleHealthRegion'      => 'N',
        //     'roleSuperAdmin'        => 'N',
        // ];

        if ($body['authStatus']) {
            // ****************************************************************
            if ($request->get('username') == 'user_caw') {
                $body['hosCode']            = '10666';
                $body['hosName']            = 'โรงพยาบาลมหาราชนครราชสีมา';
            }
            // Log::info($body);
            // ****************************************************************

            $user_val = [
                'username'              => $request->get('username'),
                'password'              => Hash::make($request->get('password')),
                'pid'                   => $body['pid'],
                'name'                  => $body['name'],
                'lastName'              => $body['lastName'],
                'hosCode'               => $body['hosCode'],
                'hosName'               => $body['hosName'],
                'hospitalPosition'      => $body['hospitalPosition'],
                'hospitalDepartment'    => $body['hospitalDepartment'],
                'phoneNumber'           => $body['phoneNumber'],
                'active'                => $body['active'],
                'completePassword'      => $body['completePassword'],
                'completeInformation'   => $body['completeInformation'],
                'completeOTPConfirm'    => $body['completeOTPConfirm'],
                'roleCaw'               => $body['roleCaw'],
                'roleCancer'            => $body['roleCancer'],
                'roleRefer'             => $body['roleRefer'],
                'roleProvince'          => $body['roleProvince'],
                'roleHealthRegion'      => $body['roleHealthRegion'],
                'roleSuperAdmin'        => $body['roleSuperAdmin'],
                'roleAdmin'             => 'N',
            ];

            $user_exist = User::where('username', $request->get('username'))->first();

            if ($user_exist) {
                $val = array_merge($user_val, ['updated_at' => Carbon::now()->format('Y-m-d H:i:s')]);
                $user_exist->update($val);
            } else {
                $val = array_merge($user_val, ['created_at' => Carbon::now()->format('Y-m-d H:i:s')]);
                User::insert($val);
            }
        } else {
            return response()->json([
                'status' => 'error',
                'message' => $body['authMessage'],
            ], 401);
        }

        $accessToken = Auth::attempt($credentials);
        if (!$accessToken) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized',
            ], 401);
        }

        $user           = Auth::user();
        // $refreshToken   = Auth::refresh();
        $expiresIn      = Auth::factory()->getTTL() * 60;

        $hospital = DB::table('bd_hospital')->where('code', $body['hosCode'])->first();

        $data = [
            'displayname'           => $body['name'] . ' ' . $body['lastName'],
            'first_name'            => $body['name'],
            'last_name'             => $body['lastName'],
            'username'              => $user->username,
            'email'                 => $user->email,
            'hosCode'               => $body['hosCode'],
            'hosName'               => $body['hosName'],
            'hos_health_region_id'  => $hospital->health_region_id,
            'hos_province_id'       => $hospital->province_id,
            'hospitalPosition'      => $body['hospitalPosition'],
            'hospitalDepartment'    => $body['hospitalDepartment'],
            'phoneNumber'           => $body['phoneNumber'],
            'roleCaw'               => $body['roleCaw'],
            'roleCancer'            => $body['roleCancer'],
            'roleRefer'             => $body['roleRefer'],
            'roleProvince'          => $body['roleProvince'],
            'roleHealthRegion'      => $body['roleHealthRegion'],
            'roleSuperAdmin'        => $body['roleSuperAdmin'],
            'roleAdmin'             => 'N',
            'expiresIn'             => $expiresIn,
            'accessToken'           => $accessToken,
            'refreshToken'          => $accessToken,
            'localId'               => $user->id,
            'userId'                => $body['pid'],
            'staffId'               => $body['pid'],
            'roles'                 => [],
        ];

        $response = [
            'success'   => true,
            'message'   => 'ยินดีต้อนรับ.',
            'data'      => $data,
        ];

        return response()->json($response, 200);
    }

    public function login1(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('username', 'password');

        $accessToken = Auth::attempt($credentials);
        if (!$accessToken) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized',
            ], 401);
        }

        $user           = Auth::user();

        // Check if OTP confirmation is required
        if ($user->completeOTPConfirm === 'N') {
            return response()->json([
                'status' => 'error',
                'message' => 'Please confirm your OTP before proceeding.',
            ], 403);
        }

        // Check if password update is required
        if ($user->completePassword === 'N') {
            return response()->json([
                'status' => 'warning',
                'message' => 'Please update your password.',
            ], 400);
        }

        // $refreshToken   = Auth::refresh();
        $expiresIn      = Auth::factory()->getTTL() * 60;

        $body = $user;

        $hospital = DB::table('bd_hospital')->where('code', $body['hosCode'])->first();

        $data = [
            'displayname'           => $body['name'] . ' ' . $body['lastName'],
            'first_name'            => $body['name'],
            'last_name'             => $body['lastName'],
            'username'              => $user->username,
            'email'                 => $user->email,
            'hosCode'               => $body['hosCode'],
            'hosName'               => $body['hosName'],
            'hos_health_region_id'  => $hospital->health_region_id,
            'hos_province_id'       => $hospital->province_id,
            'hospitalPosition'      => $body['hospitalPosition'],
            'hospitalDepartment'    => $body['hospitalDepartment'],
            'phoneNumber'           => $body['phoneNumber'],
            'roleCaw'               => $body['roleCaw'],
            'roleCancer'            => $body['roleCancer'],
            'roleRefer'             => $body['roleRefer'],
            'roleProvince'          => $body['roleProvince'],
            'roleHealthRegion'      => $body['roleHealthRegion'],
            'roleSuperAdmin'        => $body['roleSuperAdmin'],
            'expiresIn'             => $expiresIn,
            'accessToken'           => $accessToken,
            'refreshToken'          => $accessToken,
            'localId'               => $user->id,
            'userId'                => $body['pid'],
            'staffId'               => $body['pid'],
            'roles'                 => [],
        ];

        $response = [
            'success'   => true,
            'message'   => 'ยินดีต้อนรับ.',
            'data'      => $data,
        ];

        return response()->json($response, 200);
    }

    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        $token = Auth::login($user);
        return response()->json([
            'status' => 'success',
            'message' => 'User created successfully',
            'user' => $user,
            'authorisation' => [
                'token' => $token,
                'type' => 'bearer',
            ]
        ]);
    }

    public function logout()
    {
        Auth::logout();
        return response()->json([
            'status' => 'success',
            'message' => 'Successfully logged out',
        ]);
    }

    public function refresh()
    {
        return response()->json([
            'status' => 'success',
            'user' => Auth::user(),
            'authorisation' => [
                'token' => Auth::refresh(),
                'type' => 'bearer',
            ]
        ]);
    }

    public function login_mobile_app(Request $request)
    {

        $fname = $request->fname;
        $lname = $request->lname;
        $cid = $request->cid;
        $date = $request->date;

        $dataPatien = DB::table('data_patient')
            ->where('cid', $cid)
            ->where('birth_date', $date)
            ->where('name', $fname)
            ->where('last_name', $lname)
            ->first();

        if (!$dataPatien) {
            return response()->json([
                'status' => 'error',
                'message' => 'ไม่พบข้อมูลผู้ป่วย กรุณาติดต่อเจ้าหน้าที่',
            ], 400);
        }

        $otpController = new OTPController();
        return $otpController->Request_OTP($request);
    }

    public function authLogin(Request $request)
    {
        $credentials = $request->only('username', 'password');

        $accessToken = Auth::attempt($credentials);
        if (!$accessToken) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized',
            ], 401);
        }

        $user           = Auth::user();

        if ($user->active != 'Y') {
            return response()->json([
                'status' => 'error',
                'message' => 'ผู้ใช้งานนี้ถูกปิด',
            ], 400);
        }

        if ($user->completeInformation != 'Y') {
            return response()->json([
                'status'  => 'error',
                'message' => 'กรุณายืนยันข้อมูล',
                'step'    => 1
            ], 400);
        }

        if ($user->completeOTPConfirm != 'Y') {
            return response()->json([
                'status'  => 'error',
                'message' => 'กรุณายืนยัน OTP',
                'step'    => 2
            ], 400);
        }

        if ($user->completePassword != 'Y') {
            return response()->json([
                'status'  => 'error',
                'message' => 'กรุณาเปลี่ยนรหัสผ่าน',
                'step'    => 3
            ], 400);
        }

        $expiresIn      = Auth::factory()->getTTL() * 60;

        $hospital = DB::table('bd_hospital')->where('code', $user->hosCode)->first();

        $data = [
            'displayname'           => $user->name . ' ' . $user->lastName,
            'first_name'            => $user->name,
            'last_name'             => $user->lastName,
            'username'              => $user->username,
            'email'                 => $user->email,
            'hosCode'               => $user->hosCode,
            'hosName'               => $user->hosName,
            'hos_health_region_id'  => $hospital->health_region_id,
            'hos_province_id'       => $hospital->province_id,
            'hospitalPosition'      => $user->hospitalPosition,
            'hospitalDepartment'    => $user->hospitalDepartment,
            'phoneNumber'           => $user->phoneNumber,
            'roleCaw'               => $user->roleCaw,
            'roleCancer'            => $user->roleCancer,
            'roleRefer'             => $user->roleRefer,
            'roleProvince'          => $user->roleProvince,
            'roleHealthRegion'      => $user->roleHealthRegion,
            'roleSuperAdmin'        => $user->roleSuperAdmin,
            'roleAdmin'             => $user->roleAdmin,
            'expiresIn'             => $expiresIn,
            'accessToken'           => $accessToken,
            'refreshToken'          => $accessToken,
            'localId'               => $user->id,
            'userId'                => $user->pid,
            'staffId'               => $user->pid,
            'roles'                 => [],
        ];

        $response = [
            'success'   => true,
            'message'   => 'ยินดีต้อนรับ.',
            'data'      => $data,
        ];

        return response()->json($response, 200);
    }

    public function authUpdateInfomation(Request $request)
    {
        $credentials = $request->only('username', 'password');

        $accessToken = Auth::attempt($credentials);
        if (!$accessToken) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized',
            ], 401);
        }

        $user           = Auth::user();

        DB::table('users')->where('id', $user->id)->update([
            'name' => $request->name,
            'lastName' => $request->lastName,
            'email' => $request->email,
            'phoneNumber' => $request->phoneNumber,
            'hospitalPosition' => $request->hospitalPosition,
            'hospitalDepartment' => $request->hospitalDepartment,
            'completeOTPConfirm' => 'Y',
            'completeInformation' => 'Y'
        ]);

        // if ($user->completePassword != 'Y') {
        //     return response()->json([
        //         'status'  => 'error',
        //         'message' => 'กรุณาเปลี่ยนรหัสผ่าน',
        //         'step'    => 3
        //     ], 400);
        // }

        $response = [
            'success'   => true,
            'message'   => 'ยินดีต้อนรับ.',
        ];

        return response()->json($response, 200);
    }

    public function authChangePassword(Request $request)
    {
        $credentials = $request->only('username', 'password');

        $accessToken = Auth::attempt($credentials);
        if (!$accessToken) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized',
            ], 401);
        }

        $user           = Auth::user();

        DB::table('users')->where('id', $user->id)->update([
            'password' => Hash::make($request->new_password),
            'completePassword' => 'Y'
        ]);

        $response = [
            'success'   => true,
            'message'   => 'ยินดีต้อนรับ.',
        ];

        return response()->json($response, 200);
    }
}
