<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class ApiController extends Controller
{
    //
    public function getArea()
    {
        $data = DB::table('bd_area')->select('code', 'post_code', 'tambon', 'amphur', 'province')->get();
        return response()->json($data);
    }

    public function getBehaver()
    {
        $data = DB::table('bd_behaviour')->get();
        return response()->json($data);
    }

    public function getDeathCause()
    {
        $data = DB::table('bd_deathcause')->get();
        return response()->json($data);
    }

    public function getDiagnosis()
    {
        $data = DB::table('bd_diagnosis')->get();
        return response()->json($data);
    }

    public function getFinanceSupport()
    {
        $data = DB::table('bd_finance_support')->get();
        return response()->json($data);
    }

    public function getIcd10()
    {
        $data = DB::table('bd_icd10')->select('ICD10 as code', 'ICD10_TEXT as name')->get();
        return response()->json($data);
    }

    public function getTopo()
    {
        $data = DB::table('bd_topo')->get();
        return response()->json($data);
    }

    public function getSex()
    {
        $data = DB::table('bd_sex')->where('status', 'Y')->get();
        return response()->json($data);
    }

    public function getTitle()
    {
        $data = DB::table('bd_title')->get();
        return response()->json($data);
    }

    public function getTreatment()
    {
        $data = DB::table('bd_treatment')->get();
        return response()->json($data);
    }

    public function getNational()
    {
        $data = DB::table('bd_national')->get();
        return response()->json($data);
    }

    public function getGrade()
    {
        $data = DB::table('bd_grade')->get();
        return response()->json($data);
    }

    public function getStage()
    {
        $data = DB::table('bd_stage')->get();
        return response()->json($data);
    }

    public function getApiLog()
    {
        $data = DB::table('vw_log_api_count_data')->orderBy('health_region')->get();
        
        return response()->json([
            'status' => true,
            'data' => [
                'data' => $data,
                'hospitals' => DB::table('bd_hospital')->where('much_keyin', 1)->get()
            ]
        ]);
    }
}
