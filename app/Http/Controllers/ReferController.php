<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\CancerController;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class ReferController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'patient_id' => 'required|exists:data_patient,id',
            'from_hospital_code' => 'required|exists:bd_hospital,code',
            'to_hospital_code' => 'required|exists:bd_hospital,code',
            'primary_hospital_code' => 'nullable|exists:bd_hospital,code',
            'icd10_code' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }

        $user = Auth::user();
        $input = $request->all();

        $icd10_code = isset($input['icd10_code']) ? str_replace('.', '', strtoupper($input['icd10_code'])) : null;
        $icd10_text = $icd10_code ? static::getIcd10Text($icd10_code) : null;

        $input['icd10_code'] = $icd10_code;
        $input['icd10_text'] = $icd10_text;

        $input['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
        $input['updated_by'] = $user->id;
        $input['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
        $input['created_by'] = $user->id;
        $input['finance_support_code'] = static::getCodeFromText($input, 'finance_support_text', 'bd_finance_support');
        $input['finance_support_text'] = isset($input['finance_support_text']) ? $input['finance_support_text'] : null;

        DB::beginTransaction();

        try {
            $patient_id = $input['patient_id'];

            unset($input['patient_id']);

            $id = DB::table('data_refer')->insertGetId($input);

            if (isset($patient_id)) {

                $patient = DB::table('data_patient')->where('id', $patient_id)->first();

                $patientData = (array) $patient;
                unset($patientData['id']);

                $patientData['refer_id'] = $id;
                $patient_id = DB::table('data_refer_patient')->insertGetId($patientData);

                $val_cancer = [
                    'patient_id'            => $patient_id,
                    'icd10_code'            => $input['icd10_code'],
                    'icd10_text'            => $input['icd10_text'],
                    'finance_support_code'  => $input['finance_support_code'],
                    'finance_support_text'  => $input['finance_support_text'],
                    'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                    'created_by'            => $user->id,
                    'updated_at'            => Carbon::now()->format('Y-m-d H:i:s'),
                    'updated_by'            => $user->id,
                ];

                $cancer_id = DB::table('data_refer_cancer')->insertGetId($val_cancer);
            }


            if ($id) {
                $this->updateState($input['from_hospital_code'], 'refers_from');
                $this->updateState($input['to_hospital_code'], 'refers_to');
            }

            //send notification
            try {
                $url = env('SOCKET_IO_HOST') . '/api/refer';

                $body = ['event_name' => 'noti' . $input['to_hospital_code']];
                $response = Http::post($url, $body);
            } catch (\Throwable $th) {
                Log::error('Send message failed: ' . $th->getMessage());
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            Log::error($e);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 422);
        }

        return response()->json([
            'status' => true,
            'message' => 'บันทึกข้อมูลสำเร็จ.',
            'id' => $id
        ], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $refer = DB::table('data_refer')
            ->select([
                'data_refer.id',
                DB::raw('bd_title.name as title_name'),
                'data_patient.name',
                'data_patient.last_name',
                DB::raw('from_hospital.name as from_hospital_name'),
                DB::raw('to_hospital.name as to_hospital_name'),
                'data_refer.created_at'
            ])
            ->leftJoin('data_patient', 'data_refer.patient_id', '=', 'data_patient.id')
            ->leftJoin('bd_title', 'bd_title.code', '=', 'data_patient.title_code')
            ->leftJoin(DB::raw('bd_hospital from_hospital'), 'data_refer.from_hospital_code', '=', 'from_hospital.code')
            ->leftJoin(DB::raw('bd_hospital to_hospital'), 'data_refer.to_hospital_code', '=', 'to_hospital.code')
            ->where('data_refer.id', $id)
            ->first();

        return response()->json($refer, 200);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required',
            'file'   => 'file|mimes:jpeg,png,jpg,pdf',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }

        $user = Auth::user();
        $input = $request->all();

        $input['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
        $input['updated_by'] = $user->id;

        DB::beginTransaction();

        try {

            $refer = DB::table('data_refer as R')
                ->join('data_refer_patient as P', 'R.id', '=', 'P.refer_id')
                ->where('R.id', $id)
                ->first();

            switch ($input['status']) {
                case 1: //ขอข้อมูลเพิ่มเติม
                    DB::table('data_refer')->where('id', $id)->update($input);

                    DB::table('messages')->insert([
                        'user_id'       => $user->id,
                        'refer_id'      => $refer->id,
                        'hospital_code' => $refer->to_hospital_code,
                        'message'       => $input['message'],
                        'created_at'    => Carbon::now()->format('Y-m-d H:i:s'),
                        'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
                    ]);
                    break;

                case 2: //รับ Refer
                    if ($request->hasFile('file')) {
                        $file = $request->file('file');
                        $path = $file->store('files' . '/' . Carbon::now()->format('Ymd'), 'public');

                        $input['file'] = 'upload/' . $path;
                    }

                    $input['received_by'] = $user->id;
                    $input['received_at'] = Carbon::now()->format('Y-m-d H:i:s');
                    $input['appoint_date'] = getDateTimeTHtoEN($input['appoint_date']);

                    // เช็คในตาราง data_patient
                    $patientExists = DB::table('data_patient')
                        ->where('hospital_code', $refer->to_hospital_code)
                        ->where('cid', $refer->cid)
                        ->exists();

                    // ถ้ามีข้อมูลใน data_patient อยู่แล้ว ให้ existed เป็น 1 
                    $input['existed'] = $patientExists ? 1 : 0;

                    DB::table('data_refer')->where('id', $id)->update($input);

                    //แจ้งเตือนไปโรงพยาบาลที่ส่ง
                    try {
                        $url = env('SOCKET_IO_HOST') . '/api/refer';

                        $body = ['event_name' => 'noti' . $input['from_hospital_code']];
                        $response = Http::post($url, $body);
                    } catch (\Throwable $th) {
                        Log::error('Send message failed: ' . $th->getMessage());
                    }

                    //แจ้งเตืนไปหาผู้ป่วย

                    break;

                case 3: //ไม่รับ Refer
                    DB::table('data_refer')->where('id', $id)->update($input);
                    $val_notify = [
                        'hospital_code' => $refer->from_hospital_code,
                        'message'       => 'การ Refer ถูกปฏิเสธ',
                        'created_at'    => Carbon::now()->format('Y-m-d H:i:s')
                    ];

                    DB::table('refer_notification')->insertGetId($val_notify);

                    break;

                default:
                    # code...
                    break;
            }

            //get patient
            $patient = DB::table('data_patient')->find($refer->patient_id);
            if ($patient) {
                //send notify
                $title = $refer->message;
                $body = $refer->message;
                $target_id = $refer->id;
                $type = 'refer';
                $NotifyUser = [$patient->cid];
                $this->addNotifyLog($title, $body, $target_id, $type, $NotifyUser);
            }

            DB::commit();
        } catch (Exception $e) {

            DB::rollBack();

            Log::error($e);
            return response()->json([
                'data' => [
                    'status' => false,
                    'message' => $e->getMessage()
                ],
            ], 422);
        }

        return response()->json([
            'data' => [
                'input' => $input,
                'status' => true,
                'message' => 'บันทึกข้อมูลสำเร็จ.',
                'id' => $id,
            ],
        ], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        DB::table('data_refer')->where('id', $id)->delete();

        return response()->json([
            'data' => [
                'status' => true,
                'message' => 'ลบข้อมูลสำเร็จ.'
            ],
        ], 200);
    }

    public function getReferImport()
    {
        $user = Auth::user();

        $refers = DB::table('data_refer')
            ->select([
                'data_refer.id',
                DB::raw("concat(bd_title.name, data_refer_patient.name, ' ', data_refer_patient.last_name) as full_name"),
                'data_refer.from_hospital_code',
                'from_hospital.name as from_hospital_name',
                'data_refer.to_hospital_code',
                'to_hospital.name as to_hospital_name',
                'data_refer_patient.birth_date',
                'data_refer_patient.cid',
                'data_refer_patient.telephone_1',
                'data_refer.created_at'
            ])
            ->join('data_refer_patient', 'data_refer.id', '=', 'data_refer_patient.refer_id')
            ->join('bd_title', 'bd_title.code', '=', 'data_refer_patient.title_code')
            ->join('bd_hospital as from_hospital', 'data_refer.from_hospital_code', '=', 'from_hospital.code')
            ->join('bd_hospital as to_hospital', 'data_refer.to_hospital_code', '=', 'to_hospital.code')
            ->where('data_refer.to_hospital_code', $user->hosCode)
            ->where('data_refer.status', 2)
            ->where('data_refer.existed', 0)
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('data_patient')
                    ->whereColumn('data_patient.cid', 'data_refer_patient.cid')
                    ->whereColumn('data_patient.hospital_code', 'data_refer.to_hospital_code');
            })
            ->orderBy('data_refer.created_at', 'desc')
            ->get();

        return response()->json([
            'status' => true,
            'message' => 'ดึงข้อมูลสำเร็จ.',
            'data' => $refers
        ], 200);
    }
    public function getReferReceiveTable(Request $request)
    {
        $input = $request->all();
        $user = Auth::user();

        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search.value', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $searchable = [
            'P.name',
            'P.last_name',
            'P.cid',
            'P.hn_no',
            'R.icd10_code',
            DB::raw("CONCAT_WS(' ', bd_title.name, P.name, ' ', P.last_name)")
        ];

        $query = DB::table('data_refer as R')
            ->leftJoin('data_refer_patient as P', 'R.id', '=', 'P.refer_id')
            ->leftJoin('bd_title', 'P.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_hospital as H', 'R.from_hospital_code', '=', 'H.code')
            ->leftJoin('bd_sex as S', 'P.sex_code', '=', 'S.code')
            ->leftJoin('users as U', 'R.created_by', '=', 'U.id')
            ->leftJoin('users as UR', 'R.received_by', '=', 'UR.id')
            ->select(
                'R.id',
                'R.readed',
                'R.created_at AS created_at',
                'S.name AS sex_name',
                DB::raw("CONCAT_WS(' ', bd_title.name, P.name, ' ', P.last_name) as full_name"),
                'P.cid',
                'H.name AS from_hospital_name',
                'H.code AS from_hospital_code',
                'R.icd10_code',
                'R.status',
                DB::raw("CONCAT(U.name, ' ', U.lastName) as from_user_name"),
                'R.appoint_date',
                DB::raw("CONCAT(UR.name, ' ', UR.lastName) as received_user_name"),
                // DB::raw('COUNT(*) as refer_count')
            )
            ->where('R.to_hospital_code', $user->hosCode)
            ->whereIn('R.status', $input['status_selected'])
            ->where(function ($query) use ($searchable, $search) {
                if (isset($search) && $search) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search . '%');
                    }
                }
            })
            // ->groupBy('R.readed', 'P.cid', 'H.name', 'R.icd10_code', 'R.status', 'bd_title.name', 'P.name', 'P.last_name')
            ->orderBy('R.readed', 'ASC')
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);

        $count = DB::table('data_refer as R')
            ->select(
                'R.to_hospital_code',
                DB::raw('SUM(CASE WHEN R.status = 0 THEN 1 ELSE 0 END) AS status_0'),
                DB::raw('SUM(CASE WHEN R.status = 1 THEN 1 ELSE 0 END) AS status_1'),
                DB::raw('SUM(CASE WHEN R.status = 2 THEN 1 ELSE 0 END) AS status_2'),
                DB::raw('SUM(CASE WHEN R.status = 3 THEN 1 ELSE 0 END) AS status_3')
            )->where('R.to_hospital_code', $user->hosCode)
            ->groupBy('R.to_hospital_code')
            ->first();

        return [
            'data' => $query,
            'count' => $count
        ];
    }

    public function _getReferReceiveTable(Request $request)
    {
        $input = $request->all();
        $user = Auth::user();

        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search.value', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $searchable = ['data_patient.name', 'data_patient.last_name', 'data_patient.cid', 'data_patient.hn_no', 'data_cancer_summary.icd10_code'];

        $dataset = DB::table('data_refer')
            ->select([
                'data_refer.patient_id',
                DB::raw("MAX(bd_sex.name) as sex_name"),
                DB::raw("MAX(data_refer.id) as id"), // ใช้ MAX เพื่อเลือก id ของ refer ล่าสุด
                'data_patient.hn_no',
                DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name) as full_name"),
                'data_patient.birth_date',
                'data_patient.cid',
                DB::raw("MAX(bd_hospital.name) as from_hospital_name"), // ใช้ MAX เพราะไม่สามารถ GROUP BY ตรงๆ ได้
                DB::raw("MAX(bd_hospital.code) as from_hospital_code"),
                DB::raw("MAX(concat(COALESCE(users.name, ''), ' ', COALESCE(users.lastName, ''))) as from_user_name"),
                DB::raw("MAX(data_refer.status) as status"),
                DB::raw("MAX(data_refer.readed) as readed"),
                DB::raw("MAX(data_refer.appoint_date) as appoint_date"),
                DB::raw("MAX(data_refer.message) as message"),
                DB::raw("MAX(data_refer.created_at) as created_at"),
                DB::raw("COUNT(data_refer.id) as refer_count"), // นับจำนวนการ refer
                DB::raw("GROUP_CONCAT(data_cancer_summary.icd10_code SEPARATOR ', ') as icd10_codes"), // รวม icd10_code
            ])
            ->leftJoin('data_patient', 'data_refer.patient_id', '=', 'data_patient.id')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('data_cancer_summary', function ($join) {
                $join->on('data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->on('data_patient.hospital_code', '=', 'data_cancer_summary.hospital_code'); // ใช้คีย์สองตัว
            })
            ->leftJoin('bd_hospital', 'data_refer.from_hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->leftJoin('users', 'users.id', '=', 'data_refer.created_by')
            ->where('data_refer.to_hospital_code', $user->hosCode)
            ->whereIn('data_refer.status', $input['status_selected'])
            ->where(function ($query) use ($searchable, $search) {
                if (isset($search) && $search) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search . '%');
                    }
                }
            })
            ->groupBy([
                'data_refer.patient_id',
                'data_patient.hn_no',
                'bd_title.name',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.birth_date',
                'data_patient.cid'
            ])
            ->orderBy('data_refer.readed', 'ASC')
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);

        // $dataset->searchTerm = isset($input['searchTerm']) ? $input['searchTerm'] : '';

        return $dataset;
    }

    public function getReferFullDetail(Request $request)
    {
        $input = $request->all();
        $user = Auth::user();

        $curr_patient   = DB::table('data_refer_patient')
            ->select([
                'data_refer_patient.*',
                'data_refer_patient.id',
                DB::raw('bd_hospital.name as hospital_name'),
                'data_refer_patient.hn_no',
                'data_refer_patient.title_code',
                DB::raw('bd_title.name as title_name'),
                'data_refer_patient.name',
                'data_refer_patient.last_name',
                DB::raw("CONCAT_WS(' ', bd_title.name, ' ', data_refer_patient.name, ' ', data_refer_patient.last_name) as fullname"),
                'data_refer_patient.cid',
                'data_refer_patient.birth_date',
                'data_refer_patient.sex_code',
                DB::raw('bd_sex.name as sex_name'),
                'data_refer_patient.address_no',
                'data_refer_patient.address_moo',
                'ref_sub_districts.sub_district_name_th',
                'ref_districts.district_name_th',
                'ref_provinces.province_name_th',
                'data_refer_patient.address_zipcode',
                'data_refer_patient.email',
                'data_refer_patient.telephone_1',
                'data_refer_patient.telephone_2',
                'data_refer_patient.death_date',
                DB::raw('bd_deathcause.name as deathcause_name')
            ])
            ->leftJoin('data_refer', 'data_refer.id', '=', 'data_refer_patient.refer_id')
            ->leftJoin('bd_title', 'data_refer_patient.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_sex', 'data_refer_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('bd_hospital', 'data_refer_patient.hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('bd_deathcause', 'data_refer_patient.deathcause_code', '=', 'bd_deathcause.code')
            ->leftJoin('ref_provinces', 'data_refer_patient.address_province_id', '=', 'ref_provinces.id')
            ->leftJoin('ref_districts', 'data_refer_patient.address_district_id', '=', 'ref_districts.id')
            ->leftJoin('ref_sub_districts', 'data_refer_patient.address_sub_district_id', '=', 'ref_sub_districts.id')
            ->where('data_refer_patient.refer_id', $input['refer_id'])
            ->first();

        $curr_refer     = $this->getRefer($input['refer_id']);
        $curr_cancers   = \App\Http\Controllers\CancerController::getCancersAndSumByCid($curr_patient->cid);
        $curr_files     = \App\Http\Controllers\CancerController::getFilesByCid($curr_patient->cid);

        if ($user->hosCode == $curr_refer->to_hospital_code) {
            DB::table('data_refer')->where('id', $input['refer_id'])->update(['readed' => 1]);
        }

        return [
            'curr_patient'  => $curr_patient,
            'curr_refer'    => $curr_refer,
            'curr_cancers'  => $curr_cancers,
            'curr_files'    => $curr_files,
        ];
    }

    public function getReferSendTable(Request $request)
    {
        $input = $request->all();
        $user = Auth::user();

        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search.value', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $searchable = [
            DB::raw("CONCAT_WS(' ', bd_title.name, P.name, P.last_name)")
        ];

        $query = DB::table('data_refer as R')
            ->leftJoin('data_refer_patient as P', 'R.id', '=', 'P.refer_id')
            ->leftJoin('bd_title', 'P.title_code', '=', 'bd_title.code')
            ->leftJoin('bd_hospital as H', 'R.to_hospital_code', '=', 'H.code')
            ->leftJoin('bd_sex as S', 'P.sex_code', '=', 'S.code')
            ->leftJoin('users as U', 'R.created_by', '=', 'U.id')
            ->leftJoin('users as UR', 'R.received_by', '=', 'UR.id')
            ->select(
                'R.id',
                'R.readed',
                'R.created_at as created_at',
                'S.name AS sex_name',
                DB::raw("CONCAT_WS(' ', bd_title.name, P.name, P.last_name) as full_name"),
                'P.cid',
                'H.name AS to_hospital_name',
                'H.code AS to_hospital_code',
                'R.icd10_code',
                'P.hn_no',
                'R.status',
                DB::raw("CONCAT(U.name, ' ', U.lastName) as from_user_name"),
                'R.appoint_date',
                DB::raw("CONCAT(UR.name, ' ', UR.lastName) as received_user_name"),
                // DB::raw('COUNT(*) as refer_count')
            )
            ->where('R.from_hospital_code', $user->hosCode)
            ->whereIn('R.status', $input['status_selected'])
            ->where(function ($query) use ($searchable, $search) {
                if (isset($search) && $search) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search . '%');
                    }
                }
            })
            //->groupBy('R.readed', 'P.cid', 'H.name', 'R.icd10_code', 'R.status', 'bd_title.name', 'P.name', 'P.last_name')
            ->orderBy('R.readed', 'ASC')
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);

        $count = DB::table('data_refer as R')
            ->select(
                'R.from_hospital_code',
                DB::raw('SUM(CASE WHEN R.status = 0 THEN 1 ELSE 0 END) AS status_0'),
                DB::raw('SUM(CASE WHEN R.status = 1 THEN 1 ELSE 0 END) AS status_1'),
                DB::raw('SUM(CASE WHEN R.status = 2 THEN 1 ELSE 0 END) AS status_2'),
                DB::raw('SUM(CASE WHEN R.status = 3 THEN 1 ELSE 0 END) AS status_3')
            )->where('R.from_hospital_code', $user->hosCode)
            ->groupBy('R.from_hospital_code')
            ->first();

        return [
            'data' => $query,
            'count' => $count
        ];
    }

    public function _getReferSendTable(Request $request)
    {
        $input = $request->all();
        $user = Auth::user();

        $perPage = $request->input('length', 10);
        $page = $request->input('start', 0);
        $search = $request->input('search.value', "");
        $order = $request->input('order', []);
        $columns = $request->input('columns', []);

        $searchable = ['data_patient.name', 'data_patient.last_name', 'data_patient.cid', 'data_patient.hn_no', 'data_cancer_summary.icd10_code'];

        $query = DB::table('data_refer')
            ->select([
                'data_refer.patient_id',
                DB::raw("MAX(bd_sex.name) as sex_name"),
                DB::raw("MAX(data_refer.id) as id"), // ใช้ MAX เพื่อเลือก id ของ refer ล่าสุด
                'data_patient.hn_no',
                DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name) as full_name"),
                'data_patient.birth_date',
                'data_patient.cid',
                DB::raw("MAX(bd_hospital.name) as to_hospital_name"), // ใช้ MAX เพราะไม่สามารถ GROUP BY ตรงๆ ได้
                DB::raw("MAX(bd_hospital.code) as to_hospital_code"),
                DB::raw("MAX(data_refer.status) as status"),
                DB::raw("MAX(data_refer.readed) as readed"),
                DB::raw("MAX(data_refer.appoint_date) as appoint_date"),
                DB::raw("MAX(data_refer.message) as message"),
                DB::raw("MAX(data_refer.created_at) as created_at"),
                DB::raw("COUNT(data_refer.id) as refer_count"), // นับจำนวนการ refer
                DB::raw("GROUP_CONCAT(data_cancer_summary.icd10_code SEPARATOR ', ') as icd10_codes") // รวม icd10_code
            ])
            ->leftJoin('data_patient', 'data_refer.patient_id', '=', 'data_patient.id')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('data_cancer_summary', function ($join) {
                $join->on('data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->on('data_patient.hospital_code', '=', 'data_cancer_summary.hospital_code'); // ใช้คีย์สองตัว
            })
            ->leftJoin('bd_hospital', 'data_refer.to_hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->where('data_refer.from_hospital_code', $user->hosCode)
            ->whereIn('data_refer.status', $input['status_selected'])
            ->where(function ($query) use ($searchable, $search) {
                if (isset($search) && $search) {
                    foreach ($searchable as &$s) {
                        $query->orWhere($s, 'LIKE', '%' . $search . '%');
                    }
                }
            })
            ->groupBy([
                'data_refer.patient_id',
                'data_patient.hn_no',
                'bd_title.name',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.birth_date',
                'data_patient.cid'
            ])
            ->orderBy('data_refer.readed', 'ASC')
            ->orderBy($columns[$order[0]['column']]['data'], $order[0]['dir'])
            ->paginate($perPage, ['*'], 'page', ($page / $perPage) + 1);

        return $query;
    }

    public function getRefer($id)
    {
        $refer = DB::table('data_refer')->where('id', $id)->first();
        return $refer;
    }

    public function updateState($hospital_code, $field_name)
    {

        $hospital = DB::table('bd_hospital')->where('code', $hospital_code)->first();
        if ($hospital) {
            DB::table('state_patient')->updateOrInsert(
                [
                    'curr_date'         => Carbon::now()->format('Y-m-d'),
                    'hospital_code'     => $hospital_code
                ],
                [
                    'curr_date'         => Carbon::now()->format('Y-m-d'),
                    'health_region_id'  => $hospital->health_region_id,
                    'province_id'       => $hospital->province_id,
                    'district_id'       => $hospital->district_id,
                    'sub_district_id'   => $hospital->sub_district_id,
                    'hospital_code'     => $hospital_code,
                    $field_name         => DB::raw('ifnull(' . $field_name . ', 0) + 1')
                ]
            );

            return response()->json([
                'status' => true,
                'message' => 'State updated successfully for hospital ' . $hospital_code
            ], 200);
        }

        return response()->json([
            'status' => false,
            'message' => 'Hospital not found'
        ], 404);
    }

    public function getReferPatient(Request $request)
    {
        $hospital_code = $request->query('from_hospital_code');
        $patient_id = $request->query('patient_id');

        $query = DB::table('data_refer')
            ->select([
                DB::raw("data_refer.id as id"),
                'data_refer.patient_id',
                DB::raw("bd_sex.name as sex_name"),
                'data_patient.hn_no',
                DB::raw("concat(bd_title.name, data_patient.name, ' ', data_patient.last_name) as full_name"),
                DB::raw("bd_hospital.name as from_hospital_name"),
                DB::raw("bd_hospital.code as from_hospital_code"),
                DB::raw("data_refer.status as status"),
                DB::raw("data_refer.readed as readed"),
                DB::raw("data_refer.appoint_date as appoint_date"),
                DB::raw("data_refer.created_at as created_at"),
                DB::raw("GROUP_CONCAT(data_cancer_summary.icd10_code SEPARATOR ', ') as icd10_codes")
            ])
            ->leftJoin('data_patient', 'data_refer.patient_id', '=', 'data_patient.id')
            ->leftJoin('bd_sex', 'data_patient.sex_code', '=', 'bd_sex.code')
            ->leftJoin('data_cancer_summary', function ($join) {
                $join->on('data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->on('data_patient.hospital_code', '=', 'data_cancer_summary.hospital_code');
            })
            ->leftJoin('bd_hospital', 'data_refer.to_hospital_code', '=', 'bd_hospital.code')
            ->leftJoin('bd_title', 'data_patient.title_code', '=', 'bd_title.code')
            ->where('data_refer.from_hospital_code', $hospital_code)
            ->where('data_refer.patient_id', $patient_id)
            ->groupBy(
                'data_refer.id',
                'data_refer.patient_id',
                'bd_sex.name',
                'data_patient.hn_no',
                'bd_title.name',
                'data_patient.name',
                'data_patient.last_name',
                'bd_hospital.name',
                'bd_hospital.code',
                'data_refer.status',
                'data_refer.readed',
                'data_refer.appoint_date',
                'data_refer.created_at'
            )
            ->orderBy('data_refer.created_at', 'desc')
            ->get();

        return response()->json([
            'data' => $query,
        ], 200);
    }

    public function getNotifyReferPreAppoint()
    {

        DB::beginTransaction();
        try {

            $date2 = date("Y-m-d", strtotime("+2 day"));


            $Refer = DB::table('data_refer')
                ->where('appoint_date', $date2)
                ->get();

            for ($i = 0; $i < count($Refer); $i++) {

                $refer = $Refer[$i];

                //get patient
                $patient = DB::table('data_patient')->find($refer->patient_id);
                if ($patient) {
                    //send notify
                    $title = $refer->message;
                    $body = $refer->message;
                    $target_id = $refer->id;
                    $type = 'refer';
                    $NotifyUser = [$patient->cid];

                    $this->addNotifyLog($title, $body, $target_id, $type, $NotifyUser);
                }
                //
            }
            DB::commit();
        } catch (Exception $e) {
            //
            DB::rollback();
        }
    }

    public function getReportRefer(Request $request)
    {

        $from_hospital_code = $request->from_hospital_code;
        $to_hospital_code = $request->to_hospital_code;
        $patient_id = $request->patient_id;
        $appoint_start_date = $request->appoint_start_date;
        $appoint_end_date = $request->appoint_end_date;

        $refer = DB::table('data_refer');

        if ($from_hospital_code) {
            $refer->where('from_hospital_code', $from_hospital_code);
        }
        if ($to_hospital_code) {
            $refer->where('to_hospital_code', $to_hospital_code);
        }
        if ($patient_id) {
            $refer->where('patient_id', $patient_id);
        }

        if ($appoint_start_date) {
            $refer->where('appoint_date', '>=', $appoint_start_date);
        }
        if ($appoint_end_date) {
            $refer->where('appoint_date', '<=', $appoint_end_date);
        }

        $refer = $refer->select([
            DB::raw('SUM(reson_diagnosis) as total_reson_diagnosis'),
            DB::raw('SUM(reson_diagnosis_excision) as total_reson_diagnosis_excision'),
            DB::raw('SUM(reson_diagnosis_ct) as total_reson_diagnosis_ct'),
            DB::raw('SUM(reson_diagnosis_mri) as total_reson_diagnosis_mri'),
            DB::raw('SUM(reson_diagnosis_bone) as total_reson_diagnosis_bone'),
            DB::raw('SUM(reson_diagnosis_mammogram) as total_reson_diagnosis_mammogram'),
            DB::raw('SUM(reson_diagnosis_ultra) as total_reson_diagnosis_ultra'),
            DB::raw('SUM(reson_diagnosis_other) as total_reson_diagnosis_other'),
            DB::raw('SUM(reson_treat) as total_reson_treat'),
            DB::raw('SUM(reson_treat_surgery) as total_reson_treat_surgery'),
            DB::raw('SUM(reson_treat_chemo) as total_reson_treat_chemo'),
            DB::raw('SUM(reson_treat_pallative) as total_reson_treat_pallative'),
            DB::raw('SUM(reson_treat_other) as total_reson_treat_other'),
            DB::raw('SUM(reson_right) as total_reson_right'),
            DB::raw('SUM(reson_wanted) as total_reson_wanted'),
            DB::raw('SUM(reson_other) as total_reson_other'),
            DB::raw('SUM(readed) as total_readed'),
            DB::raw('SUM(imported) as total_imported'),
            DB::raw('SUM(removed) as total_removed'),
            DB::raw('SUM(reson_reject_1) as total_reson_reject_1'),
            DB::raw('SUM(reson_reject_2) as total_reson_reject_2'),
            DB::raw('SUM(reson_reject_3) as total_reson_reject_3'),
        ])->first();

        return response()->json([
            'data' => $refer,
        ], 200);
    }

    public static function getIcd10Text($icd10_code)
    {
        $icd10 = DB::table('bd_icd10')->where('ICD10', strtoupper($icd10_code))->first();
        if ($icd10) {
            return $icd10->ICD10_TEXT;
        } else {
            return null;
        }
    }
    public static function getIcd10group($icd10_code)
    {
        $group = DB::table('bd_sitegroup')
            ->where('icd10_list', 'like', '%' . $icd10_code . '%')
            ->first();

        if (!$group) {
            return null;
        }

        return $group->group_id;
    }

    public function quickly(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_hospital_code' => 'required|exists:bd_hospital,code',
            'to_hospital_code' => 'required|exists:bd_hospital,code',
            'icd10_code' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }

        $user = Auth::user();
        $input = $request->all();

        unset($input['user_app_id']);

        $icd10_code = isset($input['icd10_code']) ? str_replace('.', '', strtoupper($input['icd10_code'])) : null;
        $icd10_text = static::getIcd10Text($icd10_code);

        if (isset($input['morphology_text']) && $input['morphology_text'] != '') {
            $mor = DB::table('bd_mor')->where(DB::raw("concat(code, ' ', name)"), $input['morphology_text'])->first();
            if ($mor) {
                $mor_key = $mor->key;
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Morphology ไม่ถูกต้อง กรุณาเลือกจากตัวเลือกเท่านั้น'
                ], 422);
            }
        } else {
            $mor_key = null;
        }

        DB::beginTransaction();

        try {
            $val_refer = [
                'from_hospital_code'        => $input['from_hospital_code'],
                'to_hospital_code'          => $input['to_hospital_code'],
                'reson_diagnosis'           => $input['reson_diagnosis'],
                'reson_diagnosis_excision'  => $input['reson_diagnosis_excision'],
                'reson_diagnosis_ct'        => $input['reson_diagnosis_ct'],
                'reson_diagnosis_mri'       => $input['reson_diagnosis_mri'],
                'reson_diagnosis_bone'      => $input['reson_diagnosis_bone'],
                'reson_diagnosis_mammogram' => $input['reson_diagnosis_mammogram'],
                'reson_diagnosis_ultra'     => $input['reson_diagnosis_ultra'],
                'reson_diagnosis_other'     => $input['reson_diagnosis_other'],
                'reson_diagnosis_other_text' => $input['reson_diagnosis_other_text'],
                'reson_treat'               => $input['reson_treat'],
                'reson_treat_radiation'     => $input['reson_treat_radiation'],
                'reson_treat_surgery'       => $input['reson_treat_surgery'],
                'reson_treat_chemo'         => $input['reson_treat_chemo'],
                'reson_treat_pallative'     => $input['reson_treat_pallative'],
                'reson_treat_other'         => $input['reson_treat_other'],
                'reson_treat_other_text'    => $input['reson_treat_other_text'],
                'reson_right'               => $input['reson_right'],
                'reson_wanted'              => $input['reson_wanted'],
                'reson_other'               => $input['reson_other'],
                'note'                      => $input['note'],
                'finance_support_code'      => static::getCodeFromText($input, 'finance_support_text', 'bd_finance_support'),
                'finance_support_text'      => isset($input['finance_support_text']) ? $input['finance_support_text'] : null,
                'icd10_code'                => $icd10_code,
                'icd10_text'                => $icd10_text,
                // 'icd10_group_id'            => $icd10_group_id,
                'morphology_code'           => $mor_key,
                'morphology_text'           => $input['morphology_text'],
                'stage_code'                => $input['stage_code'],
                'primary_hospital_code'     => $input['primary_hospital_code'],
                'updated_at'                => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_by'                => $user->id,
                'created_at'                => Carbon::now()->format('Y-m-d H:i:s'),
                'created_by'                => $user->id
            ];

            $refer_id = DB::table('data_refer')->insertGetId($val_refer);

            $controller = new PatientController();
            $val_patient = $controller->getValPatientData($input);

            $val_patient['refer_id'] = $refer_id;
            $val_patient['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
            $val_patient['updated_by'] = $user->id;
            $val_patient['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
            $val_patient['created_by'] = $user->id;

            $patient_id = DB::table('data_refer_patient')->insertGetId($val_patient);

            // Step 3: Insert data into `data_refer_cancer`
            $icd10_text = $icd10_code ? CancerController::getIcd10Text($icd10_code) : null;

            $val_cancer = [
                'hospital_code'     => $input['from_hospital_code'],
                'patient_id'        => $patient_id,
                'icd10_code'        => $icd10_code,
                'icd10_text'        => $icd10_text,
                'clinical_summary'  => $input['clinical_summary'] ?? null,
                'updated_at'        => Carbon::now()->format('Y-m-d H:i:s'),
                'updated_by'        => $user->id,
                'created_at'        => Carbon::now()->format('Y-m-d H:i:s'),
                'created_by'        => $user->id
            ];

            $cancer_id = DB::table('data_refer_cancer')->insertGetId($val_cancer);

            if ($cancer_id) {
                $this->updateState($val_cancer['hospital_code'], 'cancers');
            }

            if (isset($input['curr_files']) && sizeof($input['curr_files']) > 0) {
                foreach ($input['curr_files'] as $idx => $val_file) {
                    if ($request->hasFile('curr_files.' . $idx . '.file')) {
                        $file = $request->file('curr_files.' . $idx . '.file');
                        $file_size = $file->getSize();
                        $store_path = "upload/files/" . Carbon::now()->format('Ymd');
                        $name = md5(uniqid(rand(), true)) . str_replace(' ', '-', $file->getClientOriginalName());
                        $file->move(public_path('/' . $store_path), $name);

                        $val_file['file_path'] = $store_path . '/' . $name;
                        $val_file['file_name'] = $file->getClientOriginalName();
                        $val_file['file_size'] = $file_size;
                        $val_file['patient_id'] = $patient_id;
                        $val_file['cancer_id'] = $cancer_id;
                        $val_file['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
                        $val_file['updated_by'] = $user->id;
                        $val_file['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
                        $val_file['created_by'] = $user->id;

                        unset($val_file['file']);
                        DB::table('data_refer_cancer_file')->insert($val_file);
                    }
                }
            }

            //send notification
            $url = env('SOCKET_IO_HOST') . '/api/refer';

            $body = [
                'event_name' => 'noti' . $input['to_hospital_code']
            ];

            try {
                $response = Http::post($url, $body);
            } catch (\Throwable $th) {
                Log::error('Send message failed: ' . $th->getMessage());
            }

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error($th);

            return response()->json([
                'status' => false,
                'message' => 'เกิดข้อผิดพลาด: ' . $th->getMessage()
            ], 400);
        }

        return response()->json([
            'status' => true,
            'message' => 'บันทึกข้อมูลสำเร็จ.',
            'refer_id' => $refer_id
        ], 200);
    }

    public function uploadFile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'refer_id' => 'required|exists:data_refer,id',
            'curr_files' => 'required|array|min:1',
            'curr_files.*.file' => 'required|file|mimes:jpeg,png,jpg,doc,docx,xls,xlsx,pdf',
            'curr_files.*.file_group_id' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 400);
        }

        $user = Auth::user();
        $input = $request->all();
        $refer_id = $request->refer_id;

        // ดึง patient_id และ cancer_id 
        $patient = DB::table('data_refer_patient')->where('refer_id', $refer_id)->first();
        $cancer = DB::table('data_refer_cancer')->where('patient_id', $patient->id)->first();

        DB::beginTransaction();

        try {
            foreach ($input['curr_files'] as $idx => $val_file) {
                if ($request->hasFile('curr_files.' . $idx . '.file')) {
                    $file = $request->file('curr_files.' . $idx . '.file');
                    $file_size = $file->getSize();
                    $store_path = "upload/files/" . Carbon::now()->format('Ymd');
                    $name = md5(uniqid(rand(), true)) . str_replace(' ', '-', $file->getClientOriginalName());
                    $file->move(public_path('/' . $store_path), $name);

                    $val_file['file_path'] = $store_path . '/' . $name;
                    $val_file['file_name'] = $file->getClientOriginalName();
                    $val_file['file_size'] = $file_size;
                    $val_file['patient_id'] = $patient->id;
                    $val_file['cancer_id'] = $cancer->id;
                    $val_file['updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
                    $val_file['updated_by'] = $user->id;
                    $val_file['created_at'] = Carbon::now()->format('Y-m-d H:i:s');
                    $val_file['created_by'] = $user->id;

                    unset($val_file['file']);
                    unset($val_file['type']);
                    DB::table('data_refer_cancer_file')->insert($val_file);
                }
            }

            DB::commit();
            return response()->json([
                'status' => true,
                'message' => 'บันทึกข้อมูลสำเร็จ.',
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error($th);

            return response()->json([
                'status' => false,
                'message' => 'เกิดข้อผิดพลาด: ' . $th->getMessage()
            ], 400);
        }
    }

    public static function getCodeFromText($input, $field_name, $table_name)
    {
        if (isset($input[$field_name]) && strlen($input[$field_name]) > 0) {
            $vals = explode(' ', trim($input[$field_name]));
            if (sizeof($vals) > 0) {
                $rec = DB::table($table_name)->where('code', $vals[0])->first();
                if ($rec) {
                    return $vals[0];
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    public static function get_detail_quickly(Request $request)
    {
        $id = $request->input('id');

        $referData = DB::table('data_refer')->where('id', $id)->first();
        if (!$referData) {
            return response()->json([
                'data' => [
                    'status' => false,
                    'message' => 'ไม่พบข้อมูลการ refer '
                ]
            ], 404);
        }
        $patientData = DB::table('data_refer_patient')->where('refer_id', $id)->first();
        $cancerData = DB::table('data_refer_cancer')->where('patient_id', $patientData->id ?? null)->first();
        $cancerFile = DB::table('data_refer_cancer_file')->where('patient_id', $patientData->id ?? null)
            ->get();

        $referData->patient_data = $patientData;
        $referData->patient_data->cancer_data = $cancerData;
        $referData->patient_data->cancer_data->cancer_file = $cancerFile;

        return response()->json([
            'data' => [
                'refer_data' => $referData
            ]
        ], 200);
    }

    public function hosin(Request $request)
    {
        dd($request);
    }

    public function getNotification(Request $request)
    {
        $user = Auth::user();

        $data = DB::table('refer_notification')
            ->where('hospital_code', $user->hosCode)
            ->orderByDesc('created_at')
            ->get();

        return response()->json([
            'data' => $data,
        ], 200);
    }
}
