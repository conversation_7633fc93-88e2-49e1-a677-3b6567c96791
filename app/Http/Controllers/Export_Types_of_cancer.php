<?php

namespace App\Http\Controllers;

use App\Exports\PatientsAddressExport;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\AfterSheet;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Exports\number_type_treatment_Export;
use App\Exports\Export_Types_of_cancer_Excel;



class Export_Types_of_cancer extends Controller
{
    public function Export_Types_of_cancer(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $data = [];

        switch ($area_level_id) {
            case 1:
                $data = DB::table('data_cancer_summary', 's')
                    ->select(
                        'sg.group_text as GroupName',
                        'sg.group_desc as GroupDescription',
                        DB::raw("SUM(CASE WHEN s.age IS NULL OR s.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END) as '0'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END) as '5'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END) as '10'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END) as '15'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END) as '20'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END) as '25'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END) as '30'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END) as '35'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END) as '40'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END) as '45'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END) as '50'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END) as '55'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END) as '60'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END) as '65'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END) as '70'"),
                        DB::raw("SUM(CASE WHEN s.age >= 75 THEN 1 ELSE 0 END) as '75+'"),
                        DB::raw('COUNT(s.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date]);

                // เพิ่มเงื่อนไขตาม query parameter
                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($behaviour_code_start && $behaviour_code_end) {
                    $data->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $data->where('s.behaviour_code', 3);
                }

                if ($stage_code_start && $stage_code_end) {
                    $data->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                }

                if ($morpho_start && $morpho_end) {
                    $data->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }

                if ($treatment_code) {
                    $data->leftJoin('s_treatment as t', function ($join) {
                        $join->on('s.patient_id', '=', 't.patient_id')
                            ->on('s.icd10_code', '=', 't.icd10_code');
                    });
                    $data->where('t.treatment_type_id', $treatment_code);
                }

                if ($age_start && $age_end) {
                    $data->whereBetween('s.age', [$age_start, $age_end]);
                }

                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $data->where('s.recurrent', 1);
                }

                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $data->whereNotNull('p.death_date');
                }

                if ($icd10_group_id) {
                    $data->whereIn('s.icd10_group_id', $icd10_group_id);
                }

                $data = $data->groupBy('sg.group_text', 'sg.group_desc')
                    ->get();

                break;

            case 2:
                $data = DB::table('data_cancer_summary', 's')
                    ->select(
                        'sg.group_text as GroupName',
                        'sg.group_desc as GroupDescription',
                        DB::raw("SUM(CASE WHEN s.age IS NULL OR s.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END) as '0'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END) as '5'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END) as '10'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END) as '15'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END) as '20'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END) as '25'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END) as '30'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END) as '35'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END) as '40'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END) as '45'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END) as '50'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END) as '55'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END) as '60'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END) as '65'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END) as '70'"),
                        DB::raw("SUM(CASE WHEN s.age >= 75 THEN 1 ELSE 0 END) as '75+'"),
                        DB::raw('COUNT(s.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->leftJoin('ref_provinces as v', 'v.id', '=', 'p.address_province_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date]);

                // เพิ่มเงื่อนไขตาม query parameter
                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($area_id) {
                    $data->where('v.health_region_id', $area_id);
                }

                if ($behaviour_code_start && $behaviour_code_end) {
                    $data->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $data->where('s.behaviour_code', 3);
                }

                if ($stage_code_start && $stage_code_end) {
                    $data->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                }

                if ($morpho_start && $morpho_end) {
                    $data->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }

                if ($treatment_code) {
                    $data->leftJoin('s_treatment as t', function ($join) {
                        $join->on('s.patient_id', '=', 't.patient_id')
                            ->on('s.icd10_code', '=', 't.icd10_code');
                    });
                    $data->where('t.treatment_type_id', $treatment_code);
                }

                if ($age_start && $age_end) {
                    $data->whereBetween('s.age', [$age_start, $age_end]);
                }

                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $data->where('s.recurrent', 1);
                }

                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $data->whereNotNull('p.death_date');
                }

                if ($icd10_group_id) {
                    $data->whereIn('s.icd10_group_id', $icd10_group_id);
                }

                $data = $data->groupBy('sg.group_text', 'sg.group_desc')
                    ->get();

                break;

            case 3:
                $data = DB::table('data_cancer_summary', 's')
                    ->select(
                        'sg.group_text as GroupName',
                        'sg.group_desc as GroupDescription',
                        DB::raw("SUM(CASE WHEN s.age IS NULL OR s.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END) as '0'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END) as '5'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END) as '10'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END) as '15'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END) as '20'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END) as '25'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END) as '30'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END) as '35'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END) as '40'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END) as '45'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END) as '50'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END) as '55'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END) as '60'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END) as '65'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END) as '70'"),
                        DB::raw("SUM(CASE WHEN s.age >= 75 THEN 1 ELSE 0 END) as '75+'"),
                        DB::raw('COUNT(s.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date]);

                // เพิ่มเงื่อนไขตาม query parameter
                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($area_id) {
                    $data->where('p.address_province_id', $area_id);
                }

                if ($behaviour_code_start && $behaviour_code_end) {
                    $data->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $data->where('s.behaviour_code', 3);
                }

                if ($stage_code_start && $stage_code_end) {
                    $data->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                }

                if ($morpho_start && $morpho_end) {
                    $data->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }

                if ($treatment_code) {
                    $data->leftJoin('s_treatment as t', function ($join) {
                        $join->on('s.patient_id', '=', 't.patient_id')
                            ->on('s.icd10_code', '=', 't.icd10_code');
                    });
                    $data->where('t.treatment_type_id', $treatment_code);
                }

                if ($age_start && $age_end) {
                    $data->whereBetween('s.age', [$age_start, $age_end]);
                }

                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $data->where('s.recurrent', 1);
                }

                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $data->whereNotNull('p.death_date');
                }

                if ($icd10_group_id) {
                    $data->whereIn('s.icd10_group_id', $icd10_group_id);
                }

                $data = $data->groupBy('sg.group_text', 'sg.group_desc')
                    ->get();

                break;

            case 4:

                $data = DB::table('data_cancer_summary', 's')
                    ->select(
                        'sg.group_text as GroupName',
                        'sg.group_desc as GroupDescription',
                        DB::raw("SUM(CASE WHEN s.age IS NULL OR s.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END) as '0'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END) as '5'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END) as '10'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END) as '15'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END) as '20'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END) as '25'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END) as '30'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END) as '35'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END) as '40'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END) as '45'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END) as '50'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END) as '55'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END) as '60'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END) as '65'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END) as '70'"),
                        DB::raw("SUM(CASE WHEN s.age >= 75 THEN 1 ELSE 0 END) as '75+'"),
                        DB::raw('COUNT(s.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date]);

                // เพิ่มเงื่อนไขตาม query parameter
                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($area_id) {
                    $data->where('s.hospital_code', $area_id);
                }

                if ($behaviour_code_start && $behaviour_code_end) {
                    $data->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $data->where('s.behaviour_code', 3);
                }

                if ($stage_code_start && $stage_code_end) {
                    $data->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                }

                if ($morpho_start && $morpho_end) {
                    $data->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }

                if ($treatment_code) {
                    $data->leftJoin('s_treatment as t', function ($join) {
                        $join->on('s.patient_id', '=', 't.patient_id')
                            ->on('s.icd10_code', '=', 't.icd10_code');
                    });
                    $data->where('t.treatment_type_id', $treatment_code);
                }

                if ($age_start && $age_end) {
                    $data->whereBetween('s.age', [$age_start, $age_end]);
                }

                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $data->where('s.recurrent', 1);
                }

                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $data->whereNotNull('p.death_date');
                }

                if ($icd10_group_id) {
                    $data->whereIn('s.icd10_group_id', $icd10_group_id);
                }

                $data = $data->groupBy('sg.group_text', 'sg.group_desc')
                    ->get();

                break;
        }

        return response()->json([
            'data' => $data
        ], 200);
    }

    public function Export_Types_of_cancerPop(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        $data = [];

        switch ($area_level_id) {
            case 1:
                $data = DB::table('data_cancer_summary', 's')
                    ->select(
                        'sg.group_text as GroupName',
                        'sg.group_desc as GroupDescription',
                        DB::raw("SUM(CASE WHEN s.age IS NULL OR s.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END) as '0'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END) as '5'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END) as '10'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END) as '15'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END) as '20'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END) as '25'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END) as '30'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END) as '35'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END) as '40'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END) as '45'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END) as '50'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END) as '55'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END) as '60'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END) as '65'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END) as '70'"),
                        DB::raw("SUM(CASE WHEN s.age >= 75 THEN 1 ELSE 0 END) as '75+'"),
                        DB::raw('COUNT(s.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date]);

                // เพิ่มเงื่อนไขตาม query parameter
                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($behaviour_code_start && $behaviour_code_end) {
                    $data->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $data->where('s.behaviour_code', 3);
                }

                if ($stage_code_start && $stage_code_end) {
                    $data->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                }

                if ($morpho_start && $morpho_end) {
                    $data->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }

                if ($treatment_code) {
                    $data->leftJoin('s_treatment as t', function ($join) {
                        $join->on('s.patient_id', '=', 't.patient_id')
                            ->on('s.icd10_code', '=', 't.icd10_code');
                    });
                    $data->where('t.treatment_type_id', $treatment_code);
                }

                if ($age_start && $age_end) {
                    $data->whereBetween('s.age', [$age_start, $age_end]);
                }

                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $data->where('s.recurrent', 1);
                }

                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $data->whereNotNull('p.death_date');
                }

                if ($icd10_group_id) {
                    $data->whereIn('s.icd10_group_id', $icd10_group_id);
                }

                $data = $data->groupBy('sg.group_text', 'sg.group_desc')
                    ->orderBy('sg.group_id')
                    ->get();

                break;

            case 2:
                $data = DB::table('data_cancer_summary', 's')
                    ->select(
                        'sg.group_text as GroupName',
                        'sg.group_desc as GroupDescription',
                        DB::raw("SUM(CASE WHEN s.age IS NULL OR s.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END) as '0'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END) as '5'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END) as '10'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END) as '15'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END) as '20'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END) as '25'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END) as '30'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END) as '35'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END) as '40'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END) as '45'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END) as '50'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END) as '55'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END) as '60'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END) as '65'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END) as '70'"),
                        DB::raw("SUM(CASE WHEN s.age >= 75 THEN 1 ELSE 0 END) as '75+'"),
                        DB::raw('COUNT(s.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->leftJoin('ref_provinces as v', 'v.id', '=', 'p.address_province_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date]);

                // เพิ่มเงื่อนไขตาม query parameter
                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($area_id) {
                    $data->where('v.health_region_id', $area_id);
                }

                if ($behaviour_code_start && $behaviour_code_end) {
                    $data->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $data->where('s.behaviour_code', 3);
                }

                if ($stage_code_start && $stage_code_end) {
                    $data->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                }

                if ($morpho_start && $morpho_end) {
                    $data->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }

                if ($treatment_code) {
                    $data->leftJoin('s_treatment as t', function ($join) {
                        $join->on('s.patient_id', '=', 't.patient_id')
                            ->on('s.icd10_code', '=', 't.icd10_code');
                    });
                    $data->where('t.treatment_type_id', $treatment_code);
                }

                if ($age_start && $age_end) {
                    $data->whereBetween('s.age', [$age_start, $age_end]);
                }

                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $data->where('s.recurrent', 1);
                }

                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $data->whereNotNull('p.death_date');
                }

                if ($icd10_group_id) {
                    $data->whereIn('s.icd10_group_id', $icd10_group_id);
                }

                $data = $data->groupBy('sg.group_text', 'sg.group_desc')
                    ->orderBy('sg.group_id')
                    ->get();

                break;

            case 3:
                $data = DB::table('data_cancer_summary', 's')
                    ->select(
                        'sg.group_text as GroupName',
                        'sg.group_desc as GroupDescription',
                        DB::raw("SUM(CASE WHEN s.age IS NULL OR s.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END) as '0'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END) as '5'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END) as '10'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END) as '15'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END) as '20'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END) as '25'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END) as '30'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END) as '35'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END) as '40'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END) as '45'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END) as '50'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END) as '55'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END) as '60'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END) as '65'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END) as '70'"),
                        DB::raw("SUM(CASE WHEN s.age >= 75 THEN 1 ELSE 0 END) as '75+'"),
                        DB::raw('COUNT(s.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date]);

                // เพิ่มเงื่อนไขตาม query parameter
                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($area_id) {
                    $data->where('p.address_province_id', $area_id);
                }

                if ($behaviour_code_start && $behaviour_code_end) {
                    $data->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $data->where('s.behaviour_code', 3);
                }

                if ($stage_code_start && $stage_code_end) {
                    $data->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                }

                if ($morpho_start && $morpho_end) {
                    $data->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }

                if ($treatment_code) {
                    $data->leftJoin('s_treatment as t', function ($join) {
                        $join->on('s.patient_id', '=', 't.patient_id')
                            ->on('s.icd10_code', '=', 't.icd10_code');
                    });
                    $data->where('t.treatment_type_id', $treatment_code);
                }

                if ($age_start && $age_end) {
                    $data->whereBetween('s.age', [$age_start, $age_end]);
                }

                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $data->where('s.recurrent', 1);
                }

                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $data->whereNotNull('p.death_date');
                }

                if ($icd10_group_id) {
                    $data->whereIn('s.icd10_group_id', $icd10_group_id);
                }

                $data = $data->groupBy('sg.group_text', 'sg.group_desc')
                    ->orderBy('sg.group_id')
                    ->get();

                break;

            case 4:

                $data = DB::table('data_cancer_summary', 's')
                    ->select(
                        'sg.group_text as GroupName',
                        'sg.group_desc as GroupDescription',
                        DB::raw("SUM(CASE WHEN s.age IS NULL OR s.age BETWEEN 0 AND 4 THEN 1 ELSE 0 END) as '0'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 5 AND 9 THEN 1 ELSE 0 END) as '5'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 10 AND 14 THEN 1 ELSE 0 END) as '10'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 15 AND 19 THEN 1 ELSE 0 END) as '15'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 20 AND 24 THEN 1 ELSE 0 END) as '20'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 25 AND 29 THEN 1 ELSE 0 END) as '25'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 30 AND 34 THEN 1 ELSE 0 END) as '30'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 35 AND 39 THEN 1 ELSE 0 END) as '35'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 40 AND 44 THEN 1 ELSE 0 END) as '40'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 45 AND 49 THEN 1 ELSE 0 END) as '45'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 50 AND 54 THEN 1 ELSE 0 END) as '50'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 55 AND 59 THEN 1 ELSE 0 END) as '55'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 60 AND 64 THEN 1 ELSE 0 END) as '60'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 65 AND 69 THEN 1 ELSE 0 END) as '65'"),
                        DB::raw("SUM(CASE WHEN s.age BETWEEN 70 AND 74 THEN 1 ELSE 0 END) as '70'"),
                        DB::raw("SUM(CASE WHEN s.age >= 75 THEN 1 ELSE 0 END) as '75+'"),
                        DB::raw('COUNT(s.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup as sg', 's.icd10_group_id', '=', 'sg.group_id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date]);

                // เพิ่มเงื่อนไขตาม query parameter
                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($area_id) {
                    $data->where('s.hospital_code', $area_id);
                }

                if ($behaviour_code_start && $behaviour_code_end) {
                    $data->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                } else {
                    $data->where('s.behaviour_code', 3);
                }

                if ($stage_code_start && $stage_code_end) {
                    $data->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                }

                if ($morpho_start && $morpho_end) {
                    $data->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                }

                if ($treatment_code) {
                    $data->leftJoin('s_treatment as t', function ($join) {
                        $join->on('s.patient_id', '=', 't.patient_id')
                            ->on('s.icd10_code', '=', 't.icd10_code');
                    });
                    $data->where('t.treatment_type_id', $treatment_code);
                }

                if ($age_start && $age_end) {
                    $data->whereBetween('s.age', [$age_start, $age_end]);
                }

                if (isset($recurent) && ($recurent == 'Y' || $recurent == true || $recurent == '1')) {
                    $data->where('s.recurrent', 1);
                }

                if (isset($deathcase) && ($deathcase == 'Y' || $deathcase == true || $deathcase == '1')) {
                    $data->whereNotNull('p.death_date');
                }

                if ($icd10_group_id) {
                    $data->whereIn('s.icd10_group_id', $icd10_group_id);
                }

                $data = $data->groupBy('sg.group_text', 'sg.group_desc')
                    ->orderBy('sg.group_id')
                    ->get();

                break;
        }

        return response()->json([
            'data' => $data
        ], 200);
    }

    //Excel
    public function Export_Types_of_cancer_Excel(Request $request)
    {
        return Excel::download(new Export_Types_of_cancer_Excel($request), 'Types_of_cancer.xlsx');
    }


    //ผู้ป่วยมะเร็งรายใหม่แยกตามกลุ่มประเภทการรักษา
    public function number_type_treatment(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        if (!$start_date || !$end_date) {
            return response()->json(['error' => 'กรุณาใส่วันที่เริ่มและวันที่สิ้นสุด'], 400);
        }

        $data = [];

        switch ($area_level_id) {
            case 1:

                break;
            case 2:

                break;
            case 3:

                break;
            case 4:

                $subquery = DB::table('data_cancer_summary')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                    ->select(
                        'data_cancer_summary.patient_id',
                        DB::raw("GROUP_CONCAT(DISTINCT data_cancer_summary_treatment.treatment_code ORDER BY data_cancer_summary_treatment.treatment_code SEPARATOR ' + ') AS treatment_type"),
                        'data_patient.sex_code'
                    )
                    ->whereBetween('data_cancer_summary.first_entrance_date', [$start_date, $end_date])
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('data_patient.hospital_code', '=', $area_id);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->when(isset($behaviour_code_start) && isset($behaviour_code_end), function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        return $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('data_cancer_summary.patient_id', 'data_patient.sex_code');

                $data = DB::table(DB::raw("({$subquery->toSql()}) as treatment_groups"))
                    ->mergeBindings($subquery)
                    ->select(
                        'treatment_type',
                        DB::raw('SUM(CASE WHEN sex_code = 1 THEN 1 ELSE 0 END) AS male_count'),
                        DB::raw('SUM(CASE WHEN sex_code = 2 THEN 1 ELSE 0 END) AS female_count'),
                        DB::raw('COUNT(*) AS total_count')
                    )
                    ->groupBy('treatment_type')
                    ->orderBy('treatment_type', 'ASC')
                    ->get();

                $totalMales = $data->sum('male_count');
                $totalFemales = $data->sum('female_count');
                $totalPatients = $data->sum('total_count');

                foreach ($data as $item) {
                    $item->male_percent = $totalPatients > 0 ? (($item->male_count / $totalPatients) * 100) : 0;
                    $item->female_percent = $totalPatients > 0 ? (($item->female_count / $totalPatients) * 100) : 0;
                    $item->total_percent = $totalPatients > 0 ? (($item->total_count / $totalPatients) * 100) : 0;
                }

                return response()->json([
                    'data' => $data
                ], 200);
                break;
        }
    }

    //ผู้ป่วยมะเร็งรายใหม่แยกตามประเภทการรักษา
    public function number_type_treatment2(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        if (!$start_date || !$end_date) {
            return response()->json(['error' => 'กรุณาใส่วันที่เริ่มและวันที่สิ้นสุด'], 400);
        }

        $results = null;

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary')
                    ->select(
                        DB::raw("group_t.name AS treatment_code"),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->join('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
                    ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])

                    // ->when($area_id, function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', '=', $area_id);
                    // })

                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('group_t.code', 'group_t.name')
                    ->orderBy('group_t.code', 'ASC')
                    ->get();

                break;
            case 2:
                $results = DB::table('data_cancer_summary')
                    ->select(
                        DB::raw("group_t.name AS treatment_code"),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->join('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
                    ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])

                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', '=', $area_id);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('group_t.code', 'group_t.name')
                    ->orderBy('group_t.code', 'ASC')
                    ->get();

                break;
            case 3:
                $results = DB::table('data_cancer_summary')
                    ->select(
                        DB::raw("group_t.name AS treatment_code"),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->join('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
                    ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])

                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('hos.province_id', '=', $area_id);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('group_t.code', 'group_t.name')
                    ->orderBy('group_t.code', 'ASC')
                    ->get();

                break;
            case 4:
                $results = DB::table('data_cancer_summary')
                    ->select(
                        DB::raw("group_t.name AS treatment_code"),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->join('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
                    ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])


                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('data_patient.hospital_code', '=', $area_id);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('group_t.code', 'group_t.name')
                    ->orderBy('group_t.code', 'ASC')
                    ->get();

                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }
        return response()->json([
            'data' => $results
        ], 200);
    }

    public function number_type_treatment2Pop(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        if (!$start_date || !$end_date) {
            return response()->json(['error' => 'กรุณาใส่วันที่เริ่มและวันที่สิ้นสุด'], 400);
        }

        $results = null;

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary')
                    ->select(
                        DB::raw("group_t.name AS treatment_code"),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->join('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
                    ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 'data_cancer_summary.hospital_code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])

                    // ->when($area_id, function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', '=', $area_id);
                    // })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('group_t.code', 'group_t.name')
                    ->orderBy('group_t.code', 'ASC')
                    ->get();

                break;
            case 2:
                $results = DB::table('data_cancer_summary')
                    ->select(
                        DB::raw("group_t.name AS treatment_code"),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->join('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
                    ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
                    ->join('ref_provinces as prov', 'prov.id', '=', 'data_patient.address_province_id')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])

                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('prov.health_region_id', '=', $area_id);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('group_t.code', 'group_t.name')
                    ->orderBy('group_t.code', 'ASC')
                    ->get();

                break;
            case 3:
                $results = DB::table('data_cancer_summary')
                    ->select(
                        DB::raw("group_t.name AS treatment_code"),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->join('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
                    ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])

                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('data_patient.address_province_id', '=', $area_id);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('group_t.code', 'group_t.name')
                    ->orderBy('group_t.code', 'ASC')
                    ->get();

                break;
            case 4:
                $results = DB::table('data_cancer_summary')
                    ->select(
                        DB::raw("group_t.name AS treatment_code"),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 1 THEN data_patient.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN data_patient.sex_code = 2 THEN data_patient.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(data_cancer_summary.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->join('data_patient', 'data_patient.id', '=', 'data_cancer_summary.patient_id')
                    ->join('data_cancer_summary_treatment', function ($join) {
                        $join->on('data_cancer_summary.patient_id', '=', 'data_cancer_summary_treatment.patient_id')
                            ->on('data_cancer_summary.icd10_code', '=', 'data_cancer_summary_treatment.icd10_code');
                    })
                    ->join('bd_treatment AS t', 'data_cancer_summary_treatment.treatment_type_id', '=', 't.code')
                    ->join('bd_treatment_category AS group_t', 't.code_group', '=', 'group_t.code')
                    ->whereBetween('data_cancer_summary.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(data_cancer_summary.diagnosis_date, COALESCE(data_cancer_summary.first_entrance_date, data_cancer_summary.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])

                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('data_patient.hospital_code', '=', $area_id);
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('data_patient.sex_code', '=', $gender_code);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('data_cancer_summary.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('data_cancer_summary.behaviour_code', 3);
                        }
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('data_cancer_summary.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(data_cancer_summary.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('data_cancer_summary_treatment.treatment_type_id', '=', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('data_cancer_summary.age', [$age_start, $age_end]);
                    })
                    ->when((isset($recurent)), function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('data_cancer_summary.recurrent', 1);
                        }
                    })
                    ->when((isset($deathcase)), function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('data_patient.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('data_cancer_summary.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('group_t.code', 'group_t.name')
                    ->orderBy('group_t.code', 'ASC')
                    ->get();

                break;
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }
        return response()->json([
            'data' => $results
        ], 200);
    }

    public function number_type_treatment_Excel(Request $request)
    {
        return Excel::download(new number_type_treatment_Export($request), 'Number_Type_Treatment.xlsx');
    }

    public function Number_patients_by_address(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        if (!$start_date || !$end_date) {
            return response()->json(['error' => 'กรุณาใส่วันที่เริ่มและวันที่สิ้นสุด'], 400);
        }

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary as s')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('v.province_name_th as province'),
                        DB::raw('d.district_name_th as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.permanent_address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.permanent_address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 's.hospital_code')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    // ->when($area_id, function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', '=', $area_id);
                    // })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('t.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            case 2:
                $results = DB::table('data_cancer_summary as s')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('v.province_name_th as province'),
                        DB::raw('d.district_name_th as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.permanent_address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.permanent_address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 's.hospital_code')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('hos.health_region_id', '=', $area_id);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('t.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            case 3:
                $results = DB::table('data_cancer_summary as s')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('v.province_name_th as province'),
                        DB::raw('d.district_name_th as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.permanent_address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.permanent_address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    ->leftJoin('bd_hospital as hos', 'hos.code', '=', 's.hospital_code')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('hos.province_id', '=', $area_id);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('t.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            case 4:
                $results = DB::table('data_cancer_summary as s')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('v.province_name_th as province'),
                        DB::raw('d.district_name_th as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    ->where('s.hospital_code', '=', $area_id)
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('t.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            default:
                return response()->json(['error' => 'Invalid area level'], 400);
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json([
            'data' => $results
        ], 200);
    }

    public function Number_patients_by_addressPop(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $behaviour_code_start = $request->query('behaviour_code_start');
        $behaviour_code_end = $request->query('behaviour_code_end');
        $stage_code_start = $request->query('stage_code_start');
        $stage_code_end = $request->query('stage_code_end');
        $morpho_start = $request->query('morpho_start');
        $morpho_end = $request->query('morpho_end');
        $treatment_code = $request->query('treatment_code');
        $age_start = $request->query('age_start');
        $age_end = $request->query('age_end');
        $recurent = $request->query('recurent');
        $deathcase = $request->query('deathcase');
        $icd10_group_id = array_filter($request->query('icd10_group_id', []));

        if (!$start_date || !$end_date) {
            return response()->json(['error' => 'กรุณาใส่วันที่เริ่มและวันที่สิ้นสุด'], 400);
        }

        $results = [];

        switch ($area_level_id) {
            case 1:
                $results = DB::table('data_cancer_summary as s')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('v.province_name_th as province'),
                        DB::raw('d.district_name_th as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.permanent_address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.permanent_address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    // ->leftJoin('bd_hospital as hos', 'hos.code', '=', 's.hospital_code')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    // ->when($area_id, function ($query) use ($area_id) {
                    //     return $query->where('hos.health_region_id', '=', $area_id);
                    // })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('t.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            case 2:
                $results = DB::table('data_cancer_summary as s')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('v.province_name_th as province'),
                        DB::raw('d.district_name_th as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('v.health_region_id', '=', $area_id);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('t.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            case 3:
                $results = DB::table('data_cancer_summary as s')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('v.province_name_th as province'),
                        DB::raw('d.district_name_th as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->when($area_id, function ($query) use ($area_id) {
                        return $query->where('v.health_region_id', '=', $area_id);
                    })
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('t.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            case 4:
                $results = DB::table('data_cancer_summary as s')
                    ->select(
                        'r.health_region_name as health_region',
                        DB::raw('v.province_name_th as province'),
                        DB::raw('d.district_name_th as district'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 1 THEN p.id END) AS male_count'),
                        DB::raw('0 AS male_percentage'),
                        DB::raw('COUNT(CASE WHEN p.sex_code = 2 THEN p.id END) AS female_count'),
                        DB::raw('0 AS female_percentage'),
                        DB::raw('COUNT(s.id) AS total_count'),
                        DB::raw('0 AS total_percentage')
                    )
                    ->leftJoin('data_patient as p', 's.patient_id', '=', 'p.id')
                    ->leftJoin('ref_provinces as v', 'p.address_province_id', '=', 'v.id')
                    ->leftJoin('ref_districts as d', 'p.address_district_id', '=', 'd.id')
                    ->leftJoin('ref_health_regions as r', 'v.health_region_id', '=', 'r.id')
                    ->where('s.hospital_code', '=', $area_id)
                    ->whereBetween('s.diagnosis_date', [$start_date, $end_date])
                    // ->whereRaw('GREATEST(s.diagnosis_date, COALESCE(s.first_entrance_date, s.diagnosis_date)) BETWEEN ? AND ?', [$start_date, $end_date])
                    ->where(function ($query) use ($behaviour_code_start, $behaviour_code_end) {
                        if ((isset($behaviour_code_start) && isset($behaviour_code_end))) {
                            $query->whereBetween('s.behaviour_code', [$behaviour_code_start, $behaviour_code_end]);
                        } else {
                            $query->where('s.behaviour_code', 3);
                        }
                    })
                    ->when($gender_code, function ($query) use ($gender_code) {
                        return $query->where('p.sex_code', $gender_code);
                    })
                    ->when(isset($stage_code_start) && isset($stage_code_end), function ($query) use ($stage_code_start, $stage_code_end) {
                        return $query->whereBetween('s.stage_code', [$stage_code_start, $stage_code_end]);
                    })
                    ->when($morpho_start && $morpho_end, function ($query) use ($morpho_start, $morpho_end) {
                        return $query->whereBetween(DB::raw('SUBSTRING(s.morphology_text, 1, 4)'), [$morpho_start, $morpho_end]);
                    })
                    ->when($treatment_code, function ($query) use ($treatment_code) {
                        return $query->where('t.treatment_type_id', $treatment_code);
                    })
                    ->when($age_start && $age_end, function ($query) use ($age_start, $age_end) {
                        return $query->whereBetween('s.age', [$age_start, $age_end]);
                    })
                    ->when($recurent, function ($query) use ($recurent) {
                        if ($recurent == 'Y' || $recurent == true || $recurent == '1') {
                            return $query->where('s.recurrent', 1);
                        }
                    })
                    ->when($deathcase, function ($query) use ($deathcase) {
                        if ($deathcase == 'Y' || $deathcase == true || $deathcase == '1') {
                            return $query->whereNotNull('p.death_date');
                        }
                    })
                    ->when($icd10_group_id, function ($query) use ($icd10_group_id) {
                        return $query->whereIn('s.icd10_group_id', $icd10_group_id);
                    })
                    ->groupBy('v.health_region_id', 'r.health_region_name', 'v.province_name_th', 'd.district_name_th')
                    ->orderBy('v.health_region_id', 'ASC')
                    ->orderBy('v.province_name_th', 'ASC')
                    ->orderBy('d.district_name_th', 'ASC')
                    ->get();

                break;

            default:
                return response()->json(['error' => 'Invalid area level'], 400);
        }

        $total_male = $results->sum('male_count');
        $total_female = $results->sum('female_count');
        $total_count = $results->sum('total_count');

        foreach ($results as $key => $value) {
            $results[$key]->male_percentage = $total_male > 0
                ? $value->male_count * 100.0 / $total_male
                : 0;

            $results[$key]->female_percentage = $total_female > 0
                ? $value->female_count * 100.0 / $total_female
                : 0;

            $results[$key]->total_percentage = $total_count > 0
                ? $value->total_count * 100.0 / $total_count
                : 0;
        }

        return response()->json([
            'data' => $results
        ], 200);
    }

    public function Number_patients_by_address_Excel(Request $request)
    {
        return Excel::download(new PatientsAddressExport($request), 'Number_Patients_By_Address.xlsx');
    }

    public function partient(Request $request)
    {
        $cid = $request->query('cid');

        if (!$cid) {
            return response()->json(['error' => 'กรุณากรอก cid'], 400);
        }

        $data = DB::table('data_patient as dp')
            ->distinct()
            ->select(
                DB::raw("CONCAT(bd_title.name, ' ', dp.name, ' ', dp.last_name) as full_name"),
                'bd_sex.name as sex_name',
                'dp.cid',
            )
            ->join('bd_title', 'dp.title_code', '=', 'bd_title.code')
            ->join('bd_sex', 'dp.sex_code', '=', 'bd_sex.code')
            ->where('dp.cid', $cid)
            ->first();

        return response()->json([
            'data' => $data
        ], 200);
    }

    public function app_hospital(Request $request)
    {
        $cid = $request->query('cid');

        $results = DB::table('data_patient')
            ->join('bd_hospital', 'data_patient.hospital_code', '=', 'bd_hospital.code')
            ->select(
                'bd_hospital.code as hospital_code',
                'bd_hospital.name as hospital_name',
            )
            ->where('data_patient.cid', '=', $cid)
            ->groupBy('bd_hospital.code', 'bd_hospital.name')
            ->get();

        return response()->json([
            'data' => $results
        ], 200);
    }

    public function store_suggest(Request $request)
    {
        $user = Auth::user();
        $validator = Validator::make($request->all(), [
            'patient_id'   => 'required|integer',
            'icd10_code'   => 'nullable|string|max:255',
            'description'  => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()
            ], 422);
        }

        $stingRecord = DB::table('data_suggest')
            ->where('patient_id', $request->input('patient_id'))
            ->where('icd10_code', $request->input('icd10_code'))
            ->first();

        if ($stingRecord) {
            // ถ้ามี patient_id และ icd10_code อยู่ใน row เดียวกัน ให้ทำการอัปเดต description
            $id = DB::table('data_suggest')
                ->where('id', $stingRecord->id) // ใช้ id ของ row ที่พบ
                ->update([
                    'description' => $request->input('description'),
                    'updated_at'  => now(),
                    'updated_by'  => $user->id,
                ]);
        } else {
            $id = DB::table('data_suggest')->insertGetId([
                'patient_id'  => $request->input('patient_id'),
                'icd10_code'  => $request->input('icd10_code'),
                'description' => $request->input('description'),
                'created_at'  => now(),
                'updated_at'  => now(),
                'created_by'  => $user->id,
                'updated_by'  => $user->id,
            ]);
        }

        // ส่ง Response กลับไป
        return response()->json([
            'message' => 'ข้อมูลได้ถูกเพิ่มสำเร็จแล้ว',
            'id' => $id
        ], 201);
    }

    public function store_template_suggest(Request $request)
    {
        $user = Auth::user();
        $validator = Validator::make($request->all(), [
            'icd10_code' => 'nullable|string|max:255',
            'template'   => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()
            ], 422);
        }

        $id = DB::table('bd_template_suggest')->insertGetId([
            'icd10_code'  => $request->input('icd10_code'),
            'template'    => $request->input('template'),
            'created_at'  => now(),
            'updated_at'  => now(),
            'created_by'  => $user->id,
            'updated_by'  => $user->id,
        ]);

        return response()->json([
            'message' => 'เทมเพลตได้ถูกเพิ่มสำเร็จแล้ว',
            'id'      => $id
        ], 201);
    }

    public function Dashboard_group_cancer(Request $request)
    {
        $start_date = $request->query('date_start');
        $end_date = $request->query('date_end');
        $gender_code = $request->query('gender_code');
        $area_level_id = $request->query('area_level_id');
        $area_id = $request->query('area_id');
        $finance_support_code = $request->query('finance_support_text');

        $data = [];

        switch ($area_level_id) {
            case 1:
                $data = DB::table('data_cancer_summary', 'cs')
                    ->select(
                        DB::raw("CONCAT(bd_sitegroup.group_text, ' ', bd_sitegroup.group_desc) as GroupDetails"),
                        DB::raw('COUNT(cs.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup', 'cs.icd10_group_id', '=', 'bd_sitegroup.group_id')
                    // ->leftJoin('bd_hospital as h', 'h.code', '=', 'cs.hospital_code')
                    ->whereBetween('cs.diagnosis_date', [$start_date, $end_date])
                    ->whereNot('cs.icd10_group_id', 52)
                    ->where('cs.behaviour_code', 3);

                if ($finance_support_code) {
                    $data->where('cs.finance_support_code', $finance_support_code);
                }

                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                // if ($area_id) {
                //     $data->where('h.health_region_id', $area_id);
                // }

                $data = $data->groupBy('bd_sitegroup.group_text', 'bd_sitegroup.group_desc')
                    ->orderBy('TotalPatients', 'DESC')  // เรียงลำดับจากมากไปน้อย
                    ->limit(10)
                    ->get();

                break;

            case 2:
                $data = DB::table('data_cancer_summary', 'cs')
                    ->select(
                        DB::raw("CONCAT(bd_sitegroup.group_text, ' ', bd_sitegroup.group_desc) as GroupDetails"),
                        DB::raw('COUNT(cs.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup', 'cs.icd10_group_id', '=', 'bd_sitegroup.group_id')
                    ->leftJoin('bd_hospital as h', 'h.code', '=', 'cs.hospital_code')
                    ->whereBetween('cs.diagnosis_date', [$start_date, $end_date])
                    ->whereNot('cs.icd10_group_id', 52)
                    ->where('cs.behaviour_code', 3);

                if ($finance_support_code) {
                    $data->where('cs.finance_support_code', $finance_support_code);
                }

                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($area_id) {
                    $data->where('h.health_region_id', $area_id);
                }

                $data = $data->groupBy('bd_sitegroup.group_text', 'bd_sitegroup.group_desc')
                    ->orderBy('TotalPatients', 'DESC')  // เรียงลำดับจากมากไปน้อย
                    ->limit(10)
                    ->get();

                break;

            case 3:
                $data = DB::table('data_cancer_summary', 'cs')
                    ->select(
                        DB::raw("CONCAT(bd_sitegroup.group_text, ' ', bd_sitegroup.group_desc) as GroupDetails"),
                        DB::raw('COUNT(cs.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup', 'cs.icd10_group_id', '=', 'bd_sitegroup.group_id')
                    ->leftJoin('bd_hospital as h', 'h.code', '=', 'cs.hospital_code')
                    ->whereNot('cs.icd10_group_id', 52)
                    ->whereBetween('cs.diagnosis_date', [$start_date, $end_date])
                    ->where('cs.behaviour_code', 3);

                if ($finance_support_code) {
                    $data->where('cs.finance_support_code', $finance_support_code);
                }

                if ($gender_code) {
                    $data->where('p.sex_code', $gender_code);
                }

                if ($area_id) {
                    $data->where('h.province_id', $area_id);
                }

                $data = $data->groupBy('bd_sitegroup.group_text', 'bd_sitegroup.group_desc')
                    ->orderBy('TotalPatients', 'DESC')  // เรียงลำดับจากมากไปน้อย
                    ->limit(10)
                    ->get();

                break;

            case 4:

                $data = DB::table('data_cancer_summary', 'cs')
                    ->select(
                        DB::raw("CONCAT(bd_sitegroup.group_text, ' ', bd_sitegroup.group_desc) as GroupDetails"),
                        DB::raw('COUNT(cs.patient_id) as TotalPatients')
                    )
                    ->leftJoin('data_patient as p', 'cs.patient_id', '=', 'p.id')
                    ->leftJoin('bd_sitegroup', 'cs.icd10_group_id', '=', 'bd_sitegroup.group_id')
                    ->whereNot('cs.icd10_group_id', 52)
                    ->whereBetween('cs.diagnosis_date', [$start_date, $end_date])
                    ->where('cs.behaviour_code', 3)
                    ->when($gender_code, function ($query) use ($gender_code) {
                        $query->where('p.sex_code', $gender_code);
                    })
                    ->when($area_id, function ($query) use ($area_id) {
                        $query->where('cs.hospital_code', $area_id);
                    })
                    ->when($finance_support_code, function ($query) use ($finance_support_code) {
                        $query->where('cs.finance_support_code', $finance_support_code);
                    })
                    ->groupBy('bd_sitegroup.group_text', 'bd_sitegroup.group_desc')
                    ->orderBy('TotalPatients', 'DESC')  // เรียงลำดับจากมากไปน้อย
                    ->limit(10)
                    ->get();

                break;
        }

        return response()->json([
            'data' => $data
        ], 200);
    }
}
