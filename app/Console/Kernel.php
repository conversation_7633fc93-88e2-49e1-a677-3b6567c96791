<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{

    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\NotifyReferPreAppointCron::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        // $schedule->command('app:test-job')->everyMinute();
        $schedule->command('notifyReferPreAppoint:cron')->daily();
        $schedule->command('app:clear-data-hos-test')->daily();
        // $schedule->command('app:calculate-today-treatment')->dailyAt('01:00');
        // $schedule->command('notifyReferPreAppoint:cron')->everyMinute();
        // $schedule->command('notifyReferPreAppoint:cron')->dailyAt('07:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
