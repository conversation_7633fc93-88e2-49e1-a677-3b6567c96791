<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearDataHosTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clear-data-hos-test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::table('data_cancer')
            ->whereIn('hospital_code', ['88881', '88882', '88883', '88884', '88885', '88886', '88887', '88888', '88889'])
            ->delete();

        DB::table('data_cancer_summary')
            ->whereIn('hospital_code', ['88881', '88882', '88883', '88884', '88885', '88886', '88887', '88888', '88889'])
            ->delete();

        DB::table('data_patient')
            ->whereIn('hospital_code', ['88881', '88882', '88883', '88884', '88885', '88886', '88887', '88888', '88889'])
            ->delete();

        DB::table('data_refer')
            ->whereIn('from_hospital_code', ['88881', '88882', '88883', '88884', '88885', '88886', '88887', '88888', '88889'])
            ->orWhereIn('to_hospital_code', ['88881', '88882', '88883', '88884', '88885', '88886', '88887', '88888', '88889'])
            ->delete();

        DB::table('data_refer_patient')
            ->whereIn('hospital_code', ['88881', '88882', '88883', '88884', '88885', '88886', '88887', '88888', '88889'])
            ->delete();

        DB::table('treatment_group')
            ->whereIn('hospital_code', ['88881', '88882', '88883', '88884', '88885', '88886', '88887', '88888', '88889'])
            ->delete();
    }
}
