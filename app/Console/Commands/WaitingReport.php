<?php

namespace App\Console\Commands;

use App\Http\Controllers\WaitingTimeController;
use Illuminate\Console\Command;

class WaitingReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:3waiting-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $controller = new WaitingTimeController();
        $controller->waitting();
    }
}
