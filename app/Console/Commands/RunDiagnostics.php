<?php

namespace App\Console\Commands;

use App\Http\Controllers\CancerController;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RunDiagnostics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:run-diagnostics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $cancer_summary = DB::table('data_cancer_summary')
            ->select('data_cancer_summary.*')
            ->where('diagnosis_date', 'LIKE', '0000-00-00')
            ->get();

        $this->info('Found Cancer Summary ' . count($cancer_summary) . ' records');

        foreach ($cancer_summary as $cancer) {
            $this->info('Patient ID ' . $cancer->patient_id . ' Hospital Code ' . $cancer->hospital_code . ' ICD10 Code ' . $cancer->icd10_code);

            $patientExists = DB::table('data_patient')
                ->where('id', $cancer->patient_id)
                ->where('cid', '!=', '0-0000-00000-00-0')
                ->where('cid', '!=', '9-9999-99999-99-9')
                ->exists();

            if (!$patientExists) {
                continue;
            }

            // Find Treatment
            $treatments = DB::table('data_cancer_summary_treatment')
                ->where('cancer_id', $cancer->id)
                ->get();

            $this->info('Found Treatment ' . count($treatments) . ' records for cancer ID ' . $cancer->id);

            // Add treatments as property to the cancer object
            $cancer->curr_treatments = get_object_vars($treatments);

            $cancerData = DB::table('data_cancer')
                ->where('patient_id', $cancer->patient_id)
                ->where('hospital_code', $cancer->hospital_code)
                ->where('icd10_code', $cancer->icd10_code)
                ->first();

            if (!$cancerData) {
                continue;
            }

            $cancer->diagnosis_date = $cancerData->diagnosis_date;

            $diagnosis_date = CancerController::determineDiagnosisDate1(get_object_vars($cancer));

            $this->info('Diagnosis Date ' . $diagnosis_date . ' records for cancer ID ' . $cancer->id);

            DB::table('data_cancer_summary')
                ->where('id', $cancer->id)
                ->update(['diagnosis_date' => $diagnosis_date]);
        }
    }
}
