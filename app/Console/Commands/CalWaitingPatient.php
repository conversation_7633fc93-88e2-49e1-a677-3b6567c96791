<?php

namespace App\Console\Commands;

use App\Http\Controllers\WaitingTimeController;
use Illuminate\Console\Command;

class CalWaitingPatient extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:cal-waiting-patient {cid} {hostCode}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $cid = $this->argument('cid');
        $hostCode = $this->argument('hostCode');

        $waitCon = new WaitingTimeController();
        $waitCon->calWaitingPatient($cid, $hostCode);
    }
}
