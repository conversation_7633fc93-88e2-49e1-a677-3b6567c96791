<?php

namespace App\Console\Commands;

use App\Http\Controllers\WaitingTimeController;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CalculateTreatmentCid extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:calculate-treatment-cid {cid}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->createTreamentFromOldData();
    }

    public function createTreamentFromOldData()
    {
        Log::info('Start Calculate Treatment CID');

        $cid = $this->argument('cid');

        $cids = collect([$cid]);

        Log::info('CIDs:', $cids->toArray());

        if ($cids->isEmpty()) {
            Log::info('No Data CID');
            return;
        }

        DB::beginTransaction();

        try {

            DB::table('data_treatment_group')->delete();

            foreach ($cids as $cid) {
                $this->refreshTreatmentGroupData($cid);
                $this->calculateWaitingTimesForCid($cid);
            }

            DB::commit();
            Log::info('Finish Calculate Treatment CID');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error calculating treatment: ' . $e->getMessage());
        }
    }

    protected function getCidsWithUpdatedTreatment($date)
    {
        return DB::table('data_treatment')
            ->join('data_patient', 'data_treatment.patient_id', '=', 'data_patient.id')
            ->whereDate('data_treatment.updated_at', $date)
            ->distinct()
            ->pluck('data_patient.cid');
    }

    protected function refreshTreatmentGroupData($cid)
    {
        $this->insertTreatmentData($cid);
        $this->insertBiopsyData($cid);
    }

    protected function calculateWaitingTimesForCid($cid)
    {
        (new WaitingTimeController())->waitCid($cid);

        $treatmentGroups = DB::table('treatment_group')
            ->where('key_', 'like', "%$cid%")
            ->orderBy('start_date')
            ->get();

        $previousData = null;

        foreach ($treatmentGroups as $item) {
            [$hosCode, $cid, $icd10] = explode('#', $item->key_);

            $records = $this->getPatientRecords($hosCode, $cid);

            $previousDate = $previousData ? optional($previousData)->end_date : $this->getPreviousDate($records, $item->start_date, $source);
            $item->previous_date = $item->treatment_code !== '0 Biopsy' ? $previousDate : $item->previous_date;

            $this->setWaitingFlags($item);

            $hospital = DB::table('bd_hospital')->where('code', $hosCode)->first();
            $icd10Group = DB::table('bd_sitegroup')->where('icd10_list', 'like', "%$icd10%")->first();

            DB::table('treatment_group')
                ->where('key_', $item->key_)
                ->where('treatment_code', $item->treatment_code)
                ->where('start_date', $item->start_date)
                ->where('end_date', $item->end_date)
                ->update([
                    'icd10'                     => $icd10,
                    'icd10_group'               => $icd10Group->group_id ?? null,
                    'hospital_code'             => $hospital->code ?? null,
                    'hospital_province_code'    => $hospital->province_id ?? null,
                    'hospital_area_code'        => $hospital->service_area ?? null,
                    'health_region'             => $hospital->health_region_id ?? null,
                    'previous_date'             => $item->previous_date,
                    'from'                      => $source ?? null,
                    'waiting_days'              => $item->waiting_days ?? null,
                    'waiting_flag'              => $item->waiting_flag ?? null,
                ]);

            $previousData = $item;
        }
    }

    protected function getPatientRecords($hosCode, $cid)
    {
        return [
            'hospital_summaries' => DB::table('data_cancer_summary')
                ->join('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                ->select('recurrent_date', 'diagnosis_date', 'excision_in_cut_date', 'excision_in_read_date')
                ->where('data_patient.cid', $cid)
                ->where('data_patient.hospital_code', $hosCode)
                ->get(),

            'external_summaries' => DB::table('data_cancer_summary')
                ->join('data_patient', 'data_cancer_summary.patient_id', '=', 'data_patient.id')
                ->select('recurrent_date', 'diagnosis_date', 'excision_in_cut_date', 'excision_in_read_date')
                ->where('data_patient.cid', $cid)
                ->where('data_patient.hospital_code', '<>', $hosCode)
                ->get(),

            'visits' => DB::table('data_cancer')
                ->join('data_patient', 'data_cancer.patient_id', '=', 'data_patient.id')
                ->select(
                    'data_cancer.hospital_code',
                    'recurrent_date',
                    'diagnosis_date',
                    'excision_in_cut_date',
                    'excision_in_read_date',
                    'entrance_date',
                    'data_cancer.created_at'
                )
                ->where('data_patient.cid', $cid)
                ->get(),
        ];
    }

    protected function getPreviousDate(array $records, $startDate, &$source = null)
    {
        $result = $this->findPreviousTreatmentDate($records, $startDate);

        if ($result && isset($result['date'])) {
            $source = $result['source'] ?? null;
            return $result['date']->toDateString();
        }

        return null;
    }

    protected function setWaitingFlags(&$item)
    {
        if (!$item->previous_date || !$item->start_date) return;

        $start = new Carbon($item->start_date);
        $end = new Carbon($item->previous_date);
        $days = $end->diffInDays($start);

        $limits = [
            '1 Surgery'      => 28,
            '2 Radiation'    => 42,
            '3 Chemotherapy' => 42,
            '0 Biopsy'       => 14,
        ];

        if (isset($limits[$item->treatment_code])) {
            $item->waiting_days = $days;
            $item->waiting_flag = $this->calculateWaitingFlag($days, $limits[$item->treatment_code], 180);
        }
    }

    private function calculateWaitingFlag($waiting_days, $threshold_pass, $threshold_fail)
    {
        if ($waiting_days < $threshold_pass) {
            return 'PASS';
        } elseif ($waiting_days <= $threshold_fail) {
            return 'FAIL';
        } else {
            return 'NO';
        }
    }

    protected function insertTreatmentData($cid)
    {
        DB::table('data_treatment')
            ->join('data_cancer', 'data_treatment.cancer_id', '=', 'data_cancer.id')
            ->join('data_patient', 'data_patient.id', '=', 'data_cancer.patient_id')
            ->join('bd_treatment', 'data_treatment.treatment_type_id', '=', 'bd_treatment.code')
            ->select(
                'data_patient.hospital_code',
                'data_patient.hn_no',
                'data_patient.cid',
                'data_patient.area_code',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.sex_code',
                'data_patient.birth_date',
                'data_cancer.icd10_code',
                'data_cancer.diagnosis_date',
                'data_cancer.excision_in_cut_date',
                'data_cancer.excision_in_read_date',
                'data_cancer.entrance_date',
                DB::raw("CONCAT(bd_treatment.code, ' ', bd_treatment.name) AS treatment_name"),
                'data_treatment.treatment_date'
            )
            ->whereNotNull('data_treatment.treatment_date')
            ->where('data_patient.cid', $cid)
            ->orderBy('data_treatment.id')
            ->chunk(1000, function ($results) {

                Log::info('insertTreatmentData date start', ['count' => json_encode($results)]);

                $data = [];

                foreach ($results as $value) {
                    $icd10_code = substr($value->icd10_code, 0, 3);
                    $data[] = [
                        'key_' => $value->hospital_code . '#' . $value->cid . '#' . $icd10_code,
                        'hospital_code' => $value->hospital_code,
                        'hn_no' => $value->hn_no,
                        'cid' => $value->cid,
                        'name' => $value->name,
                        'last_name' => $value->last_name,
                        'sex_code' => $value->sex_code,
                        'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                        'icd10' => $icd10_code,
                        'treatment_code' => $value->treatment_name,
                        'trt_date' => date('d/m/Y', strtotime($value->treatment_date)),
                        'trt_date_dt' => $value->treatment_date,
                        'area' => $value->area_code,
                        'diagnosis_date_dt' => $value->diagnosis_date,
                        'excision_cut_date_dt' => $value->excision_in_cut_date,
                        'excision_read_date_dt' => $value->excision_in_read_date,
                        'entrance_date_dt' => $value->entrance_date,
                    ];
                }

                DB::table('data_treatment_group')->insert($data);
            });

        DB::table('data_treatment')
            ->join('data_cancer', 'data_treatment.cancer_id', '=', 'data_cancer.id')
            ->join('data_patient', 'data_patient.id', '=', 'data_cancer.patient_id')
            ->join('bd_treatment', 'data_treatment.treatment_type_id', '=', 'bd_treatment.code')
            ->select(
                'data_patient.hospital_code',
                'data_patient.hn_no',
                'data_patient.cid',
                'data_patient.area_code',
                'data_patient.name',
                'data_patient.last_name',
                'data_patient.sex_code',
                'data_patient.birth_date',
                'data_cancer.icd10_code',
                'data_cancer.diagnosis_date',
                'data_cancer.excision_in_cut_date',
                'data_cancer.excision_in_read_date',
                'data_cancer.entrance_date',
                DB::raw("CONCAT(bd_treatment.code, ' ', bd_treatment.name) AS treatment_name"),
                'data_treatment.treatment_date_end'
            )
            ->whereNotNull('data_treatment.treatment_date_end')
            ->where('data_patient.cid', $cid)
            ->orderBy('data_treatment.id')
            ->chunk(1000, function ($results) {
                $data = [];

                Log::info('insertTreatmentData date end', ['count' => json_encode($results)]);

                foreach ($results as $value) {
                    $icd10_code = substr($value->icd10_code, 0, 3);
                    $data[] = [
                        'key_' => $value->hospital_code . '#' . $value->cid . '#' . $icd10_code,
                        'hospital_code' => $value->hospital_code,
                        'hn_no' => $value->hn_no,
                        'cid' => $value->cid,
                        'name' => $value->name,
                        'last_name' => $value->last_name,
                        'sex_code' => $value->sex_code,
                        'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                        'icd10' => $icd10_code,
                        'treatment_code' => $value->treatment_name,
                        'trt_date' => date('d/m/Y', strtotime($value->treatment_date_end)),
                        'trt_date_dt' => $value->treatment_date_end,
                        'area' => $value->area_code,
                        'diagnosis_date_dt' => $value->diagnosis_date,
                        'excision_cut_date_dt' => $value->excision_in_cut_date,
                        'excision_read_date_dt' => $value->excision_in_read_date,
                        'entrance_date_dt' => $value->entrance_date,
                    ];
                }

                DB::table('data_treatment_group')->insert($data);
            });
    }

    protected function insertBiopsyData($cid)
    {
        DB::table('data_cancer')
            ->join('data_patient', 'data_cancer.patient_id', '=', 'data_patient.id')
            ->selectRaw("
            CONCAT(data_cancer.hospital_code, '#', data_patient.cid, '#', SUBSTRING(data_cancer.icd10_code, 1, 3)) AS key_,
            data_cancer.hospital_code,
            data_patient.hn_no,
            data_patient.cid,
            data_patient.name,
            data_patient.last_name,
            data_patient.sex_code,
            data_patient.birth_date,
            SUBSTRING(data_cancer.icd10_code, 1, 3) AS icd10,
            data_cancer.excision_in_read_date,
            data_cancer.excision_in_cut_date,
            data_patient.area_code AS area,
            data_cancer.diagnosis_date,
            data_cancer.entrance_date")
            ->whereNotNull('data_cancer.diagnosis_date')
            ->whereNotNull('data_cancer.excision_in_cut_date')
            ->whereNotNull('data_cancer.excision_in_read_date')
            ->whereNotNull('data_cancer.icd10_code')
            ->whereNot('data_patient.cid', '0-0000-00000-00-0')
            ->whereNot('data_patient.cid', '9-9999-99999-99-9')
            ->whereNot('data_cancer.source_id', 3)
            ->where('data_patient.cid', $cid)
            // ->groupBy(
            //     'data_cancer.hospital_code',
            //     'data_patient.cid',
            //     'data_cancer.icd10_code',
            //     'data_patient.hn_no',
            //     'data_patient.name',
            //     'data_patient.last_name',
            //     'data_patient.sex_code',
            //     'data_patient.birth_date',
            //     'data_patient.area_code'
            // )
            ->orderBy('key_')
            ->chunk(500, function ($results) {
                $data = [];

                foreach ($results as $value) {
                    if ($value->excision_in_cut_date) {
                        $data[] = [
                            'key_' => $value->key_,
                            'hospital_code' => $value->hospital_code,
                            'hn_no' => $value->hn_no,
                            'cid' => $value->cid,
                            'name' => $value->name,
                            'last_name' => $value->last_name,
                            'sex_code' => $value->sex_code,
                            'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                            'icd10' => $value->icd10,
                            'treatment_code' => 'CUT',
                            'trt_date' => date('d/m/Y', strtotime($value->excision_in_cut_date)),
                            'trt_date_dt' => $value->excision_in_cut_date,
                            'trt_date_end' => null,
                            'trt_date_end_dt' => null,
                            'area' => $value->area,
                            'diagnosis_date_dt' => null,
                            'excision_cut_date_dt' => null,
                            'excision_read_date_dt' => null,
                            'entrance_date_dt' => null,
                        ];
                    }

                    if ($value->excision_in_read_date) {
                        $data[] = [
                            'key_' => $value->key_,
                            'hospital_code' => $value->hospital_code,
                            'hn_no' => $value->hn_no,
                            'cid' => $value->cid,
                            'name' => $value->name,
                            'last_name' => $value->last_name,
                            'sex_code' => $value->sex_code,
                            'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                            'icd10' => $value->icd10,
                            'treatment_code' => 'READ',
                            'trt_date' => date('d/m/Y', strtotime($value->excision_in_read_date)),
                            'trt_date_dt' => $value->excision_in_read_date,
                            'trt_date_end' => null,
                            'trt_date_end_dt' => null,
                            'area' => $value->area,
                            'diagnosis_date_dt' => null,
                            'excision_cut_date_dt' => null,
                            'excision_read_date_dt' => null,
                            'entrance_date_dt' => null,
                        ];
                    }

                    if ($value->diagnosis_date) {
                        $data[] = [
                            'key_' => $value->key_,
                            'hospital_code' => $value->hospital_code,
                            'hn_no' => $value->hn_no,
                            'cid' => $value->cid,
                            'name' => $value->name,
                            'last_name' => $value->last_name,
                            'sex_code' => $value->sex_code,
                            'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                            'icd10' => $value->icd10,
                            'treatment_code' => 'DIAG',
                            'trt_date' => date('d/m/Y', strtotime($value->diagnosis_date)),
                            'trt_date_dt' => $value->diagnosis_date,
                            'trt_date_end' => null,
                            'trt_date_end_dt' => null,
                            'area' => $value->area,
                            'diagnosis_date_dt' => null,
                            'excision_cut_date_dt' => null,
                            'excision_read_date_dt' => null,
                            'entrance_date_dt' => null,
                        ];
                    }

                    if ($value->entrance_date) {
                        $data[] = [
                            'key_' => $value->key_,
                            'hospital_code' => $value->hospital_code,
                            'hn_no' => $value->hn_no,
                            'cid' => $value->cid,
                            'name' => $value->name,
                            'last_name' => $value->last_name,
                            'sex_code' => $value->sex_code,
                            'birth_date' => date('d/m/Y', strtotime($value->birth_date)),
                            'icd10' => $value->icd10,
                            'treatment_code' => 'VISIT',
                            'trt_date' => date('d/m/Y', strtotime($value->entrance_date)),
                            'trt_date_dt' => $value->entrance_date,
                            'trt_date_end' => null,
                            'trt_date_end_dt' => null,
                            'area' => $value->area,
                            'diagnosis_date_dt' => null,
                            'excision_cut_date_dt' => null,
                            'excision_read_date_dt' => null,
                            'entrance_date_dt' => null,
                        ];
                    }
                }

                $uniqueData = collect($data)->unique()->values()->all();

                DB::table('data_treatment_group')->insert($uniqueData);
            });
    }

    protected function findPreviousTreatmentDate($patientRecords, $treatmentDate)
    {
        // กำหนดค่าเริ่มต้น
        $previousTreatmentDate = null;
        $source = null; // เพิ่มตัวแปรเพื่อเก็บแหล่งที่มาของข้อมูล
        $maxDays = 180;

        // แปลงวันที่เข้ารับการรักษาเป็น Carbon
        $treatmentDate = Carbon::parse($treatmentDate);

        // ค้นหาใบสรุปในโรงพยาบาลของเรา
        foreach ($patientRecords['hospital_summaries'] as $record) {
            if (isset($record->recurrent_date) && $treatmentDate->diffInDays(Carbon::parse($record->recurrent_date)) <= $maxDays) {
                $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->recurrent_date));
                $source = 'hospital_summaries (Recurrent)';
            } else if (isset($record->diagnosis_date) && $treatmentDate->diffInDays(Carbon::parse($record->diagnosis_date)) <= $maxDays) {
                $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->diagnosis_date));
                $source = 'hospital_summaries (Diagnosis)';
            } else if (isset($record->excision_in_cut_date) && $treatmentDate->diffInDays(Carbon::parse($record->excision_in_cut_date)) <= $maxDays) {
                $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->excision_in_cut_date));
                $source = 'hospital_summaries (Excision in Cut)';
            } else if (isset($record->excision_in_read_date) && $treatmentDate->diffInDays(Carbon::parse($record->excision_in_read_date)) <= $maxDays) {
                $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->excision_in_read_date));
                $source = 'hospital_summaries (Excision in Read)';
            }
        }

        // ถ้าไม่พบในโรงพยาบาลของเรา ให้ค้นหาในโรงพยาบาลอื่น
        if ($previousTreatmentDate === null) {
            foreach ($patientRecords['external_summaries'] as $record) {
                if (isset($record->recurrent_date) && $treatmentDate->diffInDays(Carbon::parse($record->recurrent_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->recurrent_date));
                    $source = 'external_summaries (Recurrent)';
                }
                if (isset($record->diagnosis_date) && $treatmentDate->diffInDays(Carbon::parse($record->diagnosis_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->diagnosis_date));
                    $source = 'external_summaries (Diagnosis)';
                }
                if (isset($record->excision_in_cut_date) && $treatmentDate->diffInDays(Carbon::parse($record->excision_in_cut_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->excision_in_cut_date));
                    $source = 'external_summaries (Excision in Cut)';
                }
                if (isset($record->excision_in_read_date) && $treatmentDate->diffInDays(Carbon::parse($record->excision_in_read_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($record->excision_in_read_date));
                    $source = 'external_summaries (Excision in Read)';
                }
            }
        }

        // ถ้าไม่พบใบสรุปเลย ให้ค้นหาใน Visit
        if ($previousTreatmentDate === null) {
            foreach ($patientRecords['visits'] as $visit) {
                if (isset($visit->recurrent_date) && $treatmentDate->diffInDays(Carbon::parse($visit->recurrent_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($visit->recurrent_date));
                    $source = 'visits (Recurrent)';
                }
                if (isset($visit->diagnosis_date) && $treatmentDate->diffInDays(Carbon::parse($visit->diagnosis_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($visit->diagnosis_date));
                    $source = 'visits (Diagnosis)';
                }
                if (isset($visit->excision_in_cut_date) && $treatmentDate->diffInDays(Carbon::parse($visit->excision_in_cut_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($visit->excision_in_cut_date));
                    $source = 'visits (Excision in Cut)';
                }
                if (isset($visit->excision_in_read_date) && $treatmentDate->diffInDays(Carbon::parse($visit->excision_in_read_date)) <= $maxDays) {
                    $previousTreatmentDate = $this->getLatestDate($previousTreatmentDate, Carbon::parse($visit->excision_in_read_date));
                    $source = 'visits (Excision in Read)';
                }
            }
        }

        // ถ้ายังไม่พบ ให้ใช้วันที่มาติดต่อครั้งแรก
        if ($previousTreatmentDate === null) {
            $lasted = null;
            foreach ($patientRecords['visits'] as $visit) {
                if (isset($visit->entrance_date)) {
                    if ($lasted === null) {
                        $lasted = Carbon::parse($visit->entrance_date);
                    } else {
                        $lasted = $lasted->gt(Carbon::parse($visit->entrance_date))
                            ? $lasted
                            : Carbon::parse($visit->entrance_date);
                    }
                }
            }
            $previousTreatmentDate = $lasted;
            $source = 'visits (First Visit)';
        }

        // คืนค่าทั้งวันที่และแหล่งที่มา
        return [
            'date' => $previousTreatmentDate,
            'source' => $source
        ];
    }

    protected function getLatestDate($currentDate, $newDate)
    {
        if ($currentDate === null || $newDate->gt($currentDate)) {
            return $newDate;
        }
        return $currentDate;
    }
}
