<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GroupIcd extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:group-icd';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $data_cancer_summary = DB::table('data_cancer_summary')
        ->whereNotNull('icd10_code')
        ->whereNull('icd10_group_id')
        ->orderBy('id')
        ->get();

        $this->info('ICD count ' . count($data_cancer_summary) . ' found.');

        foreach ($data_cancer_summary as $cancer) {
            $icd10_code = substr($cancer->icd10_code, 0, 3);

            $siteGroup = DB::table('bd_sitegroup')->where('icd10_list', 'LIKE', '%' . trim($icd10_code) . '%')->first();

            if ($siteGroup) {
                DB::table('data_cancer_summary')
                    ->where('id', $cancer->id)
                    ->update([
                        'icd10_group_id' => $siteGroup->group_id,
                    ]);
            }
        }

        $this->info('ICD codes grouped successfully.');
    }
}
