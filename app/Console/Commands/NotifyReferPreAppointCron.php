<?php

namespace App\Console\Commands;

use App\Http\Controllers\Controller;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class NotifyReferPreAppointCron extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifyReferPreAppoint:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::beginTransaction();
        try {

            $date2 = date("Y-m-d", strtotime("+2 day"));


            $Refer = DB::table('data_refer')
                ->where('appoint_date', $date2)
                ->get();

            for ($i = 0; $i < count($Refer); $i++) {

                $refer = $Refer[$i];

                //get patient
                $patient = DB::table('data_patient')->find($refer->patient_id);
                if ($patient) {
                    //send notify
                    $title = $refer->message;
                    $body = $refer->message;
                    $target_id = $refer->id;
                    $type = 'refer';
                    $NotifyUser = [$patient->cid];

                    $controller =  new Controller();
                    $controller->addNotifyLog($title, $body, $target_id, $type, $NotifyUser);
                }
                //
            }
            DB::commit();
        } catch (Exception $e) {
            //
            DB::rollback();
        }
    }
}
