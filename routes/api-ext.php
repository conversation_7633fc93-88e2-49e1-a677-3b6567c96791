<?php

use App\Http\Controllers\PatientController;
use App\Http\Controllers\CancerController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiController;

Route::get('/patient', [PatientController::class, 'getFromApi']);
Route::get('/patient/{cid}', [PatientController::class, 'getCidFromApi']);
Route::post('/patient', [PatientController::class, 'storeFromApi']);
Route::post('/cancer', [CancerController::class, 'storeCancerFromApi']);
Route::post('/treatment', [CancerController::class, 'storeTreatmentFromApi']);


Route::get('/ref/area', [ApiController::class, 'getArea']);
Route::get('/ref/beh', [ApiController::class, 'getBehaver']);
Route::get('/ref/death_cause', [ApiController::class, 'getDeathCause']);
Route::get('/ref/diag', [ApiController::class, 'getDiagnosis']);
Route::get('ref/finance_support', [ApiController::class, 'getFinanceSupport']);
Route::get('/ref/icd10', [ApiController::class, 'getIcd10']);
Route::get('/ref/topo', [ApiController::class, 'getTopo']);
Route::get('/ref/sex', [ApiController::class, 'getSex']);
Route::get('/ref/title', [ApiController::class, 'getTitle']);
Route::get('ref/treatment', [ApiController::class, 'getTreatment']);
Route::get('/ref/nationality', [ApiController::class, 'getNational']);
Route::get('/ref/grade', [ApiController::class, 'getGrade']);
Route::get('/ref/stage', [ApiController::class, 'getStage']);
