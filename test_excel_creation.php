<?php

require_once 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;

try {
    echo "Testing Excel file creation...\n";
    
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // ตั้งชื่อ sheet
    $sheet->setTitle('Test Sheet');
    
    // สร้าง header
    $headers = [
        'A1' => 'แถวที่',
        'B1' => 'ข้อผิดพลาด',
        'C1' => 'รหัสโรงพยาบาล',
        'D1' => 'HN',
        'E1' => 'ชื่อ',
    ];
    
    foreach ($headers as $cell => $value) {
        $sheet->setCellValue($cell, $value);
        $sheet->getStyle($cell)->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFFF0000');
        $sheet->getStyle($cell)->getFont()->getColor()->setARGB('FFFFFFFF');
        $sheet->getStyle($cell)->getFont()->setBold(true);
    }
    
    // เพิ่มข้อมูลทดสอบ
    $sheet->setCellValue('A2', '3');
    $sheet->setCellValue('B2', 'เลขบัตรประชาชนไม่ถูกต้อง');
    $sheet->setCellValue('C2', '12345');
    $sheet->setCellValue('D2', 'HN001');
    $sheet->setCellValue('E2', 'ทดสอบ');
    
    // ทำให้แถว error เป็นสีแดงอ่อน
    $sheet->getStyle('A2:E2')->getFill()
        ->setFillType(Fill::FILL_SOLID)
        ->getStartColor()->setARGB('FFFFCCCC');
    
    // ปรับขนาดคอลัมน์
    foreach (range('A', 'E') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // สร้างโฟลเดอร์
    $path = 'storage/app/public/imports/errors/' . date('Ymd');
    if (!file_exists($path)) {
        echo "Creating directory: $path\n";
        $created = mkdir($path, 0755, true);
        echo "Directory creation: " . ($created ? 'success' : 'failed') . "\n";
    }
    
    // บันทึกไฟล์
    $filename = 'test_excel_' . date('YmdHis') . '.xlsx';
    $fullPath = $path . '/' . $filename;
    
    echo "Saving file to: $fullPath\n";
    
    $writer = new Xlsx($spreadsheet);
    $writer->save($fullPath);
    
    echo "Excel file created successfully!\n";
    echo "File size: " . filesize($fullPath) . " bytes\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
