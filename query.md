## SQL Query ตาราง bd_hospital

- เพิ่มคอลัมน์ dropdown ในตาราง bd_hospital

```sql
ALTER TABLE `bd_hospital`
ADD COLUMN `dropdown` int NULL DEFAULT 0 AFTER `area_code`;
```

- เปลี่ยนค่า dropdown เป็น 1 หรือ 0 โดยอ้างอิงจาก much_keyin

```sql
UPDATE `bd_hospital` SET `dropdown` = `much_keyin`;
```

---

### ตาราง data_refer

- เพิ่มคอลัมน์ existed ในตาราง data_refer

```sql
ALTER TABLE `data_refer`
ADD COLUMN `existed` int NULL DEFAULT 0 AFTER `file`;
```

- เปลี่ยนค่า existed ในตาราง data_refer ให้ถูกต้องโดยเช็คจาก data_patient

```sql
UPDATE data_refer R
LEFT JOIN data_refer_patient P ON R.id = P.refer_id
LEFT JOIN data_patient DP ON DP.hospital_code = R.to_hospital_code
                          AND DP.cid = P.cid
SET R.existed = CASE
    WHEN DP.hospital_code IS NOT NULL AND DP.cid IS NOT NULL THEN 1
    ELSE 0
END;
```

- เพิ่ม checked_api ในตาราง data_patient และ data_refer_patient

```sql
ALTER TABLE `data_patient`
ADD COLUMN `checked_api` int NULL DEFAULT 0 AFTER `source_id`;

ALTER TABLE `data_refer_patient`
ADD COLUMN `checked_api` int NULL DEFAULT 0 AFTER `source_id`;

ALTER TABLE `data_patient` 
DROP COLUMN `checked_api`,
CHANGE COLUMN `checkes_api` `checked_api` int NULL DEFAULT NULL AFTER `source_id`;

ALTER TABLE `data_refer_patient` 
DROP COLUMN `checked_api`,
CHANGE COLUMN `checkes_api` `checked_api` int NULL DEFAULT NULL AFTER `source_id`;
```