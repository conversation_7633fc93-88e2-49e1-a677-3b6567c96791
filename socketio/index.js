const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
var cors = require('cors')

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
    cors: { origin: '*' }
});

// Middleware to parse JSON
app.use(express.json());

app.use(cors());

// RESTful API endpoint to post a new message
app.post('/api/messages', (req, res) => {

    console.log(req.body);

    const referId = req.body.refer_id;
    const to_hospital_code = req.body.to_hospital_code;

    const text = JSON.stringify(req.body);

    // if (message) {
    io.emit('refer-' + referId, text);  // Broadcast to all clients

    io.emit('noti' + to_hospital_code, text);  // Broadcast to all clients

    res.status(201).json({ message: 'Send message success' });
    // } else {
    //     res.status(400).json({ error: 'Message content required' });
    // }
});

app.post('/api/notification', (req, res) => {

    console.log(req.body);

    const eventName = req.body?.eventName;

    if (!eventName) {
        return res.status(400).json({ error: 'Event name required' });
    }

    const text = JSON.stringify(req.body);

    // if (message) {
    io.emit(eventName, text);  // Broadcast to all clients
    res.status(201).json({ message: 'Send message success' });
    // } else {
    //     res.status(400).json({ error: 'Message content required' });
    // }
});

app.post('/api/refer', (req, res) => {

    console.log(req.body);

    const event_name = req.body.event_name;

    const text = JSON.stringify(req.body);

    io.emit(event_name, text);

    res.status(201).json({ message: 'Send refer noti success' });
});

// Socket.io connection handler
io.on('connection', (socket) => {
    console.log('a user connected');
    socket.on('disconnect', () => {
        console.log('user disconnected');
    });
});

// Start the server
const PORT = process.env.PORT || 50001;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
